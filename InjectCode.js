const CommentRegStr =
    "\\/api\\/.+\\/.+\\/[0-9a-f]{24}\\/comment(\\/[0-9a-f]{24})*";
const CommentReg = new RegExp(CommentRegStr);
const GetCommentsRegStr = "/api/.+/.+/([0-9a-f]{24})/comments*";
const GetCommentsReg = new RegExp(GetCommentsRegStr);

let codeStatus = {
    listenClick: false,
};
// 设置权限  没有不能发送
let SET_POWER = false;
// 查看权限  没有不能查看
let LOOK_POWER = false;

// id : 选中状态  是否私密状态同聊天内容一样采用 localStorage 保存 同步
const workItemStatus = JSON.parse(
    window.localStorage.getItem("workItemStatus") || "{}"
);

function localSync() {
    window.localStorage.setItem(
        "workItemStatus",
        JSON.stringify(workItemStatus)
    );
}

let currentWorkItemId = "";

function setCheckBox(checked) {
    console.log("change checkbox checked status");
    if (document.getElementById("InjectCommentButton")) {
        console.log("change checkbox checked status!");
        document.getElementById("InjectCommentButton").checked = !!checked;
        window._is_private_comment = checked;
        workItemStatus[currentWorkItemId] = checked;
        localSync();
    }
}

const observer = new MutationObserver((mutations) => {
    if (SET_POWER) {
        mutations.forEach((mutation) => {
            const oldValue = mutation.oldValue;
            const newValue = mutation.target.textContent;
            if (oldValue !== newValue) {
                try {
                    const sendButton = document.querySelectorAll(
                        ".toolbar-send.btn-pair.ng-star-inserted"
                    );
                    if (sendButton[0]) {
                        const alreadyInsert = document.getElementById(
                            "InjectCommentButton"
                        );
                        if (!alreadyInsert) {
                            const label = document.createElement("label");
                            label.id = "InjectCommentButtonLabel";
                            const checkBox = document.createElement("input");
                            checkBox.id = "InjectCommentButton";
                            checkBox.type = "checkbox";
                            // console.log("切换任务ID：", currentWorkItemId);
                            window._is_private_comment =
                                workItemStatus[currentWorkItemId];
                            checkBox.checked = window._is_private_comment;
                            // console.log(workItemStatus,workItemStatus[currentWorkItemId],checkBox.checked)
                            checkBox.addEventListener("change", (et) => {
                                setCheckBox(et.target.checked);
                            });

                            label.appendChild(checkBox);
                            const span = document.createElement("span");
                            span.innerText = "私密评论";
                            label.appendChild(span);
                            label.style =
                                "margin: 0px 5px 0 10px;display: flex;";
                            sendButton[0].parentElement.insertBefore(
                                label,
                                sendButton[0]
                            );
                            console.log("INJECT DOM", label);
                        }
                    }
                } catch (error) {
                    debugger;
                    console.error(error);
                }
            }
        });
    }
});

observer.observe(document.body, {
    characterDataOldValue: true,
    subtree: true,
    childList: true,
    characterData: true,
});

const MyXMLHttpRequest = window.XMLHttpRequest;
class InterceptXML extends window.XMLHttpRequest {
    constructor(...p) {
        super(...p);
    }

    addEventListener(t, fn) {
        super.addEventListener(t, fn);
    }

    get InjectCommentButton() {
        return document.getElementById("InjectCommentButton");
    }

    _statusText = "";

    get statusText() {
        return this._statusText || super.statusText;
    }

    set statusText(val) {
        this._statusText = val;
    }

    _status = "";

    get status() {
        return this._status || super.status;
    }

    set status(val) {
        this._status = val;
    }

    _response = "";

    get response() {
        return this._response || super.response;
    }

    set response(val) {
        this._response = val;
    }

    _responseText = "";

    get responseText() {
        return this._responseText || super.responseText;
    }

    set responseText(val) {
        this._responseText = val;
    }

    diffTag() {
        for (const comment of this.currCommentList) {
            const container = document.querySelector(
                "div.comment-" + comment._id
            );
            if (!container) return;
            const injectDom = container
                .querySelector(".thy-comment-body-author")
                .querySelector("span[inject-comment-status]");
            if (injectDom) {
                const is_private = injectDom.innerText == "私密" ? true : false;
                if (comment.is_private !== is_private) {
                    injectDom.innerText = comment.is_private ? "私密" : "公开";
                    console.log(comment);
                }
            } else {
                const span = document.createElement("span");
                span.innerText = comment.is_private ? "私密" : "公开";
                span.style.color = "rgb(181 181 181)";
                span.style.background = "rgb(249 249 249 / 70%)";
                span.style.fontSize = "12px";
                span.style.padding = "0 5px";
                span.setAttribute("inject-comment-status", comment._id);

                container
                    .querySelector(".thy-comment-body-author")
                    ?.appendChild(span);
            }
        }
    }

    /**
     * 完全覆盖 将原请求转移到另一个
     */
    cover(method, url) {
        const xml = new MyXMLHttpRequest();
        xml.open(method, url, true);
        this.addEventListener = (type, callback) => {
            if (type == "loadend") {
                this.getAllResponseHeaders = () => xml.getAllResponseHeaders();

                xml.addEventListener(type, () => {
                    this.statusText = xml.statusText;
                    this.status = xml.status;
                    this.response = xml.response;
                    this.responseText = xml.responseText;
                    console.log("拦截：", this.response);
                    this.response = "{拦截：123}";
                    callback();
                });
            } else xml.addEventListener(type, callback);
        };

        this.setRequestHeader = (...r) => xml.setRequestHeader(...r);
        this.send = () => xml.send();
    }

    afterRender(call) {
        if (window.requestIdleCallback) {
            requestIdleCallback(() => {
                call();
            });
        } else if (window.requestAnimationFrame) {
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    call();
                });
            });
        } else {
            setTimeout(() => {
                call();
            }, 32);
        }
    }

    /** 保存发送请求时的状态，防止后续使用window.is_private值已经改变 */
    is_private_temp = false;

    open(method, url) {
        if (method === "GET") {
            return this.cover(method, url);
        }
    }

    handleSendNewComment() {
        super.addEventListener("loadend", () => {
            const res = JSON.parse(super.response || super.responseText);
            if (res && res.data && res.data.value) {
                this.currCommentList = [
                    ...this.currCommentList,
                    {
                        ...res.data.value,
                        is_private: this.is_private_temp,
                    },
                ];
            }
        });
    }

    handleEditComment() {
        super.addEventListener("loadend", () => {
            const res = JSON.parse(super.response || super.responseText);
            if (res && res.data && res.data.value) {
                this.currCommentList = this.currCommentList.map((item) => {
                    if (item._id === res.data.value._id) {
                        return {
                            ...res.data.value,
                            is_private: this.is_private_temp,
                        };
                    }
                    return item;
                });
            }
        });
    }

    handleDeleteComment() {}

    handleDom() {
        const { data, code } =
            JSON.parse(this.responseText || "{}") ||
            JSON.parse(this.response || "{}");
        if (code !== 200 || !data) return;
        const { set_power, look_power } = data;
        if (set_power == undefined || look_power == undefined) return;
        console.log(data, this.InjectCommentButton);

        SET_POWER = set_power;
        LOOK_POWER = look_power;
        // if (!data.set_power) return;
        // if (!data.look_power) return;
        if (!SET_POWER && this.InjectCommentButton) {
            console.log("REMOVE ");
            document
                .querySelector("#InjectCommentButtonLabel")
                .parentNode.removeChild(
                    document.querySelector("#InjectCommentButtonLabel")
                );
        }
        if (LOOK_POWER) {
            this.currCommentList = data.value;
            this.listenEditComment();
        }
    }

    ElementTagLevelEnum = {
        path: 4,
        g: 3,
        svg: 2,
        "THY-ICON": 1,
        SPAN: 1,
        A: 0,
    };

    needCheckElementTags = Object.keys(this.ElementTagLevelEnum);

    listenEditComment() {
        if (codeStatus.listenClick) return;
        codeStatus.listenClick = true;
        document.addEventListener("mousedown", (e) => {
            const { tagName } = e.target;
            const needCheck = this.needCheckElementTags.includes(tagName);

            if (!needCheck) return;
            // 便利查找a标签 进而确认是否是编辑按钮
            const count = this.ElementTagLevelEnum[tagName];
            let dom = e.target;
            for (let i = 0; i < count; i++) {
                if (dom == document.body) break;
                dom = dom.parentElement;
            }
            console.log(dom);
            if (dom.innerText == "编辑" && dom.href == "javascript:;") {
                // 找到当前活跃的工具栏
                const optionBar = document.querySelector(
                    ".thy-comment-actions.comment-item-actions.ng-star-inserted.active"
                );
                if (!optionBar) return;
                // 查找工具栏的父级 找到单个评论的最外层dom
                const commonWrap =
                    optionBar.parentElement?.parentElement?.parentElement;
                if (!commonWrap) return;
                const comment = this.findCommentByClassList(commonWrap);
                if (comment) {
                    setCheckBox(comment.is_private);
                }
            } else if (dom.getAttribute("thytooltip") === "回复") {
                const commonWrap =
                    dom.parentElement?.parentElement?.parentElement
                        ?.parentElement?.parentElement;
                if (!commonWrap) return;
                const comment = this.findCommentByClassList(commonWrap);
                console.log("回复：", comment);
                if (comment) {
                    setCheckBox(comment.is_private);
                }
            }
        });
    }

    findCommentByClassList(dom) {
        // dom的className 有一个comment-id的 可提取绑定的id
        let commentId = "";
        [...dom.classList].find((className) => {
            const res = className.match(/^comment-([0-9a-z]{24})$/);
            console.log("REG:", res);
            if (res && res[1]) {
                commentId = res[1];
                return true;
            }
        });
        // 找到id对应的评论
        const comment = this.currCommentList.find(
            (comment) => comment._id == commentId
        );
        return comment;
    }
}

window.XMLHttpRequest = InterceptXML;

// TODO : 优化 可将监听dom更改创建复选框移动到发送完获取评论请求后添加
//  监听新增评论  设置对应公开状态 填充本地数组以支持编辑时回显公开状态（复选框勾选状态）
//  编辑评论 如果从公开变到私密 需要更新标签
// 每次更新评论内容 可以写一个统一处理函数 类似渲染功能 diff 计算 需要更新的

/**
 * 两种方式 拦截请求和响应
 * cover 方法 完全覆盖原先请求 创建一个新请求 可获取返回值 并可对其进行修改 直接干扰渲染
 * open  方法 不覆盖原先请求 对发送的请求简单干扰 可修改上传的数据 通过监听onload 也可在返回后进行dom操作 但无法修改响应的返回值
 */
