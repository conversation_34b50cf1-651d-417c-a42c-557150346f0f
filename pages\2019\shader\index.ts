/*
 * @Author: hongbin
 * @Date: 2025-03-24 10:38:33
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-19 15:18:32
 * @Description:
 */
import rotatePositionVT from "./rotatePosition.vt.glsl";
import rotatePositionFM from "./rotatePosition.fm.glsl";
import flowerBG_VT from "./flowerBG.vt.glsl";
import flowerBG_FM from "./flowerBG.fm.glsl";
import bgTile_VT from "./bgTile.vt.glsl";
import bgTile_FM from "./bgTile.fm.glsl";
import base_VT from "./base/vt.glsl";
import base_FM from "./base/fm.glsl";

const shader = {
    rotatePosition: {
        vt: rotatePositionVT,
        fm: rotatePositionFM,
    },
    flowerBG: {
        vt: flowerBG_VT,
        fm: flowerBG_FM,
    },
    bgTile: {
        vt: bgTile_VT,
        fm: bgTile_FM,
    },
    base: {
        vt: base_VT,
        fm: base_FM,
    },
};

export default shader;
