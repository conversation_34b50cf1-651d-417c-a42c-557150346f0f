/*
 * @Author: hongbin
 * @Date: 2023-10-16 13:49:57
 * @LastEditors: hongbin
 * @LastEditTime: 2025-03-27 16:31:33
 * @Description:
 */
import { css } from "styled-components";
import Layout from "@/src/components/Three/Layout";
// import { Main } from "@/src/components/Three/Main";
import { Main } from "@/src/components/dispose";
interface IProps {}

const Index: React.FC<IProps> = () => {
    return (
        <Layout
            main={Main}
            seoTitle="Hello World"
            style={css`
                width: 100%;
                height: 100%;
                z-index: 1;
                position: absolute;
                top: 0;
            `}
        />
    );
};

export default Index;
