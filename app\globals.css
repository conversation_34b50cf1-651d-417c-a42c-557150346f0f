/*
 * @Author: hongbin
 * @Date: 1985-10-26 16:15:00
 * @LastEditors: hongbin
 * @LastEditTime: 2023-01-15 21:38:58
 * @Description: 服务端组件的全局样式 和 GlobalStyled一致
 */

html,
body {
    max-width: 100vw;
    overflow-x: hidden;
    color: var(--text-color);
}

a {
    color: inherit;
    text-decoration: none;
}

body,
head,
div,
section,
p,
h1,
h2,
h3,
h4,
h5,
h6,
code,
span,
head,
footer,
nav,
main {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    user-select: none;
}

:root {
    --primary-color: #a69176;
    --scrollbar-color: #fff;
    --scrollbar-bg-color: #32005f;
    --nav-height: 15vh;
    --text-color: #000;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-color);
    border-radius: 4px;
}
::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: var(--scrollbar-bg-color);
}

.dg.ac {
    z-index: 10000 !important;
}

@media (prefers-color-scheme: dark) {
    html {
        color-scheme: dark;
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #fff;
    }
}
