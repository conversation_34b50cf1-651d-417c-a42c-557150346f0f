import { css } from "styled-components";
import Layout from "@/src/components/Three/Layout";
import { Main } from "./main";
import { useEffect } from "react";

interface IProps {}

const Index: React.FC<IProps> = () => {
    useEffect(() => {}, []);

    return (
        <>
            <Layout
                main={Main}
                seoTitle="🌹"
                style={css`
                    width: 100vw;
                    height: 100vh;
                    position: fixed;
                    z-index: 1;
                    border: none;
                `}
            />
        </>
    );
};

export default Index;
