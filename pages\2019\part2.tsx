/*
 * @Author: hongbin
 * @Date: 2025-06-17 12:40:27
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-20 18:34:20
 * @Description: 飞鸟
 */
import { css } from "styled-components";
import Layout from "@/src/components/Three/Layout";
import { Main } from "./main2";
import { useEffect } from "react";
import LoaderRing from "./LoaderRing";

interface IProps {}

const Index: React.FC<IProps> = () => {
    useEffect(() => {}, []);

    return (
        <>
            <Layout
                main={Main}
                seoTitle="🐦"
                style={css`
                    width: 100vw;
                    height: 100vh;
                    position: fixed;
                    z-index: 1;
                    border: none;
                `}
            />
            <LoaderRing/>
        </>
    );
};

export default Index;
