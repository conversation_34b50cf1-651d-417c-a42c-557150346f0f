/*
 * @Author: hongbin
 * @Date: 2023-10-20 17:21:46
 * @LastEditors: hongbin
 * @LastEditTime: 2023-10-24 10:05:02
 * @Description:
 */
import * as THREE from "three";
import { UnrealBloomEffect } from "@/src/ThreeHelper/decorators/UnrealBloomEffect";
import { MainScreen } from "../../components/Three/Canvas";
import { ThreeHelper } from "../../ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "../../ThreeHelper/decorators";
import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { BackgroundLine } from "./BackgroundLine";

@UnrealBloomEffect({
    close: true,
    smaa: false,
    fxaa: true,
    bloom: { threshold: 0.0, strength: 0.3, radius: 0.3, exposure: 1 },
})
export class CartoonMain extends MainScreen {
    /** UnrealBloomEffect装饰器插入的方法 选择应用辉光效果的物体 */
    select!: (object: Object3D) => void;

    constructor(private helper: ThreeHelper) {
        super(helper);
        this.init();
    }

    @MethodBaseSceneSet({
        addAxis: true,
        cameraPosition: new THREE.Vector3(0, 0, 10),
        // cameraTarget: new THREE.Vector3(0, -4, -20),
        near: 1,
        far: 300,
    })
    init() {
        this.initGui();
        this.loadModel();
        const cartoonMaterial = new THREE.MeshToonMaterial({
            color: "#090",
            gradientMap: this.helper.loadTexture(
                "/textures/gradientMaps/threeTone.jpg",
                (t) => {
                    t.minFilter = THREE.NearestFilter;
                    t.magFilter = THREE.NearestFilter;
                }
            ),
        });
        const geometry = new THREE.TorusKnotGeometry(1, 0.3, 20, 9, 2, 2);
        const mesh = new THREE.Mesh(geometry, cartoonMaterial);
        new BackgroundLine(mesh);
        this.helper.add(mesh);
        const light2 = new THREE.DirectionalLight(0xffffff, 3);
        light2.position.set(100, 200, 100);
        this.helper.camera.add(light2);
        this.helper.add(this.helper.camera);
    }

    initGui() {
        const gui = this.helper.gui!;
        if (!gui) this.helper.addGUI();
    }

    @ThreeHelper.InjectAnimation()
    animation() {}

    @LoadGLTF("/public/models/jp.glb")
    loadModel(gltf?: GLTF) {
        if (!gltf) throw Error("gltf not loaded");
        this.helper.add(gltf.scene);

        const meshList: THREE.Mesh[] = [];

        gltf.scene.traverse((node) => {
            if (node.type === "Mesh") {
                const mesh = node as THREE.Mesh<
                    THREE.BufferGeometry,
                    THREE.MeshStandardMaterial
                >;
                meshList.push(mesh);
                const newMat = new THREE.MeshToonMaterial({
                    color: "#fff",
                    gradientMap: this.helper.loadTexture(
                        "/textures/gradientMaps/threeTone.jpg",
                        (t) => {
                            t.minFilter = THREE.NearestFilter;
                            t.magFilter = THREE.NearestFilter;
                        }
                    ),
                    map: mesh.material.map,
                    aoMap: mesh.material.aoMap,
                    alphaMap: mesh.material.alphaMap,
                });
                (mesh.material as THREE.Material) = newMat;
            }
        });

        for (const mesh of meshList) {
            new BackgroundLine(mesh, 0.8, new THREE.Color("#0f0"));
        }
    }
}
