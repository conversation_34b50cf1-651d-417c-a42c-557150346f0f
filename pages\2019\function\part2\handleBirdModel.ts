import { DDSLoader } from "three/examples/jsm/loaders/DDSLoader";
/*
 * @Author: hongbin
 * @Date: 2025-06-17 14:33:05
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-19 21:16:44
 * @Description:
 */

import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Main } from "../../main2";
import * as THREE from "three";
import shader from "../../shader";
import { BaseSkinnedMaterial } from "../../shader/BaseSkinnedMaterial";

interface IRefl {
    offset?: THREE.Vector2;
    repeat?: THREE.Vector2;
    mesh?: THREE.Mesh;
    path: string;
    alpha?: boolean;
    transparent?: boolean;
    iColor?: THREE.Color;
}

export const MapReflection: Record<string, IRefl> = {
    gorund: {
        path: "/public/textures/2019/background_white_pattern.jpg",
        alpha: false,
        repeat: new THREE.Vector2(5, 5),
        iColor: new THREE.Color("#637ba0"),
    },
    Clouds: {
        path: "/public/textures/2019/cloud.png",
        alpha: true,
        transparent: true,
        iColor: new THREE.Color("#d3d3d3"),
        offset: new THREE.Vector2(0, 0),
    },
    Fish: { path: "/public/textures/2019/fish.jpg", alpha: true, iColor: new THREE.Color("#ccc") },
    Plancton_1_2: { path: "/public/textures/2019/blob_1.png", alpha: true, iColor: new THREE.Color("#16EFAF") },
    Plancton_2_2: { path: "/public/textures/2019/blob_1.png", alpha: true, iColor: new THREE.Color("#11AAB1") },
    Plancton_3_2: { path: "/public/textures/2019/blob_1.png", alpha: true, iColor: new THREE.Color("#73A1BF") },
    Planction_4: { path: "/public/textures/2019/blob_1.png", alpha: true, iColor: new THREE.Color("#D9B0C5") },
    Traces: { path: "/public/textures/2019/blob_1.png", alpha: true, iColor: new THREE.Color("#655D8E") },
    ShadowPlane: {
        path: "/public/textures/2019/bird_shadow.png",
        // offset: new THREE.Vector2(0.5, 0.5),
        // offset: new THREE.Vector2(0., 0.),
        // offset: new THREE.Vector2(0.5, 0.),
        offset: new THREE.Vector2(0, 0.5),
        repeat: new THREE.Vector2(0.5, 0.5),
        alpha: true,
        transparent: true,
        iColor: new THREE.Color("#235"),
    },
};

export const handleBirdModel = (self: Main, gltf: GLTF) => {
    self.helper.add(gltf.scene);

    console.log(gltf)

    const meshes = gltf.scene.getObjectsByProperty("type", "Mesh") as THREE.Mesh[];
    const sk_meshes = gltf.scene.getObjectsByProperty("type", "SkinnedMesh") as THREE.Mesh[];

    self.addSelectedObjects(sk_meshes[0]);

    const m = new BaseSkinnedMaterial({
        map: new DDSLoader().load("/textures/2019/goose.dds", (t) => {
            t.colorSpace = THREE.SRGBColorSpace;
            t.flipY = false;
        }),
    });

    sk_meshes[0].material = m;

    meshes.forEach((mesh) => {
        let refl: IRefl;

        if ((refl = MapReflection[mesh.name])) {
            refl.mesh = mesh;

            mesh.morphTargetInfluences && (mesh.morphTargetInfluences[0] = 0);

            mesh.material = new BaseMaterial(
                {
                    iColor: { value: refl.iColor || new THREE.Color(0xffffff) },
                    tDiffuse: {
                        value: self.helper.loadTexture(refl.path, (t) => {
                            t.colorSpace = THREE.SRGBColorSpace;
                            t.wrapS = t.wrapT = THREE.RepeatWrapping;
                        }),
                    },
                    // 替代texture.offset
                    iOffset: { value: refl.offset || new THREE.Vector2(0, 0) },
                    iRepeat: { value: refl.repeat || new THREE.Vector2(1, 1) },
                },
                {
                    USE_Alpha_X: !!refl.alpha,
                    USE_Transparent: !!refl.transparent,
                }
            );
            // self.morphGUI(mesh);

            // if (mesh.name == "Clouds") {
            // console.log(self.helper.gui);
            // self.helper.gui?.addColor({ color: "#" + refl.iColor?.getHexString() }, "color").onChange((v) => {
            //     mesh.material.uniforms.iColor.value.set(new THREE.Color(v));
            // });
            // }
        }
    });

    const ShadowPlane = MapReflection["ShadowPlane"].mesh;

    if (ShadowPlane) {
        self.morphGUI(ShadowPlane);
    }
};

class BaseMaterial extends THREE.ShaderMaterial {
    constructor(uniforms: { [key: string]: THREE.IUniform }, defines: { [key: string]: boolean }) {
        super({
            uniforms: {
                ...uniforms,
            },
            defines: {
                ...defines,
            },
            vertexShader: /* glsl */ `
                varying vec2 vUv;

                #ifdef MORPHTARGETS_COUNT
                    #include <common>
                    #include <skinning_pars_vertex>
                    #include <normal_pars_vertex>
                    #include <morphtarget_pars_vertex>
                #endif

                void main() {
                    vUv = uv;

                    #ifdef MORPHTARGETS_COUNT
                        #include <morphinstance_vertex>
                        #include <morphcolor_vertex>

                        #include <begin_vertex>

                        for(int i = 0; i < MORPHTARGETS_COUNT; i++) {
                            if(morphTargetInfluences[i] != 0.0) {
                                vec3 morphTrans = getMorph(gl_VertexID, i, 0).xyz * morphTargetInfluences[i];
                                transformed += morphTrans;
                            }
                        }

                        float mP = (modelMatrix * vec4(transformed,1.)).y;

                        if(mP > -1.){
                            transformed = vec3(0.);
                        }
                        // if(mP > 0.){
                        //     transformed = vec3(0.);
                        // }

                        #include <project_vertex>
                    #else
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    #endif

                }
            `,
            fragmentShader: /* glsl */ `
                varying vec2 vUv;
                uniform sampler2D tDiffuse;
                uniform vec3 iColor;
                uniform vec2 iOffset;
                uniform vec2 iRepeat;

                void main() {
                    vec4 texel = texture2D(tDiffuse, (vUv * iRepeat) + iOffset);

                    texel.rgb *= iColor;

                    gl_FragColor = vec4(texel.rgb,1.);

                    #ifdef USE_Alpha_X
                        gl_FragColor.rgb = texel.rgb;

                        #ifdef USE_Transparent
                            gl_FragColor.a = texel.a;
                        #else
                            if(texel.x < 0.1) discard;
                        #endif

                    #endif
                    
                }
            `,
            transparent: !!defines.USE_Transparent,
        });
    }
}
