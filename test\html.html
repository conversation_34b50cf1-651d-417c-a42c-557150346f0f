<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>Canvas 圆环加缩放图</title>
        <style>
            body {
                background: #222;
            }
            canvas {
                display: block;
                margin: 50px auto;
            }
        </style>
    </head>
    <body>
        <canvas id="canvas" width="128" height="128"></canvas>

        <script>
            const canvas = document.getElementById("canvas");
            const ctx = canvas.getContext("2d");
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 64;

            const outerImage = new Image();
            outerImage.src = "../public/textures/2019/hotspot.png"; // 替换为你的图片路径或使用 FileReader 方式加载

            const innerImage = new Image();
            innerImage.src = "../public/textures/2019/hover_inner.png";

            Promise.all([
                new Promise((res) => (outerImage.onload = res)),
                new Promise((res) => (innerImage.onload = res)),
            ]).then(() => {

                function draw(progress) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    // 裁剪圆形扇形区域
                    ctx.save();
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + progress * 2 * Math.PI);
                    ctx.closePath();
                    ctx.clip();

                    ctx.drawImage(outerImage, 0, 0, canvas.width, canvas.height);
                    ctx.restore();

                    // 画内图，缩放比例从 1 → 0.5
                    let scale = Math.max(0.5,1 - 2 * progress); // 保持不小于 0.5
                    const iw = innerImage.width * scale;
                    const ih = innerImage.height * scale;
                    ctx.drawImage(innerImage, centerX - iw / 2, centerY - ih / 2, iw, ih);

                    if (progress <= 1) {
                        requestAnimationFrame(draw);
                    }
                    progress += 0.01;
                }

                draw();
            });
        </script>
    </body>
</html>
