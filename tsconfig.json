{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["*.d.ts", "**/*.ts", "**/*.tsx", "*/ThreeHelper/types/*.ts", "*/ThreeHelper/types/*.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}