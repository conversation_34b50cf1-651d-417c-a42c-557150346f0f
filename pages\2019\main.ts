import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "@/src/components/Three/Canvas";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import type { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import type { GUI } from "dat.gui";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { gsap } from "gsap";

import { flowerBlooming, stemBlooming } from "./function/MorphAnimation";
import { createNewFlower } from "./function/createNewFlower";
import { handleFlowerModel } from "./function/handleFlowerModel";
import { handleSceneModel } from "./function/handleSceneModel";
import { TempAudioPlayback } from "./function/TempAudioPlayback";
import { usedEffect } from "./function/usedEffect";
import { createRotateMatrix } from "./function/ints";
import { handleMouseMove } from "./function/handleMouseMove";

const p1 = new THREE.Vector3();
const p2 = new THREE.Vector3();

type Mesh<T extends THREE.MeshBasicMaterial = THREE.MeshBasicMaterial> = THREE.Mesh<THREE.BufferGeometry, T>;

const AA = () => {};

@Injectable
export class Main extends MainScreen {
    static instance: Main;
    clock = new THREE.Clock();
    iTime = { value: 1 };
    // 设置基点
    basePoint = { x: 0, y: 0 };
    prevPoint = { x: 0, y: 0 };
    /** 鼠标按下的点位 */
    basePoint3D = new THREE.Vector3();
    // 路径数据
    pathData: (typeof this.basePoint)[] = [];
    rotateData: number[] = [];
    prevRotateData?: number;
    stem?: THREE.Mesh;
    base?: THREE.Mesh;
    rTextureData = new Float32Array(new Array(100).fill(0));
    rTexture = new THREE.DataTexture(
        this.rTextureData,
        1, // 宽度 1px
        100, // 高度 100px
        THREE.RedFormat, // 丢弃绿色和蓝色分量，只读取红色分量
        THREE.FloatType, // 数据类型
        THREE.Texture.DEFAULT_MAPPING,
        THREE.ClampToEdgeWrapping,
        THREE.ClampToEdgeWrapping,
        THREE.NearestFilter,
        THREE.NearestFilter
    );
    selectedObjects: Object3D[] = [];
    flower?: THREE.Mesh;
    flowerMap?: THREE.Texture;
    flowerArr: THREE.Group[] = [];
    flowerGroup?: THREE.Group<THREE.Object3DEventMap>;
    flowerColor = [
        new THREE.Vector2(0, 0),
        new THREE.Vector2(0, 0.5),
        new THREE.Vector2(0.5, 0),
        new THREE.Vector2(0.5, 0.5),
    ];

    constructor(public helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        Main.instance = this;

        const { ScrollTrigger } = require("gsap/ScrollTrigger");

        gsap.registerPlugin(ScrollTrigger);

        this.init();

        document.addEventListener(
            "click",
            () => {
                TempAudioPlayback("/public/music/UI_MUSIC_STEM-1.mp3", 0.5, true);
            },
            { once: true }
        );
    }

    @MethodBaseSceneSet({
        addAxis: false,
        fov: 30,
        cameraPosition: new THREE.Vector3(0.1, 3, 10.2),
        // cameraPosition: new THREE.Vector3(0, 1.1, 0.5),
        cameraTarget: new THREE.Vector3(0, 1, 0),
        useRoomLight: true,
    })
    async init() {
        this.helper.renderer.setPixelRatio(2);

        this.flowerMap = this.helper.loadTexture("/public/textures/2019/flower_color.jpg", (t) => {
            t.colorSpace = THREE.SRGBColorSpace;
            t.flipY = false;

            // 黄
            t.offset = new THREE.Vector2(0, 0);
            // 粉
            t.offset = new THREE.Vector2(0, 0.5);
            // 清
            t.offset = new THREE.Vector2(0.5, 0);
            // 蓝
            t.offset = new THREE.Vector2(0.5, 0.5);
        });

        // this.initShadow();
        usedEffect(this);

        // 定位花朵的位置和旋转角度
        // const box = this.helper.create.plane(1, 1).add();

        // box.material(
        //     new THREE.MeshBasicMaterial({
        //         map: this.flowerMap,
        //     })
        // );

        // box.mesh.position.set(0, 0.5, 0);

        // this.flower = box.mesh;

        // 加载模型等
        this.handleScene();

        const plane = this.helper.create.plane(15, 3);
        // plane.add();

        plane.mesh.rotateX(Math.PI / -2);
        plane.mesh.name = "floor";

        plane.mesh.position.set(0, 0, -0.5);

        plane.material(
            new THREE.MeshBasicMaterial({
                color: "#BDEACA",
            })
        );

        // 改变背景颜色
        // this.helper.scene.background = new THREE.Color(0x5511ff);

        // EventMesh.appendIntersectObjects([plane.mesh]);
    }

    /**
     * 鼠标按下 确认有效 记录信息
     */
    @EventMesh.OnMouseDown(Main)
    onMouseDown(RayInfo: BackIntersection, _: undefined, event: MouseEvent) {
        // 点击目标需要是canvas
        if (RayInfo.object && (<HTMLElement>event.target).tagName == "CANVAS") {
            if (RayInfo.object.name === "seed_picking") {
                TempAudioPlayback("/public/music/UI-Clic.mp3", 0.1);

                this.pathData = [];
                this.rotateData = [];
                this.prevRotateData = undefined;
                this.basePoint = { x: event.clientX, y: event.clientY };
                this.basePoint3D.copy(RayInfo.point);
                this.pathData.push(this.basePoint);

                // this.createNewFlower();

                // for (let i = 0; i < 100; i++) {
                //     this.rTextureData[i] = 3.14 * 10;
                // }

                EventMesh.enabledMouseMove = true;
                // EventMesh.mouseMoveIntersects = true;
                this.helper.controls.enabled = false;
            }
        }
    }

    /**
     * 记录移动距离
     * 创建一次性花朵
     * 填充生长路径
     */
    @EventMesh.OnMouseMove(Main)
    onMouseMove(event: MouseEvent) {
        const StartingDistance = 10;

        const allowStart = event.clientY - this.basePoint.y < -StartingDistance;

        if (!allowStart && !this.flowerGroup) return;

        if (!this.flowerGroup) {
            createNewFlower(this);
        }

        const space = 2;

        if (Math.abs(event.clientX - this.prevPoint.x) > space || Math.abs(event.clientY - this.prevPoint.y) > space) {
            if (EventMesh.RayInfo?.point) {

                handleMouseMove(this, event, (x: number, y: number, angle: number) => {

                    const index = this.rotateData.length - 1;

                    this.rotateData.push(angle);

                    this.stem!.userData.recordIndex = Math.min(100, index + 1);
                    (<THREE.ShaderMaterial>this.stem!.material).uniforms.recordIndex.value =
                        Math.min(100, index + 1) / 100;

                    if (index > 100) {
                        EventMesh.enabledMouseMove = false;
                    } else {
                        if (index >= 2) {
                            for (let i = 0; i < index; i++) {
                                const curr = this.rotateData[i];

                                let next = this.rotateData[i + 1];

                                if (i == index - 1) {
                                    next = this.rotateData[i - 1];
                                }

                                this.rotateData[i] = (curr + next) / 2;
                            }

                            for (let i = 0; i < index; i++) {
                                this.rTextureData[i] = this.rotateData[i];
                            }
                        } else {
                            this.rTextureData[index] = angle;
                        }

                        this.rTexture.needsUpdate = true;

                        // 计算花朵的位置和旋转角度
                        if (this.flower) {
                            const lastIndex = Math.min(100, index) - 1;

                            // 获取前两个点的位置
                            p1.set(0, lastIndex / 100, 0.0);
                            p2.set(0, (lastIndex - 1) / 100, 0.0);

                            p1.applyMatrix4(createRotateMatrix(this.rTextureData[lastIndex]));
                            p2.applyMatrix4(createRotateMatrix(this.rTextureData[lastIndex - 1]));

                            const dirAngle = -this.getAngle({ x: p1.x, y: p1.y }, { x: p2.x, y: p2.y });

                            this.flower.position.copy(p1);
                            // flower z轴有偏转 设置y轴旋转值
                            this.flower.rotation.y = -dirAngle;
                        }
                    }
                });
            }

            this.prevPoint.x = event.clientX;
            this.prevPoint.y = event.clientY;
        }
    }

    /**
     * 鼠标抬起
     * 花朵绽放
     * 茎 绽放
     */
    @EventMesh.OnMouseUp(Main)
    onMouseUp(event: MouseEvent) {
        const target = event.target as HTMLElement;

        if (!target || target.nodeName != "CANVAS") return;

        EventMesh.enabledMouseMove = false;
        this.helper.controls.enabled = true;

        if (this.flowerGroup && this.stem && this.flower) {
            flowerBlooming(this.flower);
            stemBlooming(this.stem);
        }

        this.flowerGroup = undefined;
    }

    async handleScene() {
        EventMesh.setIntersectObjects([]);

        await this.loadModel();
        await this.loadModel2();
    }

    initShadow() {
        const spotLight = new THREE.SpotLight(0xffffff, 1);
        spotLight.angle = Math.PI / 3;
        spotLight.target.position.set(0, 0, -20);
        this.helper.add(spotLight.target);
        // spotLight.penumbra = 0.3;
        spotLight.position.set(0, 10, -20);
        spotLight.castShadow = true;
        // spotLight.shadow.camera.near = 8;
        // spotLight.shadow.camera.far = 30;
        spotLight.shadow.mapSize.width = 1024;
        spotLight.shadow.mapSize.height = 1024;
        spotLight.shadow.intensity = 2000;

        this.helper.add(spotLight);

        const h = new THREE.SpotLightHelper(spotLight);
        this.helper.add(h);

        this.helper.renderer.shadowMap.enabled = true;
        this.helper.renderer.shadowMap.type = THREE.PCFShadowMap;
    }

    @LoadGLTF("/public/models/scene1.glb")
    async loadModel(gltf?: GLTF) {
        if (gltf) handleSceneModel(this, gltf);
    }

    @LoadGLTF("/public/models/花.glb")
    async loadModel2(gltf?: GLTF) {
        if (gltf) handleFlowerModel(this, gltf);
    }

    // 蒙皮调试
    morphGUI(mesh: THREE.Mesh) {
        const gui = this.helper.gui!;

        if (!gui) return;

        const morph = gui.addFolder(mesh.name + "形态键");
        const morphTargetInfluences = mesh.morphTargetInfluences;

        if (morphTargetInfluences) {
            morphTargetInfluences[0] = 1;

            morphTargetInfluences.forEach((_, i) => {
                morph.add(morphTargetInfluences, "" + i, 0, 1, 0.01).name(`形态键${i}`);
            });

            morph.open();
        }
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {
        const delta = this.clock.getDelta();
        this.iTime.value += delta;
    }

    applyRotate = true;

    @ThreeHelper.AddGUI(Main)
    Gui(gui: GUI) {
        // this.helper.controls.enabled = false;
        // gui.add(this, "applyRotate");

        const flowerMap = this.flowerMap;

        if (flowerMap) {
            const flowerTextureType = gui.addFolder("花瓣纹理");

            flowerTextureType.addFunction(() => {
                flowerMap.offset = new THREE.Vector2(0, 0);
            }, "黄");
            flowerTextureType.addFunction(() => {
                flowerMap.offset = new THREE.Vector2(0, 0.5);
            }, "粉");
            flowerTextureType.addFunction(() => {
                flowerMap.offset = new THREE.Vector2(0.5, 0);
            }, "清");
            flowerTextureType.addFunction(() => {
                flowerMap.offset = new THREE.Vector2(0.5, 0.5);
            }, "蓝");
        }

        setTimeout(() => {
            if (this.stem) {
                gui.addFunction(() => {
                    stemBlooming(this.stem);
                }, "茎-绽放");
            }

            if (this.flower) {
                gui.addFunction(() => {
                    flowerBlooming(this.flower);
                }, "花朵-绽放");
            }
        }, 1000);
    }

    appendRotateData() {}

    getAngle(base: typeof this.basePoint, curr: typeof this.basePoint) {
        // let angle = Math.atan2(curr.y - base.y, curr.x - base.x);
        // let angle = Math.atan2(curr.y - base.y, curr.x - base.x) * (180 / Math.PI);
        let dx = base.y - curr.y;
        let dy = base.x - curr.x;
        // let angle = Math.atan2(dy, dx) * (180 / Math.PI); // 转换为角度
        const angle = Math.atan2(dy, dx);

        // console.log(`当前点角度: ${(angle * (180 / Math.PI)).toFixed(2)}°,${angle}`);

        return angle;
    }

    addSelectedObjects(Object3D: Object3D) {}
}
