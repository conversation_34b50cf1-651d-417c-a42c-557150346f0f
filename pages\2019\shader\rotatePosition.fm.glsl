varying vec2 vUv;
varying vec3 vPos;
varying float vRotationAngle;
varying float alreadyRotated;
uniform sampler2D diffuseTexture;
uniform vec3 color;
uniform float recordIndex;

void main() {
    gl_FragColor.rgb = color;
    gl_FragColor.a = 1.;

    // ==NEW==
    // 由于下方 ‘vPos.z >= (recordIndex - 0.005)’ 也可隐藏 虽然效果稍差一点但是可以接受 可以被花朵遮挡展开动画缺失的部分
    // ==OLD==
    // 最上方枝叶 未张开 隐藏 
    // if(vPos.z > 1. && abs(vPos.x) < 0.01) {
    //     discard;
    // }

    // 未设置生长路径的部分隐藏 || 未设置完整生长路径的部分隐藏 recordIndex最大为1 也就是0.995以上的部分全部隐藏
    // 并且 取消粘连情况出现
    if(vRotationAngle > 31.1 || vPos.z >= (recordIndex - 0.005)) {
        discard;
    }

    #include <tonemapping_fragment>
	#include <colorspace_fragment>
}