"use client";
/*
 * @Author: AI Assistant
 * @Date: 2025-01-20
 * @Description: 示例导航组件
 */
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import styled, { keyframes } from 'styled-components';

// 动画定义
const slideIn = keyframes`
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
`;

const slideOut = keyframes`
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
`;

// 样式组件
const NavigationWrapper = styled.div`
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    height: 100vh;
    display: flex;
    align-items: center;
`;

const ToggleButton = styled.button<{ isOpen: boolean }>`
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #007bff;
    border-radius: 25px 0 0 25px;
    color: white;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    
    &:hover {
        background: rgba(0, 123, 255, 0.8);
        transform: translateY(-50%) scale(1.1);
    }
    
    &::before {
        content: '${props => props.isOpen ? '✕' : '☰'}';
    }
`;

const NavigationPanel = styled.div<{ isOpen: boolean }>`
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 123, 255, 0.1));
    backdrop-filter: blur(20px);
    border-left: 2px solid rgba(0, 123, 255, 0.3);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    animation: ${props => props.isOpen ? slideIn : slideOut} 0.3s ease-in-out;
    transform: ${props => props.isOpen ? 'translateX(0)' : 'translateX(100%)'};
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
`;

const PanelTitle = styled.h2`
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.3);
`;

const DemoButton = styled.button<{ active?: boolean }>`
    padding: 15px 20px;
    background: ${props => props.active 
        ? 'linear-gradient(135deg, #007bff, #0056b3)' 
        : 'rgba(255, 255, 255, 0.1)'};
    color: white;
    border: 2px solid ${props => props.active ? '#007bff' : 'rgba(255, 255, 255, 0.2)'};
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
    
    &:hover {
        background: ${props => props.active 
            ? 'linear-gradient(135deg, #0056b3, #004085)' 
            : 'rgba(255, 255, 255, 0.2)'};
        border-color: #007bff;
        transform: translateX(-5px);
        box-shadow: 5px 5px 15px rgba(0, 123, 255, 0.3);
    }
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    &:hover::before {
        left: 100%;
    }
`;

const DemoLabel = styled.div`
    font-size: 18px;
    margin-bottom: 5px;
`;

const DemoDescription = styled.div`
    font-size: 12px;
    opacity: 0.8;
    color: #ccc;
`;

const StatusIndicator = styled.div<{ active?: boolean }>`
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: ${props => props.active ? '#00ff88' : '#666'};
    box-shadow: ${props => props.active ? '0 0 10px #00ff88' : 'none'};
`;

// 示例数据
const demos = [
    { 
        path: '/', 
        label: '🌀 莫比乌斯渲染', 
        description: '空间扭曲与穿梭门效果',
        title: 'Moebius Rendering' 
    },
    { 
        path: '/2017', 
        label: '⚡ 物理引擎', 
        description: '碰撞检测与重力模拟',
        title: 'Physics Engine' 
    },
    { 
        path: '/2018', 
        label: '🎨 交互动画', 
        description: '鼠标交互与GSAP动画',
        title: 'Interactive Animation' 
    },
    { 
        path: '/2019', 
        label: '🌸 花朵生长', 
        description: '有机形态变换动画',
        title: 'Flower Blooming' 
    },
    { 
        path: '/2018/part2', 
        label: '🎭 2018-Part2', 
        description: '高级交互效果',
        title: '2018 Advanced' 
    },
    { 
        path: '/2019/part2', 
        label: '🌺 2019-Part2', 
        description: '扩展花朵动画',
        title: '2019 Extended' 
    },
];

// 主组件
export const DemoNavigation: React.FC = () => {
    const [isOpen, setIsOpen] = useState(false);
    const router = useRouter();
    const currentPath = router.pathname;
    
    const handleNavigation = (path: string) => {
        router.push(path);
        setIsOpen(false); // 导航后关闭面板
    };
    
    const togglePanel = () => {
        setIsOpen(!isOpen);
    };
    
    return (
        <NavigationWrapper>
            <ToggleButton isOpen={isOpen} onClick={togglePanel} />
            <NavigationPanel isOpen={isOpen}>
                <PanelTitle>🎮 示例导航</PanelTitle>
                {demos.map((demo) => (
                    <DemoButton
                        key={demo.path}
                        active={currentPath === demo.path}
                        onClick={() => handleNavigation(demo.path)}
                        title={demo.title}
                    >
                        <DemoLabel>{demo.label}</DemoLabel>
                        <DemoDescription>{demo.description}</DemoDescription>
                        <StatusIndicator active={currentPath === demo.path} />
                    </DemoButton>
                ))}
            </NavigationPanel>
        </NavigationWrapper>
    );
};

export default DemoNavigation;
