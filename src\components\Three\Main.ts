/*
 * @Author: hongbin
 * @Date: 2023-10-16 13:52:48
 * @LastEditors: hongbin
 * @LastEditTime: 2023-12-06 15:34:35
 * @Description:
 */
import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "./Canvas";
import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import EventMesh, {
    BackIntersection,
} from "@/src/ThreeHelper/decorators/EventMesh";
import gsap from "gsap";

const upDir = new THREE.Vector3(0, 1, 0);
const rightDir = new THREE.Vector3(1, 0, 0);
const frontDir = new THREE.Vector3(0, 0, 1);

// 前 右 左
const relation = [
    [-45, 45],
    [45, 135],
    [-135, -45],
];
const relationVal: Record<
    string,
    { dir: string; x: number; y: number; axis1: Vector3; axis2: Vector3 }
> = {
    0: {
        dir: "前",
        x: 1,
        y: 1,
        axis1: upDir,
        axis2: rightDir,
    },
    1: {
        dir: "右",
        x: 1,
        y: 1,
        axis1: upDir,
        axis2: frontDir,
    },
    2: {
        dir: "左",
        x: 1,
        y: -1,
        axis1: upDir,
        axis2: frontDir,
    },
    3: {
        dir: "后",
        x: 1,
        y: -1,
        axis1: upDir,
        axis2: rightDir,
    },
};

interface IPostures {
    name: string;
    rotation: Array<number>;
}

export class Main extends MainScreen {
    bones: THREE.Bone[] = [];
    static instance: Main;
    /** 相机 水平角度 */
    horizontalAngle: number = 0;
    /** 相机 垂直角度 */
    verticalAngle: number = 0;
    bone?: THREE.Bone;
    controlsInfo?: (typeof relationVal)[string];
    _dir = new THREE.Vector3();
    postures: IPostures[][] = [];

    constructor(private helper: ThreeHelper) {
        super(helper);
        this.init();
        Main.instance = this;
    }

    @MethodBaseSceneSet({
        addAxis: true,
        cameraPosition: new THREE.Vector3(5, 13, 20).multiplyScalar(0.2),
        near: 0.1,
        far: 1000,
    })
    init() {
        this.initGui();
        this.loadModel();
    }

    @LoadGLTF("/public/models/observer1.glb")
    loadModel(gltf?: GLTF) {
        this.helper.add(gltf!.scene);
        this.initBoneGui(gltf!);
    }

    @EventMesh.OnMouseDown(Main)
    handleMouseDown(backIntersection: BackIntersection) {
        if (
            backIntersection &&
            backIntersection.faceIndex &&
            backIntersection.object?.geometry?.index
        ) {
            //* 通过 faceIndex 找到 geometry.index[index]  再找到 attributes.skinIndex[index] 就是骨骼的索引
            const skinIndex = backIntersection.object.geometry.index.getX(
                backIntersection.faceIndex * 3
            );
            const boneIndex =
                backIntersection.object.geometry.attributes.skinIndex.getX(
                    skinIndex
                );
            const bone = this.bones[boneIndex];

            if (bone) {
                this.bone = bone;
                this.handleConfirmCameraDir();
                EventMesh.enabledMouseMove = true;
                this.helper.controls.enabled = false;
            }
        }
    }

    handleConfirmCameraDir() {
        this.horizontalAngle = this.helper.controls.getAzimuthalAngle();
        this.verticalAngle = this.helper.controls.getPolarAngle();
        const horizontalDeg = THREE.MathUtils.radToDeg(this.horizontalAngle);
        const rangeIndex = relation.findIndex(
            (range) => horizontalDeg > range[0] && horizontalDeg < range[1]
        );
        this.controlsInfo = relationVal[rangeIndex == -1 ? 3 : rangeIndex];
        console.log(this.controlsInfo);
    }

    /**
     *  |\
     * y| \
     *  |--\
     *    x
     * 求: y边和斜边夹脚度数  atan(x / y)
     */
    handleDeg(event: MouseEvent) {
        const startX = EventMesh.RayInfo!.clientX;
        const startY = EventMesh.RayInfo!.clientY;

        const { clientX, clientY } = event;
        const xLength = clientX - startX;
        const yLength = clientY - startY;
        const radian = Math.atan(xLength / yLength);
        //点的位置相同时 NaN 即为 0 / 0
        const deg = THREE.MathUtils.radToDeg(radian);
        return {
            radian: isNaN(radian) ? 0 : radian,
            deg: isNaN(deg) ? 0 : deg,
        };
    }

    twoPI = Math.PI * 2;
    prevX = 0;
    prevY = 0;
    handleRange(event: MouseEvent) {
        const startX = EventMesh.RayInfo!.clientX;
        const startY = EventMesh.RayInfo!.clientY;

        const { clientX, clientY } = event;
        const xLength = clientX - startX;
        const yLength = clientY - startY;
        const { offsetWidth, offsetHeight } = this.helper.renderer.domElement;
        const minSide = Math.min(offsetWidth, offsetHeight);

        const xRange = this.twoPI * ((xLength - this.prevX) / minSide);
        const yRange = this.twoPI * ((yLength - this.prevY) / minSide);
        this.prevX = xLength;
        this.prevY = yLength;
        return {
            xRange,
            yRange,
        };
    }

    @EventMesh.OnMouseMove(Main)
    handleMouseMove(event: MouseEvent) {
        //: 方案1 根据拖拽角度 决定旋转幅度
        // const { deg, radian } = this.handleDeg(event);
        // => 方案2 根据拖拽距离 决定旋转幅度
        const { xRange, yRange } = this.handleRange(event);
        this.findParentQuaternion(this.bone!);

        const { axis1, axis2, x, y } = this.controlsInfo!;
        this._dir.copy(axis1).applyQuaternion(this.affectedQuaternion);
        this.bone!.rotateOnWorldAxis(this._dir, xRange * x);

        this._dir.copy(axis2).applyQuaternion(this.affectedQuaternion);
        this.bone!.rotateOnWorldAxis(this._dir, yRange * y);
    }

    private affectedQuaternion = new THREE.Quaternion();
    readonly _defaultQuaternion = new THREE.Quaternion();

    /** 找到其受父级影响的旋转角度 */
    findParentQuaternion(
        mesh: Object3D,
        reset: boolean = true
    ): typeof this.affectedQuaternion {
        if (reset) {
            this.affectedQuaternion.copy(this._defaultQuaternion);
        }
        if (mesh.parent) {
            this.affectedQuaternion.multiplyQuaternions(
                this.affectedQuaternion,
                mesh.parent.quaternion
            );
            return this.findParentQuaternion(mesh.parent, false);
        }
        return this.affectedQuaternion;
    }

    @EventMesh.OnMouseUp(Main)
    handleMouseUpControl() {
        if (EventMesh.enabledMouseMove) {
            this.helper.controls.enabled = true;
            EventMesh.enabledMouseMove = false;
            this.prevX = 0;
            this.prevY = 0;
        }
    }

    initBoneGui(gltf: GLTF) {
        const skeleton = new THREE.SkeletonHelper(gltf!.scene);
        this.helper.add(skeleton);
        EventMesh.setIntersectObjects([gltf!.scene]);
        this.bones = skeleton.bones;
        skeleton.visible = false;
        this.helper.gui
            ?.add({ show: false }, "show")
            .onChange((isShow) => {
                skeleton.visible = isShow;
            })
            .name("显示骨骼");

        this.helper.gui
            ?.add({ save: () => this.savePosture() }, "save")
            .name("保存姿势");
        this.savePosture("默认姿势");
    }

    savePosture(name?: string) {
        const posture = this.bones.map((bone) => {
            return {
                name: bone.name,
                rotation: [bone.rotation.x, bone.rotation.y, bone.rotation.z],
            };
        });
        console.log(posture);
        this.postures.push(posture);
        this.helper.gui
            ?.add(
                {
                    f: () => {
                        this.togglePosture(posture);
                    },
                },
                "f"
            )
            .name(name || `姿势${this.postures.length + 1}`);
    }

    togglePosture(posture: IPostures[]) {
        this.bones.forEach((bone) => {
            const bonePosture = posture.find((p) => p.name == bone.name);
            if (bonePosture) {
                gsap.to(bone.rotation, {
                    x: bonePosture.rotation[0],
                    y: bonePosture.rotation[1],
                    z: bonePosture.rotation[2],
                    duration: 1,
                });
            } else {
                console.log(bone.name + "没找到");
            }
        });
    }

    initGui() {
        const gui = this.helper.gui!;
        if (!gui) this.helper.addGUI();
        return gui;
    }

    @ThreeHelper.InjectAnimation()
    animation() {
        console.log(this);
    }
}

function getBoneList(object: THREE.Object3D) {
    const boneList = [];

    if ((<any>object).isBone === true) {
        boneList.push(object);
    }

    for (let i = 0; i < object.children.length; i++) {
        boneList.push.apply(boneList, getBoneList(object.children[i]));
    }

    return boneList;
}
