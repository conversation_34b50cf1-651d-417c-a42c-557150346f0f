/*
 * @Author: hongbin
 * @Date: 2025-06-15 17:05:40
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-15 17:05:41
 * @Description:
 */

import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Main } from "../main";
import * as THREE from "three";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";

export const handleSceneModel = (self: Main, gltf: GLTF) => {
    self.helper.add(gltf.scene);
    console.log(gltf);

    const seed_picking = gltf.scene.getObjectByName("seed_picking") as THREE.Mesh;

    if (!seed_picking) throw new Error("No seed_picking");

    EventMesh.appendIntersectObjects([seed_picking]);

    const Green_02 = gltf.scene.getObjectByName("Green_02") as THREE.Mesh;

    Green_02.material = new THREE.ShaderMaterial({
        uniforms: {
            color: { value: new THREE.Color("#7A9B6F") },
            iTime: self.iTime,
        },
        vertexShader: `
                    varying vec2 vUv;
                    uniform float iTime;
                    
                    void main() {
                        vUv = uv;

                        vec3 transformed = position;

                        transformed.z += sin(iTime + position.y) * 0.1;

                        vec4 modelViewPosition = modelViewMatrix * vec4(transformed, 1.0);

                        gl_Position = projectionMatrix * modelViewPosition;
                    }
                `,
        fragmentShader: `
                    uniform vec3 color;

                    void main() {
                    
                        
                        gl_FragColor = vec4(color, 1.0);
                    }
                `,
    });

    const main_plant_texture = gltf.scene.getObjectByName("main_plant_texture") as THREE.Mesh;

    main_plant_texture.material = new THREE.ShaderMaterial({
        uniforms: {
            map: { value: (<{ map: unknown }>main_plant_texture.material).map },
            iTime: self.iTime,
        },
        vertexShader: `
                    varying vec2 vUv;
                    uniform float iTime;
                    
                    void main() {
                        vUv = uv;

                        vec3 transformed = position;

                        transformed.y += sin(iTime + position.x) * 0.05;

                        vec4 modelViewPosition = modelViewMatrix * vec4(transformed, 1.0);

                        gl_Position = projectionMatrix * modelViewPosition;
                    }
                `,
        fragmentShader: `
                    uniform sampler2D map;
                    varying vec2 vUv;

                    void main() {
                    
                        
                        gl_FragColor = vec4(texture2D(map, vUv).rgb, 1.0);
                    }
                `,
    });

    const main_plant_texture_2 = gltf.scene.getObjectByName("main_plant_texture_2") as THREE.Mesh;

    main_plant_texture_2.material = new THREE.ShaderMaterial({
        uniforms: {
            map: { value: (<{ map: unknown }>main_plant_texture_2.material).map },
            iTime: self.iTime,
        },
        vertexShader: `
                    varying vec2 vUv;
                    uniform float iTime;
                    
                    void main() {
                        vUv = uv;

                        vec3 transformed = position;

                        transformed.x += sin(iTime + position.z) * 0.1;

                        vec4 modelViewPosition = modelViewMatrix * vec4(transformed, 1.0);

                        gl_Position = projectionMatrix * modelViewPosition;
                    }
                `,
        fragmentShader: `
                    uniform sampler2D map;
                    varying vec2 vUv;

                    void main() {
                    
                        
                        gl_FragColor = vec4(texture2D(map, vUv).rgb, 1.0);
                    }
                `,
    });

    // 开启阴影
    // gltf.scene.traverse((obj) => {
    //     if (obj instanceof THREE.Mesh) {
    //         if (!obj.name.includes("herbs")) {
    //             obj.castShadow = true;
    //         }
    //     }
    // });

    const Green_01 = gltf.scene.getObjectByName("Green_01") as THREE.Mesh;

    // Green_01.receiveShadow = true;
};
