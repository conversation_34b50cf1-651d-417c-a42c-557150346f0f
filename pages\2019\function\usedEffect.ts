import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass";
import { FXAAShader } from "three/examples/jsm/shaders/FXAAShader";
import { OutLineShaderPass, OutLineShader } from "../shader/outline";
import * as THREE from "three";

import shader from "../shader";
import { ThreeHelper } from "@/src/ThreeHelper";

interface Main {
    helper: ThreeHelper;
    selectedObjects: THREE.Object3D[];
    addSelectedObjects: (Object3D: Object3D) => void;
}

export function usedEffect(self: Main, use_outputPass = true) {
    const { renderer, scene, camera } = self.helper;

    const pixel = renderer.getPixelRatio();

    const fullWidth = innerWidth * pixel;
    const fullHeight = innerHeight * pixel;

    const composer = new EffectComposer(renderer);

    const renderPass = new RenderPass(scene, camera);
    composer.addPass(renderPass);

    const outLineShaderPass = new OutLineShaderPass(scene, camera, self.selectedObjects);
    composer.addPass(outLineShaderPass);

    const bgTilePass = new ShaderPass(
        new THREE.ShaderMaterial({
            uniforms: {
                tileMap: {
                    value: self.helper.loadTexture("/public/textures/2019/bg-tile.png", (t) => {
                        t.wrapS = t.wrapT = THREE.RepeatWrapping;
                    }),
                },
                tDiffuse: { value: null },
                brightness: { value: 1 },
            },
            vertexShader: shader.bgTile.vt,
            fragmentShader: shader.bgTile.fm,
        }),
        "tDiffuse"
    );
    composer.addPass(bgTilePass);

    self.helper.gui?.add(bgTilePass.material.uniforms.brightness, "value", 0, 1, 0.01).name("亮度");

    const resolution = new THREE.Vector2(fullWidth, fullHeight);

    OutLineShader.uniforms.resolution.value.copy(resolution);

    if (use_outputPass) {
        const outputPass = new OutputPass();
        composer.addPass(outputPass);
    }

    const effectFXAA = new ShaderPass(FXAAShader);
    effectFXAA.uniforms["resolution"].value.set(1 / fullWidth, 1 / fullHeight);
    composer.addPass(effectFXAA);

    self.addSelectedObjects = (Object3D: Object3D) => {
        self.selectedObjects.push(Object3D);
    };

    self.helper.render = () => {
        composer.render();
    };
}
