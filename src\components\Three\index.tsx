"use client";
/*
 * @Author: hongbin
 * @Date: 2023-01-15 14:29:08
 * @LastEditors: hongbin
 * @LastEditTime: 2023-03-28 10:30:23
 * @Description: 新版本使用第三方包需要作为客户端组件渲染
 */
import styled from "styled-components";
import { useRouter } from "next/router";

export const Container = styled.main`
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    background: #000;
`;

export const Title = styled.h1`
    color: #fff;
    font-weight: bold;
    height: 10vh;
    line-height: 10vh;
    text-align: center;
    letter-spacing: 2px;
    text-decoration: underline;
`;

export const Desc = styled.h6`
    color: #ccc;
    font-weight: bold;
    text-align: center;
    letter-spacing: 2px;
`;

// 导航按钮容器
export const NavigationContainer = styled.div`
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
`;

// 导航按钮样式
export const NavButton = styled.button<{ active?: boolean }>`
    padding: 12px 20px;
    background: ${props => props.active ? '#007bff' : 'rgba(0, 0, 0, 0.7)'};
    color: white;
    border: 2px solid ${props => props.active ? '#007bff' : '#fff'};
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 120px;

    &:hover {
        background: ${props => props.active ? '#0056b3' : 'rgba(255, 255, 255, 0.1)'};
        border-color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    &:active {
        transform: translateY(0);
    }
`;

// 导航组件
export const DemoNavigation: React.FC = () => {
    const router = useRouter();
    const currentPath = router.pathname;

    const demos = [
        { path: '/', label: '🌀 莫比乌斯', title: 'Moebius Rendering' },
        { path: '/2017', label: '⚡ 物理引擎', title: 'Physics Engine' },
        { path: '/2018', label: '🎨 交互动画', title: 'Interactive Animation' },
        { path: '/2019', label: '🌸 花朵生长', title: 'Flower Blooming' },
        { path: '/2018/part2', label: '🎭 2018-P2', title: '2018 Part 2' },
        { path: '/2019/part2', label: '🌺 2019-P2', title: '2019 Part 2' },
    ];

    const handleNavigation = (path: string) => {
        router.push(path);
    };

    return (
        <NavigationContainer>
            {demos.map((demo) => (
                <NavButton
                    key={demo.path}
                    active={currentPath === demo.path}
                    onClick={() => handleNavigation(demo.path)}
                    title={demo.title}
                >
                    {demo.label}
                </NavButton>
            ))}
        </NavigationContainer>
    );
};
