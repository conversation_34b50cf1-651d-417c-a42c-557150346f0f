// declare namespace ThreeHelper {
//     let frameHandle: number;
//     let framing: boolean;
//     const _animation: VoidFunction;
//     const AnimationPlayer;
//     const SkeletonAnimation;
//     const LinearAnimation;
//     const RandomColor;
//     const stats;
//     const gui;
//     const clock: THREE.Clock;
//     const instance: typeof ThreeHelper;
//     const runAnimate = true;
//     const create;
//     const handles: Array<VoidFunction>;
// }
