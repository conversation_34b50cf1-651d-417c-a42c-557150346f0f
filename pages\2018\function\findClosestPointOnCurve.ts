/*
 * @Author: hongbin
 * @Date: 2025-07-01 12:53:00
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-04 07:49:26
 * @Description:
 */
import * as THREE from "three";

let lastClosestIndex = -1;

/**
 * 查找最近的点 进度 索引 距离最近点距离 下一个点的方向
 * 使用 第一次全部遍历 剩下的在附近索引5个内查找的方式优化
 */
export function findClosestPointOnCurve(positions: number[], currPos: THREE.Vector3) {
    let closestDist = Infinity;
    let closestIndex = 0;
    let closestPosition: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 };

    for (let index = 0; index < positions.length; index += 3) {
        const i3 = index * 3;

        const position = {
            x: positions[i3],
            y: positions[i3 + 1],
            z: positions[i3 + 2],
        };

        const dist = currPos.distanceTo(position);

        if (dist < closestDist) {
            closestPosition = position;
            closestDist = dist;
            closestIndex = index;
        }
    }

    lastClosestIndex = closestIndex;

    const nextIndex = (closestIndex + 1) % (positions.length / 3);

    const nextPosition = {
        x: positions[nextIndex * 3],
        y: positions[nextIndex * 3 + 1],
        z: positions[nextIndex * 3 + 2],
    };

    const vp = new THREE.Vector3().copy(nextPosition);
    // 根据当前位置(currPos) 和 最近的点(closestDist) 以及 下一个点(nextPosition) 获取当前位置在最近的两个点之间的进度百分比
    const stepProgress = closestDist / vp.distanceTo(closestPosition);

    const progress = closestIndex / (positions.length / 3) + stepProgress / positions.length;
    const nextProgress = nextIndex / (positions.length / 3) ;

    return { progress, index: closestIndex, dist: closestDist, dir: vp.sub(closestPosition), nextPosition,nextProgress };
}
