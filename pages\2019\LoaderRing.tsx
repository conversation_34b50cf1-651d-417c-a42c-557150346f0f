import { FC, useCallback, useEffect, useRef } from "react";
import styled from "styled-components";

interface IProps {}

const LoadingRing: FC<IProps> = () => {
    const wrapRef = useRef<HTMLDivElement>(null);
    const titleRef = useRef<HTMLParagraphElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    const draw = useCallback(
        (
            ctx: CanvasRenderingContext2D,
            outerImage: HTMLImageElement,
            innerImage: HTMLImageElement,
            progress: number
        ) => {
            const centerX = 128 / 2;
            const centerY = 128 / 2;
            const radius = 64;

            ctx.clearRect(0, 0, 128, 128);

            // 裁剪圆形扇形区域
            ctx.save();
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + progress * 2 * Math.PI);
            ctx.closePath();
            ctx.clip();

            ctx.drawImage(outerImage, 0, 0, 128, 128);
            ctx.restore();

            // 画内图，缩放比例从 1 → 0.5
            let scale = Math.max(0.5, 1 - 2 * progress); // 保持不小于 0.5
            const iw = innerImage.width * scale;
            const ih = innerImage.height * scale;
            ctx.drawImage(innerImage, centerX - iw / 2, centerY - ih / 2, iw, ih);

            if (titleRef.current) {
                titleRef.current.style.opacity = String(Math.max(0., 1 - 2 * progress));
            }

            // if (progress <= 1) {
            //     requestAnimationFrame(draw);
            // }
            // progress += 0.01;
        },
        []
    );

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        const outerImage = new Image();
        outerImage.src = "/textures/2019/hotspot4.png";

        const innerImage = new Image();
        innerImage.src = "/textures/2019/hover_inner.png";

        Promise.all([new Promise((res) => (outerImage.onload = res)), new Promise((res) => (innerImage.onload = res))])
            .then(() => {
                draw(ctx, outerImage, innerImage, 0);

                // @ts-ignore
                window.setProgress = (progress: number) => {
                    draw(ctx, outerImage, innerImage, progress);
                };
            })
            .catch((error) => {
                console.error("Error loading images:", error);
            });
    }, []);

    useEffect(() => {
        // 监听鼠标移动事件将鼠标对应wrapRef的中心
        const handleMouseMove = (e: MouseEvent) => {
            if (wrapRef.current) {
                wrapRef.current.style.left = e.clientX - 64 + "px";
                wrapRef.current.style.top = e.clientY - 64 + "px";
            }
        };

        // 监听鼠标离开事件
        const handleMouseLeave = () => {
            if (wrapRef.current) {
                wrapRef.current.style.display = "none";
            }
        };

        window.addEventListener("mousemove", handleMouseMove);
        window.addEventListener("mouseleave", handleMouseLeave);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
            window.removeEventListener("mouseleave", handleMouseLeave);
        };
    }, []);

    return (
        <Container ref={wrapRef}>
            <p ref={titleRef}>Hold</p>
            <canvas ref={canvasRef} width={128} height={128}></canvas>
        </Container>
    );
};

export default LoadingRing;

const Container = styled.div`
    position: fixed;
    top: 0;
    left: 0;
    width: 128px;
    height: 128px;
    z-index: 10;

    color: #fff;
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;

    canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 128px;
        height: 128px;
        z-index: -1;
    }
`;
