/*
 * @Author: hong<PERSON>
 * @Date: 2025-06-15 16:32:09
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-15 21:32:07
 * @Description:
 */

import { Main } from "../main";
import * as THREE from "three";
import { checkLeaveFlower } from "./MorphAnimation";
import { TempAudioPlayback } from "./TempAudioPlayback";

/**
 * 创建花朵
 * 移除超出数量的花朵
 */
export const createNewFlower = (self: Main) => {
    if (self.stem && self.flower && self.base) {
        const flowerGroup = new THREE.Group();

        self.rTextureData = new Float32Array(new Array(100).fill(3.14 * 10));
        self.rTexture = new THREE.DataTexture(
            self.rTextureData,
            1, // 宽度 1px
            100, // 高度 100px
            THREE.RedFormat, // 丢弃绿色和蓝色分量，只读取红色分量
            THREE.FloatType, // 数据类型
            THREE.Texture.DEFAULT_MAPPING,
            THREE.ClampToEdgeWrapping,
            THREE.ClampToEdgeWrapping,
            THREE.NearestFilter,
            THREE.NearestFilter
        );

        // for (let i = 0; i < 100; i++) {
        //     self.rTextureData[i] = 3.14 * 10;
        // }

        self.rTexture.needsUpdate = true;

        const stem = self.stem.clone(true);
        self.stem = stem;

        // @ts-ignore
        stem.morphTargetInfluences[0] = 1;

        flowerGroup.add(stem);
        stem.material = (<THREE.Material>stem.material).clone();
        (<THREE.ShaderMaterial>stem.material).uniforms.rotateTexture.value = self.rTexture;

        stem.userData.rTextureData = self.rTextureData;
        stem.userData.rTexture = self.rTexture;

        const flower = self.flower.clone() as THREE.Mesh<THREE.BufferGeometry, THREE.MeshBasicMaterial>;

        // 随机颜色花朵
        flower.material = flower.material.clone();
        if (self.flowerMap) {
            flower.material.map = self.flowerMap.clone();
            flower.material.map.offset = self.flowerColor[Math.floor(Math.random() * 4)];
        }

        self.flower = flower;

        flower.rotation.x = -1.4;
        flower.rotation.y = 0;
        flower.rotation.z = 0;

        flower.morphTargetInfluences![0] = 1;
        flower.morphTargetInfluences![6] = 0.06;
        flower.morphTargetInfluences![7] = 0.06;
        flower.morphTargetInfluences![9] = 1;
        flower.morphTargetInfluences![10] = 0;

        flower.position.set(0, 0, 0);

        flowerGroup.add(flower);

        const base = self.base.clone();
        self.base = base;

        flowerGroup.add(base);
        flowerGroup.scale.setScalar(7);

        self.helper.add(flowerGroup);

        self.flowerArr.push(flowerGroup);

        flowerGroup.userData.stem = stem;
        flowerGroup.userData.base = base;
        flowerGroup.userData.flower = flower;

        self.flowerGroup = flowerGroup;

        flowerGroup.position.copy(self.basePoint3D);

        // 如果设置花朵存在数量超出限制 则移除第一个
        checkLeaveFlower(self);

        TempAudioPlayback("/public/music/plant_grown.mp3", 0.5);

        return flowerGroup;
    }
};
