/*
 * @Author: hongbin
 * @Date: 2023-10-24 09:09:32
 * @LastEditors: hongbin
 * @LastEditTime: 2023-10-24 09:19:01
 * @Description: 在物体背后增加线条 简易： 卡通风格 边缘检测
 */
import * as THREE from "three";

export class BackgroundLine {
    material: THREE.ShaderMaterial;

    constructor(
        mesh: THREE.Mesh,
        /** 线条粗细 */
        wide = 0.01,
        /** 线条颜色 */
        color = new THREE.Color("#000")
    ) {
        const material = new THREE.ShaderMaterial({
            side: THREE.BackSide,
            vertexShader: `
            void main() {
                vec4 modelViewPosition = modelViewMatrix * vec4(position + normal * ${wide}, 1.0);
                gl_Position = projectionMatrix * modelViewPosition;
            }`,
            fragmentShader: `
            void main() {
                gl_FragColor = vec4(vec3(${color.r},${color.g},${color.b}), 1.0);
            }`,
        });
        this.material = material;
        const bg = new THREE.Mesh(mesh.geometry, material);
        mesh.add(bg);
    }
}
