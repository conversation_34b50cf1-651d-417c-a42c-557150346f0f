/*
 * @Author: hongbin
 * @Date: 2025-06-11 17:08:56
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-17 22:11:53
 * @Description: 动态键动画
 */

import { gsap } from "gsap";
import { CustomEase } from "gsap/CustomEase";
import { Main } from "../main";
import * as THREE from "three";
import { TempAudioPlayback } from "./TempAudioPlayback";
import { createRotateMatrix } from "./ints";

gsap.registerPlugin(CustomEase);

const p1 = new THREE.Vector3();
const p2 = new THREE.Vector3();

/**
 * 茎 绽放
 */
export const stemBlooming = (mesh: Mesh) => {
    gsap.fromTo(
        mesh.morphTargetInfluences,
        {
            0: 1,
        },
        {
            0: 0,
            duration: 0.5,
            ease: "power2.in",
        }
    );

    gsap.fromTo(
        mesh.morphTargetInfluences,
        { 2: 0 },
        {
            2: 1.5,
            duration: 1,
            delay: 0.3,
            ease: CustomEase.create(
                "custom",
                "M0,0 C0.012,0 0.025,0.066 0.05,0.066 0.1,0.066 0.1,-0.211 0.15,-0.211 0.2,-0.211 0.2,0.557 0.25,0.557 0.3,0.557 0.447,-0.226 0.497,-0.226 0.548,-0.226 0.613,0.177 0.663,0.177 0.713,0.177 0.776,-0.073 0.826,-0.073 0.876,-0.073 0.899,-0.024 0.949,-0.024 0.974,-0.024 0.974,0 1,0 "
            ),
        }
    );
};

/**
 * 花朵 绽放
 */
export const flowerBlooming = (mesh: Mesh) => {
    // return;
    gsap.fromTo(
        mesh.morphTargetInfluences,
        {
            0: 1,
        },
        {
            0: 0,
            duration: 0.3,
            ease: "power1.inOut",
        }
    );

    TempAudioPlayback("/public/music/IT-Grass.mp3", 1);

    gsap.fromTo(
        mesh.morphTargetInfluences,
        { 2: 0 },
        {
            2: 1,
            duration: 0.3,
            ease: "power1.inOut",
            onComplete: () => {
                gsap.to(mesh.morphTargetInfluences, {
                    2: 0,
                    duration: 0.3,
                    ease: "power1.inOut",
                });

                //花蕊部分
                gsap.fromTo(
                    mesh.morphTargetInfluences,
                    { 5: 0 },
                    {
                        5: 1,
                        duration: 0.15,
                        ease: "power1.inOut",
                        onComplete: () => {
                            gsap.to(mesh.morphTargetInfluences, {
                                5: 0,
                                duration: 0.15,
                                ease: "power1.inOut",
                            });
                            gsap.to(mesh.morphTargetInfluences, {
                                7: 1,
                                duration: 0.15,
                                ease: "power1.inOut",
                                onComplete: () => {
                                    gsap.to(mesh.morphTargetInfluences, {
                                        7: 0,
                                        duration: 0.15,
                                        ease: "power1.inOut",
                                    });
                                    gsap.to(mesh.morphTargetInfluences, {
                                        9: 1,
                                        duration: 0.15,
                                        ease: "power1.inOut",
                                    });
                                },
                            });
                        },
                    }
                );
            },
        }
    );

    // mesh.morphTargetInfluences[5] = 0;
};

/**
 * 花朵移除
 */
export const checkLeaveFlower = (self: Main) => {
    if (self.flowerArr.length > 5) {
        const flowerGroup = self.flowerArr[0];

        const { base, stem, flower } = flowerGroup.userData;
        const { rTextureData, rTexture, recordIndex } = stem.userData;

        // gsap.to(flowerGroup.position, {
        //     y: flowerGroup.position.y - 0.2,
        //     duration: (recordIndex / 100) * 0.1,
        //     delay: (recordIndex / 100) * 0.9,
        //     ease: "power2.in",
        //     onComplete: () => {},
        // });

        // 茎和花抽离退场 顶点和位置设置
        gsap.to(stem.material.uniforms.leaveProgress, {
            value: 1,
            duration: recordIndex / 100,
            ease: "power2.in",
            onComplete: () => {
                // 暂不移除全部 导致花朵因旋转角度导致不能整体引入地面而闪烁消失
                self.flowerArr.shift();
                gsap.to(base.position, {
                    y: base.position.y - 0.2,
                    duration: 0.1,
                    ease: "power2.in",
                    onComplete: () => {},
                });
                gsap.to(flower.position, {
                    y: flower.position.y - 0.2,
                    duration: 0.1,
                    ease: "power2.in",
                    onComplete: () => {
                        flowerGroup.parent?.remove(flowerGroup);
                    },
                });
            },
            onUpdate: () => {
                // 花朵位置 跟踪
                if (flower) {
                    const leaveProgress = 1 - stem.material.uniforms.leaveProgress.value;

                    const lastIndex = Math.floor(leaveProgress * recordIndex);

                    let p1p = lastIndex;

                    // 获取当前茎高处两个点的位置
                    // 满100元素数组不止为何和不满100元素数组的处理方式不一样
                    if (recordIndex == 100) {
                        p1.set(0, p1p / 100, 0.0);
                        p2.set(0, (p1p - 1) / 100, 0.0);
                    } else {
                        p1p -= 1;
                        p1.set(0, leaveProgress * (p1p / 99), 0.0);
                        p2.set(0, leaveProgress * ((p1p - 1) / 99), 0.0);
                    }

                    p1.applyMatrix4(createRotateMatrix(rTextureData[p1p]));
                    p2.applyMatrix4(createRotateMatrix(rTextureData[p1p - 1]));

                    const dirAngle = -self.getAngle({ x: p1.x, y: p1.y }, { x: p2.x, y: p2.y });

                    flower.position.copy(p1);

                    flower.rotation.y = -dirAngle;
                }
            },
        });

        // 茎和花抽离退场 动画(形态键)设置
        gsap.fromTo(
            stem.morphTargetInfluences,
            { 2: 0 },
            {
                2: 1.5,
                duration: recordIndex / 100,
                ease: CustomEase.create(
                    "custom",
                    "M0,0 C0.012,0 0.025,0.066 0.05,0.066 0.1,0.066 0.1,-0.211 0.15,-0.211 0.2,-0.211 0.2,0.557 0.25,0.557 0.3,0.557 0.447,-0.226 0.497,-0.226 0.548,-0.226 0.613,0.177 0.663,0.177 0.713,0.177 0.776,-0.073 0.826,-0.073 0.876,-0.073 0.899,-0.024 0.949,-0.024 0.974,-0.024 0.974,0 1,0 "
                ),
            }
        );
        gsap.fromTo(
            stem.morphTargetInfluences,
            { 1: 0 },
            {
                1: 1,
                duration: recordIndex / 100,
                ease: "power2.in",
            }
        );

        gsap.fromTo(
            flower.morphTargetInfluences,
            { 10: 0 },
            {
                10: 1,
                duration: recordIndex / 100,
                ease: "power2.inOut",
            }
        );
    }
};
