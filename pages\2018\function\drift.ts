/*
 * @Author: hongbin
 * @Date: 2025-07-01 16:02:09
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-05 09:50:36
 * @Description:
 */
import * as THREE from "three";
import { Main } from "../main";

export type CustomerMaterial = THREE.Mesh<THREE.BufferGeometry, THREE.ShaderMaterial>;

const v3 = new THREE.Vector3();

/**
 * 根据模型中的定位锚点 生成甩尾平面并自定义材质
 */
export const handleDrift = (
    self: Main,
    curveWrap: THREE.Object3D
): { drift: CustomerMaterial; positions: number[]; curve: THREE.CatmullRomCurve3 } => {
    const { geometry, positions, curve } = createGeometry(self, curveWrap);

    const drift = new THREE.Mesh(geometry, CustomerMaterial(self, new THREE.Color("rgb(255, 0, 183)"), 0.2));

    drift.position.set(0, 0.01, 0.025);

    self.select(drift);

    self.helper.add(drift);
    {
        const drift = new THREE.Mesh(geometry, CustomerMaterial(self, new THREE.Color("rgb(0, 255, 255)"), 0.15));

        drift.position.set(-0.025, 0.025, -0.025);

        self.select(drift);

        self.helper.add(drift);
    }
    {
        const drift = new THREE.Mesh(geometry, CustomerMaterial(self, new THREE.Color("rgb(255, 242, 2)"), 0.3));

        drift.position.set(0.025, 0.025, 0);

        self.select(drift);

        self.helper.add(drift);
    }

    return { drift, positions, curve };
};

/**
 * 根据定位点定义曲线
 */
const createGeometry = (self: Main, curveWrap: THREE.Object3D) => {
    const curve = new THREE.CatmullRomCurve3(
        curveWrap.children.map((point) => point.getWorldPosition(v3).clone()),
        true,
        "centripetal"
    );

    /**
     * 根据曲线生成平面
     */
    const halfHeight = 0.01;
    const segments = 200;

    const positions = [];
    const uvs = [];
    const indices = [];

    for (let i = 0; i <= segments; i++) {
        const t = i / segments;

        const point = curve.getPointAt(t);

        // const tangent = curve.getTangentAt(t).normalize();

        const up = new THREE.Vector3(0, 1, 0); // 上方向扩展
        const bottom = point.clone().add(up.clone().multiplyScalar(-halfHeight));
        const top = point.clone().add(up.clone().multiplyScalar(halfHeight));

        positions.push(bottom.x, bottom.y, bottom.z);
        positions.push(top.x, top.y, top.z);

        if (i < segments) {
            const base = i * 2;
            indices.push(base, base + 1, base + 2);
            indices.push(base + 1, base + 3, base + 2);
        }

        const u = i / segments;

        // 两个点：下（v=0）和上（v=1）
        uvs.push(u, 0); // bottom
        uvs.push(u, 1); // top
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute("position", new THREE.Float32BufferAttribute(positions, 3));
    geometry.setAttribute("uv", new THREE.Float32BufferAttribute(uvs, 2));
    geometry.setIndex(indices);
    geometry.computeVertexNormals();

    curveHelper(self, curve, segments);

    return { geometry, positions, curve };
};

/**
 * 曲线可视化辅助查看器
 */
const curveHelper = (self: Main, curve: THREE.CatmullRomCurve3, segments: number) => {
    // 生成采样点
    const points = curve.getPoints(segments);

    const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);

    const group = new THREE.Object3D();
    self.helper.add(group);

    // 使用points为每个点创建可视化辅助
    points.forEach((point, i) => {
        const mesh = new THREE.Mesh(
            new THREE.BoxGeometry(0.01, 0.01, 0.01),
            new THREE.MeshBasicMaterial({ color: 0xff0000 })
        );

        mesh.name = "" + i;

        group.add(mesh);

        mesh.position.copy(point);
        // mesh.position.z += (Math.random() - 0.5) / 10
    });

    // 创建并添加辅助线
    const curveObject = new THREE.Line(lineGeometry, new THREE.LineBasicMaterial({ color: 0xff0000 }));
    group.add(curveObject);

    group.visible = false;

    self.helper.gui?.add(group, "visible").name("curveHelper");
};

const CustomerMaterial = (self: Main, color?: THREE.Color, length?: number) => {
    // 自定义shader
    const shader = {
        uniforms: {
            tDiffuse: { value: null },
            iTime: self.iTime,
            iProgress: self.iProgress,
            iLength: { value: length },
            iColor: { value: color || new THREE.Color("#EB56D2") },
            iOpacity: self.iOpacity,
        },
        vertexShader: /*glsl*/ `
            varying vec2 vUv;

            void main() {
                vUv = uv;

                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: /*glsl*/ `
            uniform sampler2D tDiffuse;
            uniform float iTime;
            uniform float iProgress;
            uniform float iOpacity;
            uniform float iLength;
            uniform vec3 iColor;
            varying vec2 vUv;

            void main() {
                vec3 color = vec3(1.0);

                // float diff = circularDiff(iProgress, vUv.x);
                // float diff = circularDiff( fract(iTime) , vUv.x);
                float diff = iProgress - vUv.x;
                // float diff = fract(iTime) - vUv.x;
                diff = mod(diff , 1.0);

                // 距离越近红色越强 
                //💡 用 diff这个范围比0.2大的数除0.2 在被1减去 除了0.2之内的 其他的都是负数了 可以直接作为透明度使用 也可以约束到0 
                float strength = max(0.0, 1.0 - (diff / iLength));
                color = mix(color, iColor * 2., strength);

                // color = texture2D(tDiffuse, vUv);

                gl_FragColor = vec4(color, strength * iOpacity);
                // gl_FragColor = vec4(color,strength);
            }
        `,
        side: THREE.DoubleSide,
        transparent: true,
    };

    // 自定义材质
    return new THREE.ShaderMaterial(shader);
};
