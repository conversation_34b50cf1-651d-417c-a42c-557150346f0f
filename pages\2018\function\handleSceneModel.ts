/*
 * @Author: hongbin
 * @Date: 2025-06-30 17:38:18
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-06 21:28:42
 * @Description:
 */
import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Main } from "../main";
import { SaiDaoConvex } from "../constant/SaiDaoConvex.js";
import * as THREE from "three";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";
import { ConvexMeshDecomposition } from "vhacd-js";
import { findClosestPointOnCurve } from "./findClosestPointOnCurve";
import { CustomerMaterial, handleDrift } from "./drift";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import { data } from "@/pages/2017/modelData";
import { TextureBlenderMaterial } from "./TextureBlenderMaterial";
import { ThreeHelper } from "@/src/ThreeHelper";
import gsap from "gsap";
import { CustomEase } from "gsap/CustomEase";

const v3 = new THREE.Vector3();

export const handleSceneModel = (self: Main, gltf: GLTF) => {
    self.helper.add(gltf.scene);

    const mesh = gltf.scene.getObjectByProperty("name", "赛道wrap") as THREE.Mesh;
    const curveWrap = gltf.scene.getObjectByProperty("name", "曲线定位") as THREE.Mesh;
    const SaiDaoMesh = gltf.scene.getObjectByProperty("name", "赛道") as THREE.Mesh;
    const ani_mesh = gltf.scene.getObjectByProperty("name", "立方体024") as THREE.Mesh;
    const ani_ring = gltf.scene.getObjectByProperty("name", "贝塞尔圆环006") as THREE.Mesh;
    const cy = gltf.scene.getObjectByProperty("name", "cy") as THREE.Mesh;
    const cy2 = gltf.scene.getObjectByProperty("name", "cy2") as THREE.Mesh;
    const ShuttleRing = gltf.scene.getObjectByProperty("name", "穿梭环") as THREE.Mesh;

    if (SaiDaoMesh) {
        // 从网格生成一系列凸包。
        self.helper.gui
            ?.addFunction(async () => {
                const decomposer = await ConvexMeshDecomposition.create();
                const options = { maxHulls: 64 };

                console.log(
                    decomposer.computeConvexHulls(
                        {
                            positions: SaiDaoMesh.geometry.attributes.position.array as Float64Array,
                            indices: SaiDaoMesh.geometry.index!.array as Uint32Array,
                        },
                        options
                    )
                );
            })
            .name("生成模型凸包");
    }

    // 没有mesh则抛出错误
    if (!mesh) throw new Error("没有获取到mesh");
    if (!curveWrap) throw new Error("没有获取到曲线定位");

    mesh.children[0].castShadow = true;
    // mesh.children[0].receiveShadow = true;
    mesh.children[1].castShadow = true;
    ani_mesh && (ani_mesh.castShadow = true);
    ani_ring && (ani_ring.castShadow = true);
    cy && (cy.castShadow = true);
    cy2 && (cy2.castShadow = true);

    ThreeHelper.handles.push(() => {
        if (ani_ring && ani_mesh) {
            // 不断旋转
            ani_ring.rotation.z += 0.01;
            ani_ring.rotation.x += 0.01;
            ani_ring.rotation.y += 0.01;
            ani_mesh.rotation.z -= 0.01;
            ani_mesh.rotation.x -= 0.01;
            ani_mesh.rotation.y -= 0.01;
        }

        if (self.cruise && self.iProgress.value > 0.7 && self.iProgress.value < 0.75) {
            handleShuttleRingContract(ShuttleRing, self.iProgress.value);
        }
    });

    // mesh.children[1].receiveShadow = true;

    TextureBlenderMaterial(mesh.children[1] as any, self);

    const driftInfo = handleDrift(self, curveWrap);

    self.curve = driftInfo.curve;

    curveWrap.visible = false;
    SaiDaoMesh.visible = false;

    // mesh.position.y = 0.11674 ;

    const saiDaoBody = new CANNON.Body({
        mass: 0,
        // 设置y轴高度为模型中心点 y轴以下距离
        position: new CANNON.Vec3(...mesh.getWorldPosition(v3)),
        // position: new CANNON.Vec3(0,0.6,0),
        material: new CANNON.Material("saiDao"),
    });

    // 数据从文件加载 数据从computeConvexHulls函数 copy
    const hulls = SaiDaoConvex;
    // const hulls = data;

    hulls.forEach(({ positions, indices }) => {
        const vertices: CANNON.Vec3[] = [];
        const faces: number[][] = [];

        for (let index = 0; index < Object.keys(positions).length; index += 3) {
            // @ts-ignore
            vertices.push(new CANNON.Vec3(positions[index], positions[index + 1], positions[index + 2]));
        }

        for (let index = 0; index < Object.keys(indices).length; index += 3) {
            // @ts-ignore
            faces.push([indices[index], indices[index + 1], indices[index + 2]]);
        }

        const part = new CANNON.ConvexPolyhedron({ vertices, faces });

        saiDaoBody.addShape(part);
    });

    self.world.addBody(saiDaoBody);

    self.dynamicBodies.push([mesh, saiDaoBody]);
    // self.dynamicBodies.push([SaiDaoMesh, saiDaoBody]);

    self.selectedObjects.push(mesh.children[0]);

    console.log(gltf);

    const sphere = addSphereToScene(self);

    self.sphere = sphere;

    if (saiDaoBody.material && sphere[1].material) {
        const contactMaterial = new CANNON.ContactMaterial(sphere[1].material, saiDaoBody.material, {
            restitution: 0.1, // 越接近 1 越弹
            friction: 0, // 可选：摩擦力
        });

        self.world.addContactMaterial(contactMaterial);
    }

    self.findClosestPointOnCurve = () => {
        return findClosestPointOnCurve(driftInfo.positions, sphere[0].position);
    };

    //🏁 小球移动时 同步甩尾
    sphere[0].userData.sync = () => {
        // const { progress, index, dir, dist } = findClosestPointOnCurve(driftInfo.positions, sphere[0].position);
        // console.log("progress", progress, "index", index, "dir", dir, "dist:", dist);
        // self.iProgress.value = progress;
    };

    // CircularTruncation.customDepthMaterial =
    // CircularTruncation.visible = false
    // CircularTruncation.layers.toggle(2)
};

/**
 * 添加球体到场景并处理物理交互
 * @param self Main 类实例
 */
const addSphereToScene = (self: Main) => {
    const sphere = createSphere(0.5, 1, 0.8);

    self.helper.scene.add(sphere[0]);

    self.select(sphere[0]);

    self.world.addBody(sphere[1]);

    self.dynamicBodies.push(sphere);

    self.helper.gui
        ?.add(sphere[0].position, "x")
        .name("x轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });
    self.helper.gui
        ?.add(sphere[0].position, "y")
        .name("y轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });
    self.helper.gui
        ?.add(sphere[0].position, "z")
        .name("z轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });

    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(1, 0, 0);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加x轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(-1, 0, 0);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加-x轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(0, 0, 1);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加z轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(0, 0, -1);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加-z轴力");

    if (self.floorBody?.material && sphere[1].material) {
        const contactMaterial = new CANNON.ContactMaterial(sphere[1].material, self.floorBody.material, {
            restitution: 0.5, // 越接近 1 越弹
            friction: 0.4, // 可选：摩擦力
        });

        self.world.addContactMaterial(contactMaterial);
    }

    return sphere;
};

/**
 * 创建一个cannon球体
 */
const createSphere = (x: number, y: number, z: number): [THREE.Mesh, CANNON.Body] => {
    // 定义球体半径
    const radius = 0.04;

    const sphereMesh = new THREE.Mesh(
        new THREE.SphereGeometry(radius, 32, 32),
        new THREE.MeshStandardMaterial({ color: 0xff0000 })
    );

    sphereMesh.name = "ball";

    // 创建球形状
    const sphereShape = new CANNON.Sphere(radius);

    // 创建刚体
    const sphereBody = new CANNON.Body({
        mass: 1, // 设置质量（非零表示可移动）
        shape: sphereShape,
        position: new CANNON.Vec3(x, y, z), // 初始位置（x, y, z）
        material: new CANNON.Material("ball_material"),
    });

    return [sphereMesh, sphereBody];
};

let isRunning = false;
/**
 * 处理穿梭环收缩
 */
const handleShuttleRingContract = (wrap?: THREE.Object3D, progress = 0) => {
    if (!isRunning && progress > 0.7 && wrap && wrap.children.length) {
        isRunning = true;

        // 三个子集 使用gsap更改scale属性不断收缩动画
        const duration = 0.1;
        const ease = CustomEase.create("custom", "M0,0 C0.114,0.61 0.293,1.05 0.388,1 1.041,0.645 0.414,0.343 1,0 ");

        gsap.to(wrap.children[0].scale, {
            x: 1.1,
            y: 1.1,
            z: 1.1,
            duration,
            ease,
        });
        gsap.to(wrap.children[1].scale, {
            x: 1.1,
            y: 1.1,
            z: 1.1,
            delay: 0.1,
            duration,
            ease,
        });
        gsap.to(wrap.children[2].scale, {
            x: 1.1,
            y: 1.1,
            z: 1.1,
            delay: 0.2,
            duration,
            ease,
            onComplete: () => {
                isRunning = false;
            },
        });
    }
};
