/*
 * @Author: hongbin
 * @Date: 2025-07-05 18:07:29
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-05 18:19:40
 * @Description:
 */
import * as THREE from "three";
import gsap from "gsap";
import { CustomEase } from "gsap/CustomEase";

export const createFragment = () => {
    // 创建碎片系统
    const shardCount = 200;
    const shards: THREE.Mesh<THREE.BufferGeometry, THREE.MeshBasicMaterial>[] = [];
    const geometry = new THREE.PlaneGeometry(0.05, 0.03);
    const group = new THREE.Group();

    for (let i = 0; i < shardCount; i++) {
        let material = new THREE.MeshBasicMaterial({
            color: new THREE.Color(`hsl(${Math.random() * 360}, 80%, 60%)`),
            transparent: true,
        });
        let mesh = new THREE.Mesh(geometry, material);
        // mesh.position.set(0, 1, 0);
        group.add(mesh);
        shards.push(mesh);
    }

    function explode() {
        shards.forEach((shard) => {
            const angle = Math.random() * Math.PI * 2;
            const distance = 0.5 + Math.random() * 2;
            const target = {
                x: Math.cos(angle) * distance,
                y: Math.sin(angle) * distance,
                z: (Math.random() - 0.5) * distance,
            };

            // 获取位置和原点的角度
            const a = Math.atan2(target.x, target.y);

            shard.rotateZ(a);
            shard.rotateY(0.1);

            gsap.fromTo(
                shard.position,
                { x: 0, y: 0, z: 0 },
                {
                    x: target.x,
                    y: target.y,
                    z: target.z,
                    duration: 1 + Math.random(),
                    ease: "expo.out",
                }
            );

            gsap.fromTo(
                shard.material,
                {
                    opacity: 0,
                },
                {
                    opacity: 1,
                    duration: 1,
                    ease: CustomEase.create("custom", "M0,0 C0.114,0.61 0.046,0.985 0.153,1 0.921,1.106 0.142,0 1,0 "),
                    onStart() {},
                    onComplete() {
                        // shard.material.transparent = false;
                    },
                }
            );
        });
    }

    return { group, explode };
};
