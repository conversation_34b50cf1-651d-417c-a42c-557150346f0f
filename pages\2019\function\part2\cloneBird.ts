/*
 * @Author: hongbin
 * @Date: 2025-06-19 15:54:00
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-20 10:30:01
 * @Description:
 */

import { Main } from "../../main2";
import { BaseSkinnedMaterial } from "../../shader/BaseSkinnedMaterial";
import * as THREE from "three";
import { MapReflection } from "./handleBirdModel";

export const cloneBird = (self: Main, position: THREE.Vector3) => {
    const clone = self.bird!.clone();

    const mm = new BaseSkinnedMaterial({
        map: self.bird!.material.map,
    });

    clone.material = mm;

    clone.material.position.copy(position);
    clone.material.scale.set(0.6, 0.6, 0.6);

    clone.morphTargetInfluences = self.bird!.morphTargetInfluences;
    clone.morphTargetDictionary = self.bird!.morphTargetDictionary;

    self.helper.add(clone);

    self.addSelectedObjects(clone);

    // 阴影

    const ShadowPlane = MapReflection.ShadowPlane.mesh;

    if (ShadowPlane) {
        const clone_ShadowPlane = ShadowPlane.clone();

        clone_ShadowPlane.morphTargetDictionary = ShadowPlane.morphTargetDictionary;
        clone_ShadowPlane.morphTargetInfluences = ShadowPlane.morphTargetInfluences;

        clone_ShadowPlane.position.copy(position.multiplyScalar(0.6));

        clone_ShadowPlane.scale.multiplyScalar(0.6);

        if (!ShadowPlane.userData.clone) {
            ShadowPlane.userData.clone = [];
        }

        ShadowPlane.userData.clone.push((scaleCoefficient: number) => {
            clone_ShadowPlane.scale.x = 0.6 / scaleCoefficient;
        });

        self.helper.add(clone_ShadowPlane);
    }
};
