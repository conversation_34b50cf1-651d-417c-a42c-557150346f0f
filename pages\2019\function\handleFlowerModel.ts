/*
 * @Author: hongbin
 * @Date: 2025-06-15 17:03:33
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-15 21:33:07
 * @Description:
 */

import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Main } from "../main";
import * as THREE from "three";
import shader from "../shader";

export const handleFlowerModel = (self: Main, gltf: GLTF) => {
    const stem = gltf.scene.getObjectByName("tige") as Mesh;
    if (!stem) throw new Error("No stem");

    const base = gltf.scene.getObjectByName("base") as Mesh;
    if (!base) throw new Error("No base");

    const flower = gltf.scene.getObjectByName("flower") as Mesh;
    if (!flower) throw new Error("No flower");

    const flowerGroup = new THREE.Group();

    flowerGroup.add(stem);
    flowerGroup.add(flower);
    flowerGroup.add(base);
    flowerGroup.scale.setScalar(7);
    flowerGroup.position.set(0, -5, -25);

    // self.helper.add(flowerGroup);

    flower.position.set(0, 1, 0);
    // flower.position.set(0, 0, 0);
    flower.rotation.x = -1.4;

    self.stem = stem;
    self.base = base;
    self.flower = flower;

    self.flowerMap && (flower.material.map = self.flowerMap);

    stem.morphTargetInfluences && self.morphGUI(stem);
    flower.morphTargetInfluences && self.morphGUI(flower);

    const material = new THREE.ShaderMaterial({
        uniforms: {
            rotateTexture: { value: self.rTexture },
            color: { value: new THREE.Color("#5F9B8F") },
            scalePosX: { value: 1 },
            scalePosY: { value: 1 },
            pillar: { value: 1 },
            leaveProgress: { value: 0 },
            recordIndex: { value: 1 },
        },
        vertexShader: shader.rotatePosition.vt,
        fragmentShader: shader.rotatePosition.fm,
        side: 0,
        transparent: true,
    });

    self.helper.gui?.add(material.uniforms.leaveProgress, "value", 0, 1);

    self.stem!.material = material;

    // cloneBG.morphTargetInfluences[0] = 0;

    self.rTexture.needsUpdate = true;
};
