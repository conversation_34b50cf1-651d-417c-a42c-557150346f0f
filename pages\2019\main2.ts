/*
 * @Author: hongbin
 * @Date: 2025-06-17 12:40:51
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-20 20:42:34
 * @Description:
 */
import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "@/src/components/Three/Canvas";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import type { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import type { GUI } from "dat.gui";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import gsap from "gsap";

import { usedEffect } from "./function/usedEffect";
import { MapReflection, handleBirdModel } from "./function/part2/handleBirdModel";
import { BaseSkinnedMaterial } from "./shader/BaseSkinnedMaterial";
import { cloneBird } from "./function/part2/cloneBird";
import { TempAudioPlayback } from "./function/TempAudioPlayback";

type Mesh<T extends THREE.MeshBasicMaterial = THREE.MeshBasicMaterial> = THREE.Mesh<THREE.BufferGeometry, T>;

@Injectable
export class Main extends MainScreen {
    static instance: Main;
    clock = new THREE.Clock();
    iTime = { value: 1 };
    selectedObjects: Object3D[] = [];
    shaderOffsetIndex = 0;
    shaderOffsetArr = [
        new THREE.Vector2(0.5, 0),
        new THREE.Vector2(0, 0),
        new THREE.Vector2(0.5, 0.5),
        new THREE.Vector2(0, 0.5),
    ];
    mixer?: THREE.AnimationMixer;
    bird?: THREE.SkinnedMesh<THREE.BufferGeometry, BaseSkinnedMaterial>;
    action1?: THREE.AnimationAction;
    action2?: THREE.AnimationAction;
    fadeDuration = 1; // 动画淡入淡出时长
    // 蓄力进度 也是 gsap动画的载体
    gsapCarrier = {
        value: 0,
    };
    bgMusic?: HTMLAudioElement;

    constructor(public helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        Main.instance = this;

        this.init();
    }

    @MethodBaseSceneSet({
        addAxis: false,
        fov: 30,
        far: 1000,
        cameraPosition: new THREE.Vector3(-20, 60, -30),
        // cameraPosition: new THREE.Vector3(0, 1.1, 0.5),
        cameraTarget: new THREE.Vector3(5, 0, 0),
        useRoomLight: true,
    })
    async init() {
        this.helper.renderer.setPixelRatio(2);
        this.helper.renderer.toneMapping = THREE.NeutralToneMapping;

        usedEffect(this);

        // 加载模型等
        this.handleScene();

        // 背景音乐
        this.once(() => {
            TempAudioPlayback("/public/music/UI_MUSIC_STEM-3.mp3", 0.5, true);
        });
        // 背景噪音
        this.once(() => {
            TempAudioPlayback("/public/music/BG-Loop.mp3", 0.5, true);
        });
        // 加速音
        this.once(() => {
            this.bgMusic = TempAudioPlayback("/public/music/CLK-Speed_LOOP.mp3", 0, true);
        });
    }

    once(call: VoidFunction) {
        document.addEventListener(
            "mousedown",
            () => {
                call();
            },
            { once: true }
        );
    }

    @EventMesh.OnMouseDown(Main)
    onMouseDown(RayInfo: BackIntersection, _: undefined, event: MouseEvent) {
        // 点击目标需要是canvas
        if (this.action1 && this.action2) {
            gsap.killTweensOf(this.gsapCarrier); // 停止之前的动画

            gsap.to(this.gsapCarrier, {
                duration: this.fadeDuration,
                value: 1,
                ease: "power1.inOut",
                onUpdate: () => {
                    const weight = this.gsapCarrier.value;
                    if (this.action1 && this.action2) {
                        this.action1.setEffectiveWeight(1 - weight);
                        this.action2.setEffectiveWeight(weight);
                    }

                    this.bgMusic && (this.bgMusic.volume = weight);

                    // @ts-ignore
                    if (window.setProgress) window.setProgress(weight);
                },
            });
        }
    }

    // @EventMesh.OnMouseMove(Main)
    // onMouseMove(event: MouseEvent) {}

    @EventMesh.OnMouseUp(Main)
    onMouseUp(event: MouseEvent) {
        const curr = this.gsapCarrier.value;

        gsap.killTweensOf(this.gsapCarrier); // 停止之前的动画

        gsap.to(this.gsapCarrier, {
            // 动画时间不是完整时间 而是寻求最大时间和最大值之间的关系
            // 确保在未完整过渡动画时不是按照当前进度调度退场时间
            duration: curr,
            value: 0,
            ease: "power1.inOut",
            onUpdate: () => {
                const weight = this.gsapCarrier.value;
                if (this.action1 && this.action2) {
                    this.action1.setEffectiveWeight(1 - weight);
                    this.action2.setEffectiveWeight(weight);
                }

                this.bgMusic && (this.bgMusic.volume = weight);

                // @ts-ignore
                if (window.setProgress) window.setProgress(weight);
            },
        });
    }

    async handleScene() {
        EventMesh.setIntersectObjects([]);

        await this.loadModel();
    }

    @LoadGLTF("/public/models/鸟.glb")
    async loadModel(gltf?: GLTF) {
        if (gltf) {
            handleBirdModel(this, gltf);

            const sk_meshes = gltf.scene.getObjectsByProperty("type", "SkinnedMesh") as (typeof this.bird)[];

            this.bird = sk_meshes[0];

            // this.morphGUI(this.bird as THREE.Mesh);

            //创建动画
            const mixer = new THREE.AnimationMixer(gltf.scene);

            this.mixer = mixer;

            this.action1 = mixer.clipAction(gltf.animations[0]);
            this.action1.play();

            if (gltf.animations.length > 1) {
                this.action2 = mixer.clipAction(gltf.animations[1]);
                this.action2.play();
                // this.action2.enabled = true;
                this.action2.setEffectiveWeight(0);
                this.action2.clampWhenFinished = true;
                this.action2.setLoop(THREE.LoopOnce, 1);
            }

            // 克隆两个
            cloneBird(this, new THREE.Vector3(30, 0, -18));
            cloneBird(this, new THREE.Vector3(-10, 0, 30));
        }
    }

    // 蒙皮调试
    morphGUI(mesh: THREE.Mesh) {
        const gui = this.helper.gui!;

        if (!gui) return;

        const morph = gui.addFolder(mesh.name + "形态键");
        const morphTargetInfluences = mesh.morphTargetInfluences;

        if (morphTargetInfluences) {
            morphTargetInfluences[0] = 1;

            morphTargetInfluences.forEach((_, i) => {
                morph.add(morphTargetInfluences, "" + i, -1, 1, 0.01).name(`形态键${i}`);
            });

            morph.open();
        }
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {
        const delta = this.clock.getDelta();
        this.iTime.value += delta;

        const StorageStrength = this.gsapCarrier.value + 1;

        // 相机跟着晃动 晃动幅度跟随StorageStrength
        if (this.helper.camera) {
            this.helper.camera.position.x = -20 + (Math.sin(this.iTime.value * 120) * this.gsapCarrier.value) / 3;
            this.helper.camera.position.z = -30 + (Math.cos(this.iTime.value * 120) * this.gsapCarrier.value) / 3;
        }

        this.mixer && this.mixer.update(delta * StorageStrength);

        if (this.bird) {
            this.bird.morphTargetInfluences![0] = 0;
            this.bird.morphTargetInfluences![1] = (Math.sin(this.iTime.value * 4 * StorageStrength) * 0.5 + 0.5) * 0.2;
            this.bird.morphTargetInfluences![2] = 0;
            this.bird.morphTargetInfluences![3] = 0;
            this.bird.morphTargetInfluences![4] = Math.sin(this.iTime.value * 4 * StorageStrength) * 0.5 + 0.7;
            this.bird.morphTargetInfluences![5] = Math.sin(this.iTime.value * 4 * StorageStrength) * 0.5 + 0.7;
        }

        // 遍历MapReflection确保每个都有有效mesh
        for (const key in MapReflection) {
            const refl = MapReflection[key];
            // 都有形态键属性
            if (refl.mesh && refl.mesh.morphTargetInfluences) {
                const v = refl.mesh.morphTargetInfluences[0];

                if (key.includes("Planct")) {
                    //refl.mesh.morphTargetInfluences[0]限制值在0-1之间
                    // refl.mesh.morphTargetInfluences[0] = this.iTime.value;
                    refl.mesh.morphTargetInfluences[0] = (v + 0.07 * StorageStrength) % 1;
                    refl.mesh.morphTargetInfluences[1] = (v + 0.02 * StorageStrength) % 1;
                    refl.mesh.morphTargetInfluences[2] = (v + 0.03 * StorageStrength) % 1;
                    refl.mesh.morphTargetInfluences[3] = (v + 0.04 * StorageStrength) % 1;
                    refl.mesh.morphTargetInfluences[4] = (v + 0.05 * StorageStrength) % 1;
                } else if (refl.mesh && key == "ShadowPlane") {
                    this.shaderOffsetIndex += 0.34 * StorageStrength;
                    this.shaderOffsetIndex %= 3;

                    if (!this.gsapCarrier.value) {
                        refl.mesh.morphTargetInfluences[0] = Math.cos(this.iTime.value * StorageStrength) * 0.2;
                    } else {
                        const scaleCoefficient = StorageStrength;

                        refl.mesh.scale.x = 1 / scaleCoefficient;
                        refl.mesh.userData.clone.forEach((fn: (scaleCoefficient: number) => void) =>
                            fn(scaleCoefficient)
                        );
                    }

                    refl.mesh.morphTargetInfluences[0] = -this.gsapCarrier.value;

                    const offset = this.shaderOffsetArr[Math.floor(this.shaderOffsetIndex)];

                    refl.offset!.copy(offset);
                }
            } else if (refl.mesh && key == "Fish") {
                refl.mesh.position.z = (refl.mesh.position.z - 0.6 * StorageStrength) % 100;
            } else if (refl.mesh && key == "Clouds") {
                refl.offset!.x += 0.01 * StorageStrength;
            } else if (refl.mesh && key == "Traces") {
                refl.mesh.position.z = (refl.mesh.position.z - 1 * StorageStrength) % 100;
            }
        }
    }

    @ThreeHelper.AddGUI(Main)
    Gui(gui: GUI) {
        const toneMappingOptions = {
            None: THREE.NoToneMapping,
            Linear: THREE.LinearToneMapping,
            Reinhard: THREE.ReinhardToneMapping,
            Cineon: THREE.CineonToneMapping,
            ACESFilmic: THREE.ACESFilmicToneMapping,
            AgX: THREE.AgXToneMapping,
            Neutral: THREE.NeutralToneMapping,
            Custom: THREE.CustomToneMapping,
        };

        const params = {
            toneMapping: "Neutral" as keyof typeof toneMappingOptions,
        };

        const renderer = this.helper.renderer;
        const rendererFolder = gui.addFolder("Renderer");

        rendererFolder
            .add(params, "toneMapping", Object.keys(toneMappingOptions))

            .name("type")
            .onChange(function () {
                renderer.toneMapping = toneMappingOptions[params.toneMapping];
            });
    }

    appendRotateData() {}

    addSelectedObjects = (Object3D: Object3D) => {};
}
