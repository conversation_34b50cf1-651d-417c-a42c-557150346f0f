// Three.js r167 - Node System


diagnostic( off, derivative_uniformity );

// uniforms
@binding( 1 ) @group( 0 ) var nodeUniform8 : texture_2d<f32>;
@binding( 2 ) @group( 0 ) var nodeUniform10 : texture_2d<f32>;
struct objectStruct {
	nodeUniform1 : vec3<f32>,
	nodeUniform2 : f32,
	nodeUniform3 : f32,
	nodeUniform4 : f32,
	nodeUniform5 : vec3<f32>,
	nodeUniform6 : f32,
	nodeUniform9 : mat3x3<f32>,
	nodeUniform11 : mat3x3<f32>,
	nodeUniform12 : vec3<f32>,
	nodeUniform13 : vec3<f32>,
	nodeUniform14 : vec3<f32>,
	nodeUniform15 : vec3<f32>
};
@binding( 0 ) @group( 0 )
var<uniform> object : objectStruct;

// structs

struct OutputStruct {
	@location(0) color: vec4<f32>
};
var<private> output : OutputStruct;



// codes
fn LTC_EdgeVectorFormFactor ( v1 : vec3<f32>, v2 : vec3<f32> ) -> vec3<f32> {

	var nodeVar0 : f32;
	var nodeVar1 : f32;
	var nodeVar2 : f32;
	var nodeVar3 : f32;
	var nodeVar4 : f32;

	nodeVar1 = dot( v1, v2 );
	nodeVar0 = abs( nodeVar1 );
	nodeVar2 = ( ( ( ( nodeVar0 * 0.0145206 ) + 0.4965155 ) * nodeVar0 ) + 0.8543985 );
	nodeVar3 = ( ( ( nodeVar0 + 4.1616724 ) * nodeVar0 ) + 3.417594 );

	if ( ( nodeVar1 > 0.0 ) ) {

		nodeVar4 = ( nodeVar2 / nodeVar3 );

	} else {

		nodeVar4 = ( ( inverseSqrt( max( ( 1.0 - ( nodeVar1 * nodeVar1 ) ), 1e-7 ) ) * 0.5 ) - ( nodeVar2 / nodeVar3 ) );

	}


	return ( cross( v1, v2 ) * vec3<f32>( nodeVar4 ) );

}

fn LTC_ClippedSphereFormFactor ( f : vec3<f32> ) -> f32 {

	var nodeVar0 : f32;

	nodeVar0 = length( f );

	return max( ( ( ( nodeVar0 * nodeVar0 ) + f.z ) / ( nodeVar0 + 1.0 ) ), 0.0 );

}

fn LTC_Uv ( N : vec3<f32>, V : vec3<f32>, roughness : f32 ) -> vec2<f32> {

	var nodeVar0 : vec2<f32>;

	nodeVar0 = vec2<f32>( roughness, sqrt( ( 1.0 - clamp( dot( N, V ), 0.0, 1.0 ) ) ) );
	nodeVar0 = ( ( nodeVar0 * vec2<f32>( 0.984375 ) ) + vec2<f32>( 0.0078125 ) );

	return nodeVar0;

}


fn threejs_repeatWrapping( uv : vec2<f32>, dimension : vec2<u32> ) -> vec2<u32> {

	let uvScaled = vec2<u32>( uv * vec2<f32>( dimension ) );

	return ( ( uvScaled % dimension ) + dimension ) % dimension;

}

fn LTC_Evaluate ( N : vec3<f32>, V : vec3<f32>, P : vec3<f32>, mInv : mat3x3<f32>, p0 : vec3<f32>, p1 : vec3<f32>, p2 : vec3<f32>, p3 : vec3<f32> ) -> vec3<f32> {

	var nodeVar0 : vec3<f32>;
	var nodeVar1 : vec3<f32>;
	var nodeVar2 : vec3<f32>;
	var nodeVar3 : mat3x3<f32>;
	var nodeVar4 : vec3<f32>;
	var nodeVar5 : vec3<f32>;
	var nodeVar6 : vec3<f32>;
	var nodeVar7 : vec3<f32>;
	var nodeVar8 : vec3<f32>;
	var nodeVar9 : vec3<f32>;

	nodeVar0 = ( p1 - p0 );
	nodeVar1 = ( p3 - p0 );
	nodeVar2 = vec3<f32>( 0.0, 0.0, 0.0 );

	if ( ( dot( cross( nodeVar0, nodeVar1 ), ( P - p0 ) ) >= 0.0 ) ) {

		nodeVar4 = normalize( ( V - ( N * vec3<f32>( dot( V, N ) ) ) ) );
		nodeVar3 = ( mInv * transpose( mat3x3<f32>( nodeVar4, ( - cross( N, nodeVar4 ) ), N ) ) );
		nodeVar5 = normalize( ( nodeVar3 * ( p0 - P ) ) );
		nodeVar6 = normalize( ( nodeVar3 * ( p1 - P ) ) );
		nodeVar7 = normalize( ( nodeVar3 * ( p2 - P ) ) );
		nodeVar8 = normalize( ( nodeVar3 * ( p3 - P ) ) );
		nodeVar9 = vec3<f32>( 0.0, 0.0, 0.0 );
		nodeVar9 = ( nodeVar9 + LTC_EdgeVectorFormFactor( nodeVar5, nodeVar6 ) );
		nodeVar9 = ( nodeVar9 + LTC_EdgeVectorFormFactor( nodeVar6, nodeVar7 ) );
		nodeVar9 = ( nodeVar9 + LTC_EdgeVectorFormFactor( nodeVar7, nodeVar8 ) );
		nodeVar9 = ( nodeVar9 + LTC_EdgeVectorFormFactor( nodeVar8, nodeVar5 ) );
		nodeVar2 = vec3<f32>( LTC_ClippedSphereFormFactor( nodeVar9 ) );
		

	}


	return nodeVar2;

}

fn DFGApprox ( roughness : f32, dotNV : vec3<f32> ) -> vec2<f32> {

	var nodeVar0 : vec4<f32>;

	nodeVar0 = ( ( roughness * vec4<f32>( -1.0, -0.0275, -0.572, 0.022 ) ) + vec4<f32>( 1.0, 0.0425, 1.04, -0.04 ) );

	return ( ( vec3<f32>( vec2<f32>( -1.04, 1.04 ), 0.0 ) * ( ( min( vec3<f32>( ( nodeVar0.x * nodeVar0.x ) ), exp2( ( dotNV * vec3<f32>( -9.28 ) ) ) ) * vec3<f32>( nodeVar0.x ) ) + vec3<f32>( nodeVar0.y ) ) ) + vec3<f32>( nodeVar0.zw, 0.0 ) ).xy;

}



@fragment
fn main( @location( 0 ) v_normalView : vec3<f32>,
	@location( 1 ) v_positionView : vec3<f32>,
	@location( 2 ) v_positionViewDirection : vec3<f32>,
	@location( 3 ) vInstanceIndexOpacity : f32,
	@location( 4 ) vInstanceOpacity : f32,
	@location( 5 ) nodeVarying5 : vec3<f32>,
	@builtin( front_facing ) isFront : bool ) -> OutputStruct {

	// vars
	
	var transformedNormalView : vec3<f32>;
	var normalView : vec3<f32>;
	var DiffuseColor : vec4<f32>;
	var Metalness : f32;
	var Roughness : f32;
	var nodeVar5 : vec3<f32>;
	var SpecularColor : vec3<f32>;
	var SpecularF90 : f32;
	var EmissiveColor : vec3<f32>;
	var Output : vec4<f32>;
	var nodeVar10 : vec3<f32>;
	var nodeVar11 : vec4<f32>;
	var positionViewDirection : vec3<f32>;
	var nodeVar13 : vec4<f32>;
	var nodeVar14 : vec4<f32>;
	var nodeVar15 : vec4<f32>;
	var nodeVar16 : mat3x3<f32>;
	var nodeVar17 : vec3<f32>;
	var directSpecular : vec3<f32>;
	var nodeVar19 : vec3<f32>;
	var nodeVar20 : vec3<f32>;
	var nodeVar21 : vec3<f32>;
	var nodeVar22 : vec3<f32>;
	var directDiffuse : vec3<f32>;
	var indirectDiffuse : vec3<f32>;
	var irradiance : vec3<f32>;
	var singleScattering : vec3<f32>;
	var nodeVar27 : vec3<f32>;
	var multiScattering : vec3<f32>;
	var nodeVar29 : vec3<f32>;
	var nodeVar30 : f32;
	var indirectSpecular : vec3<f32>;
	var radiance : vec3<f32>;
	var iblIrradiance : vec3<f32>;
	var nodeVar34 : vec3<f32>;
	var nodeVar35 : vec3<f32>;
	var ambientOcclusion : f32;
	var totalDiffuse : vec3<f32>;
	var totalSpecular : vec3<f32>;
	var outgoingLight : vec3<f32>;
	var nodeVar40 : vec4<f32>;


	// flow
	// code

	normalView = normalize( v_normalView );
	transformedNormalView = ( normalView * vec3<f32>( ( ( f32( isFront ) * 2.0 ) - 1.0 ) ) );
	DiffuseColor = vec4<f32>( object.nodeUniform1, 1.0 );
	DiffuseColor.w = ( ( ( DiffuseColor.w * object.nodeUniform2 ) * vInstanceIndexOpacity ) * vInstanceOpacity );
	DiffuseColor.w = 1.0;
	Metalness = object.nodeUniform3;
	nodeVar5 = max( abs( dpdx( nodeVarying5 ) ), abs( - dpdy( nodeVarying5 ) ) );
	Roughness = min( ( max( object.nodeUniform4, 0.0525 ) + max( max( nodeVar5.x, nodeVar5.y ), nodeVar5.z ) ), 1.0 );
	SpecularColor = mix( vec3<f32>( 0.04, 0.04, 0.04 ), DiffuseColor.xyz, Metalness );
	SpecularF90 = 1.0;
	DiffuseColor = vec4<f32>( ( DiffuseColor.xyz * vec3<f32>( ( 1.0 - object.nodeUniform3 ) ) ), DiffuseColor.w );
	EmissiveColor = ( object.nodeUniform5 * vec3<f32>( object.nodeUniform6 ) );
	nodeVar10 = v_positionView;
	positionViewDirection = normalize( v_positionViewDirection );
	nodeVar13 = textureLoad( nodeUniform8, threejs_repeatWrapping( ( object.nodeUniform9 * vec3<f32>( LTC_Uv( transformedNormalView, positionViewDirection, Roughness ), 1.0 ) ).xy, textureDimensions( nodeUniform8, 0 ) ), i32( 0 ) );
	nodeVar11 = nodeVar13;
	nodeVar15 = textureLoad( nodeUniform10, threejs_repeatWrapping( ( object.nodeUniform11 * vec3<f32>( LTC_Uv( transformedNormalView, positionViewDirection, Roughness ), 1.0 ) ).xy, textureDimensions( nodeUniform10, 0 ) ), i32( 0 ) );
	nodeVar14 = nodeVar15;
	nodeVar16 = mat3x3<f32>( vec3<f32>( nodeVar11.x, 0.0, nodeVar11.y ), vec3<f32>( 0.0, 1.0, 0.0 ), vec3<f32>( nodeVar11.z, 0.0, nodeVar11.w ) );
	nodeVar17 = ( ( SpecularColor * vec3<f32>( nodeVar14.x ) ) + ( ( vec3<f32>( 1.0 ) - SpecularColor ) * vec3<f32>( nodeVar14.y ) ) );
	directSpecular = vec3<f32>( 0.0, 0.0, 0.0 );
	nodeVar19 = ( ( object.nodeUniform13 + object.nodeUniform14 ) - object.nodeUniform15 );
	nodeVar20 = ( ( object.nodeUniform13 - object.nodeUniform14 ) - object.nodeUniform15 );
	nodeVar21 = ( ( object.nodeUniform13 - object.nodeUniform14 ) + object.nodeUniform15 );
	nodeVar22 = ( ( object.nodeUniform13 + object.nodeUniform14 ) + object.nodeUniform15 );
	directSpecular = ( directSpecular + ( ( object.nodeUniform12 * nodeVar17 ) * LTC_Evaluate( transformedNormalView, positionViewDirection, nodeVar10, nodeVar16, nodeVar19, nodeVar20, nodeVar21, nodeVar22 ) ) );
	directDiffuse = vec3<f32>( 0.0, 0.0, 0.0 );
	directDiffuse = ( vec4<f32>( directDiffuse, 1.0 ) + ( ( vec4<f32>( object.nodeUniform12, 1.0 ) * DiffuseColor ) * vec4<f32>( LTC_Evaluate( transformedNormalView, positionViewDirection, nodeVar10, mat3x3<f32>( 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0 ), nodeVar19, nodeVar20, nodeVar21, nodeVar22 ), 1.0 ) ) ).xyz;
	indirectDiffuse = vec3<f32>( 0.0, 0.0, 0.0 );
	irradiance = vec3<f32>( 0.0, 0.0, 0.0 );
	indirectDiffuse = ( vec4<f32>( indirectDiffuse, 1.0 ) + ( vec4<f32>( irradiance, 1.0 ) * ( DiffuseColor * vec4<f32>( 0.3183098861837907 ) ) ) ).xyz;
	singleScattering = vec3<f32>( 0.0, 0.0, 0.0 );
	nodeVar27 = ( ( SpecularColor * vec3<f32>( DFGApprox( Roughness, vec3<f32>( clamp( dot( transformedNormalView, positionViewDirection ), 0.0, 1.0 ) ) ).x ) ) + vec3<f32>( ( SpecularF90 * DFGApprox( Roughness, vec3<f32>( clamp( dot( transformedNormalView, positionViewDirection ), 0.0, 1.0 ) ) ).y ) ) );
	singleScattering = ( singleScattering + nodeVar27 );
	multiScattering = vec3<f32>( 0.0, 0.0, 0.0 );
	nodeVar29 = ( SpecularColor + ( ( vec3<f32>( 1.0 ) - SpecularColor ) * vec3<f32>( 0.047619 ) ) );
	nodeVar30 = ( 1.0 - ( DFGApprox( Roughness, vec3<f32>( clamp( dot( transformedNormalView, positionViewDirection ), 0.0, 1.0 ) ) ).x + DFGApprox( Roughness, vec3<f32>( clamp( dot( transformedNormalView, positionViewDirection ), 0.0, 1.0 ) ) ).y ) );
	multiScattering = ( multiScattering + ( ( ( nodeVar27 * nodeVar29 ) / ( vec3<f32>( 1.0 ) - ( vec3<f32>( nodeVar30 ) * nodeVar29 ) ) ) * vec3<f32>( nodeVar30 ) ) );
	indirectSpecular = vec3<f32>( 0.0, 0.0, 0.0 );
	radiance = vec3<f32>( 0.0, 0.0, 0.0 );
	indirectSpecular = ( indirectSpecular + ( radiance * singleScattering ) );
	iblIrradiance = vec3<f32>( 0.0, 0.0, 0.0 );
	nodeVar34 = ( iblIrradiance * vec3<f32>( 0.3183098861837907 ) );
	indirectSpecular = ( indirectSpecular + ( multiScattering * nodeVar34 ) );
	nodeVar35 = ( singleScattering + multiScattering );
	indirectDiffuse = ( vec4<f32>( indirectDiffuse, 1.0 ) + ( ( DiffuseColor * vec4<f32>( ( 1.0 - max( max( nodeVar35.x, nodeVar35.y ), nodeVar35.z ) ) ) ) * vec4<f32>( nodeVar34, 1.0 ) ) ).xyz;
	ambientOcclusion = 1.0;
	indirectDiffuse = ( indirectDiffuse * vec3<f32>( ambientOcclusion ) );
	indirectSpecular = ( indirectSpecular * vec3<f32>( clamp( ( ambientOcclusion - ( 1.0 - pow( ( clamp( dot( transformedNormalView, positionViewDirection ), 0.0, 1.0 ) + ambientOcclusion ), exp2( ( - ( 1.0 - ( Roughness * -16.0 ) ) ) ) ) ) ), 0.0, 1.0 ) ) );
	totalDiffuse = vec3<f32>( 0.0, 0.0, 0.0 );
	totalDiffuse = ( directDiffuse + indirectDiffuse );
	totalSpecular = vec3<f32>( 0.0, 0.0, 0.0 );
	totalSpecular = ( directSpecular + indirectSpecular );
	outgoingLight = vec3<f32>( 0.0, 0.0, 0.0 );
	outgoingLight = ( totalDiffuse + totalSpecular );
	nodeVar40 = max( vec4<f32>( ( outgoingLight + EmissiveColor ), DiffuseColor.w ), vec4<f32>( 0.0 ) );
	Output = nodeVar40;

	// result

	output.color = nodeVar40;

	return output;

}
