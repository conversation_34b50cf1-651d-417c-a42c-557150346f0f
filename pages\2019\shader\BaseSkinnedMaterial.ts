/*
 * @Author: hongbin
 * @Date: 2025-06-19 15:27:31
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-19 15:40:27
 * @Description:
 */
import * as THREE from "three";

export class BaseSkinnedMaterial extends THREE.MeshBasicMaterial {
    position = new THREE.Vector3(0, 0, 0);
    scale = new THREE.Vector3(1, 1, 1);

    constructor(...args: ConstructorParameters<typeof THREE.MeshBasicMaterial>) {
        super(...args);
    }

    onBeforeCompile = (shader: THREE.WebGLProgramParametersWithUniforms) => {
        shader.uniforms.iPosition = { value: this.position };
        shader.uniforms.iScale = { value: this.scale };

        shader.vertexShader = shader.vertexShader.replace(
            "void main() {",
            `
            uniform vec3 iPosition;
            uniform vec3 iScale;
            
            void main() {
            `
        );

        shader.vertexShader = shader.vertexShader.replace(
            "#include <project_vertex>",
            `
            transformed += iPosition;
            transformed *= iScale;

            #include <project_vertex>
            `
        );
    };
}
