varying vec2 vUv;
varying vec3 vPos;
varying vec3 VVNormal;
varying vec3 vViewPosition;
uniform float scalePosX;
uniform float scalePosY;
uniform float pillar;

#include <common>
#include <skinning_pars_vertex>
#include <normal_pars_vertex>
#include <morphtarget_pars_vertex>

void main() {
    vUv = uv;

    #include <morphinstance_vertex>
	#include <morphcolor_vertex>

	#include <beginnormal_vertex>
    #include <morphnormal_vertex>
	#include <skinbase_vertex>
	#include <skinnormal_vertex>
	#include <defaultnormal_vertex>
	#include <normal_vertex>

	#include <begin_vertex>
	#include <morphtarget_vertex>

    // 枝叶部分
    // if(abs(transformed.x) > 0.011) {
    //     float scale = clamp(transformed.z * 2.5, 0., 1.);
    //     transformed.x *= scale;
    //     transformed.y *= scale;

    //     // transformed.x *= scalePosX * (smoothstep(rand(vec2(transformed.z)), 0.995, 1.));
    //     transformed.x *= scalePosX;
    //     // transformed.y *= scalePosY;
    //     // transformed.y *= smoothstep(rand(vec2(transformed.z)), 0.99, 1.1);
    // } else {
    //     transformed.x *= scalePosX * pillar * (smoothstep(0.1, 0.12, rand(uv)));
    //     // transformed.y *= scalePosY * pillar;
    // }

    // vNormal = normalMatrix * normal;

    // vec4 mvPosition = vec4(transformed, 1.0);

    // mvPosition = modelViewMatrix * mvPosition;

    // vViewPosition = - mvPosition.xyz;

    vPos = position;

    transformed.x *= 1.03;
    transformed.z *= 1.003;
    transformed.y *= 1.04;

    if(vPos.z < 0.149) {
        if(length(vPos.xz) > 0.032) {
            transformed.x *= 1.2;
            transformed.z *= 1.03;
            transformed.y *= 1.3;
        }
    }

	#include <skinning_vertex>

	#include <project_vertex>

    VVNormal = normalize(normal);

    vViewPosition = -mvPosition.xyz;
}