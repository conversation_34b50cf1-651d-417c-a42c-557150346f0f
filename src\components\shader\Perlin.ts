/*
 * @Author: hongbin
 * @Date: 2023-10-16 16:26:31
 * @LastEditors: hongbin
 * @LastEditTime: 2023-10-17 14:28:44
 * @Description:柏林噪声
 */

import { ThreeHelper } from "@/src/ThreeHelper";
import {
    BoxGeometry,
    BufferGeometry,
    DoubleSide,
    IcosahedronGeometry,
    Mesh,
    MeshStandardMaterial,
    PlaneGeometry,
    ShaderMaterial,
    SphereGeometry,
    Uniform,
} from "three";

/** 柏林噪声片段 */
const perlinNoiseFragment = `
//	Classic Perlin 3D Noise 
//	by <PERSON>
//
vec4 permute(vec4 x){return mod(((x*34.0)+1.0)*x, 289.0);}
vec4 taylorInvSqrt(vec4 r){return 1.79284291400159 - 0.85373472095314 * r;}
vec3 fade(vec3 t) {return t*t*t*(t*(t*6.0-15.0)+10.0);}

float noise(vec3 P){
  vec3 Pi0 = floor(P); // Integer part for indexing
  vec3 Pi1 = Pi0 + vec3(1.0); // Integer part + 1
  Pi0 = mod(Pi0, 289.0);
  Pi1 = mod(Pi1, 289.0);
  vec3 Pf0 = fract(P); // Fractional part for interpolation
  vec3 Pf1 = Pf0 - vec3(1.0); // Fractional part - 1.0
  vec4 ix = vec4(Pi0.x, Pi1.x, Pi0.x, Pi1.x);
  vec4 iy = vec4(Pi0.yy, Pi1.yy);
  vec4 iz0 = Pi0.zzzz;
  vec4 iz1 = Pi1.zzzz;

  vec4 ixy = permute(permute(ix) + iy);
  vec4 ixy0 = permute(ixy + iz0);
  vec4 ixy1 = permute(ixy + iz1);

  vec4 gx0 = ixy0 / 7.0;
  vec4 gy0 = fract(floor(gx0) / 7.0) - 0.5;
  gx0 = fract(gx0);
  vec4 gz0 = vec4(0.5) - abs(gx0) - abs(gy0);
  vec4 sz0 = step(gz0, vec4(0.0));
  gx0 -= sz0 * (step(0.0, gx0) - 0.5);
  gy0 -= sz0 * (step(0.0, gy0) - 0.5);

  vec4 gx1 = ixy1 / 7.0;
  vec4 gy1 = fract(floor(gx1) / 7.0) - 0.5;
  gx1 = fract(gx1);
  vec4 gz1 = vec4(0.5) - abs(gx1) - abs(gy1);
  vec4 sz1 = step(gz1, vec4(0.0));
  gx1 -= sz1 * (step(0.0, gx1) - 0.5);
  gy1 -= sz1 * (step(0.0, gy1) - 0.5);

  vec3 g000 = vec3(gx0.x,gy0.x,gz0.x);
  vec3 g100 = vec3(gx0.y,gy0.y,gz0.y);
  vec3 g010 = vec3(gx0.z,gy0.z,gz0.z);
  vec3 g110 = vec3(gx0.w,gy0.w,gz0.w);
  vec3 g001 = vec3(gx1.x,gy1.x,gz1.x);
  vec3 g101 = vec3(gx1.y,gy1.y,gz1.y);
  vec3 g011 = vec3(gx1.z,gy1.z,gz1.z);
  vec3 g111 = vec3(gx1.w,gy1.w,gz1.w);

  vec4 norm0 = taylorInvSqrt(vec4(dot(g000, g000), dot(g010, g010), dot(g100, g100), dot(g110, g110)));
  g000 *= norm0.x;
  g010 *= norm0.y;
  g100 *= norm0.z;
  g110 *= norm0.w;
  vec4 norm1 = taylorInvSqrt(vec4(dot(g001, g001), dot(g011, g011), dot(g101, g101), dot(g111, g111)));
  g001 *= norm1.x;
  g011 *= norm1.y;
  g101 *= norm1.z;
  g111 *= norm1.w;

  float n000 = dot(g000, Pf0);
  float n100 = dot(g100, vec3(Pf1.x, Pf0.yz));
  float n010 = dot(g010, vec3(Pf0.x, Pf1.y, Pf0.z));
  float n110 = dot(g110, vec3(Pf1.xy, Pf0.z));
  float n001 = dot(g001, vec3(Pf0.xy, Pf1.z));
  float n101 = dot(g101, vec3(Pf1.x, Pf0.y, Pf1.z));
  float n011 = dot(g011, vec3(Pf0.x, Pf1.yz));
  float n111 = dot(g111, Pf1);

  vec3 fade_xyz = fade(Pf0);
  vec4 n_z = mix(vec4(n000, n100, n010, n110), vec4(n001, n101, n011, n111), fade_xyz.z);
  vec2 n_yz = mix(n_z.xy, n_z.zw, fade_xyz.y);
  float n_xyz = mix(n_yz.x, n_yz.y, fade_xyz.x); 
  return 2.2 * n_xyz;
}
`;
/** 模糊取余片段 */
const smoothModFragment = `
/* 
* SMOOTH MOD
* - authored by @charstiles -
* based on https://math.stackexchange.com/questions/2491494/does-there-exist-a-smooth-approximation-of-x-bmod-y
* (axis) input axis to modify
* (amp) amplitude of each edge/tip
* (rad) radius of each edge/tip
* returns => smooth edges
*/

float smoothMod(float axis, float amp, float rad){
    float top = cos(PI * (axis / amp)) * sin(PI * (axis / amp));
    float bottom = pow(sin(PI * (axis / amp)), 2.0) + pow(rad, 2.0);
    float at = atan(top / bottom);
    return amp * (1.0 / 2.0) - (1.0 / PI) * at;
}
`;
/** 区间映射片段 */
const fitFragment = `
float fit (float unscaled, float originalMin,float originalMax,float minAllowed, float maxAllowed) {
    return (maxAllowed - minAllowed) * (unscaled - originalMin) / (originalMax - originalMin) + minAllowed;
}
`;

export class PerlinNoise {
    material: ShaderMaterial | MeshStandardMaterial;
    mesh: Mesh<BufferGeometry, typeof this.material>;
    appendUniforms: ShaderMaterial["uniforms"] = {
        iTime: { value: 3 },
        iStepCount: { value: 3 },
    };

    constructor(private gui: ThreeHelper["gui"]) {
        // this.material = this.ReplaceMaterial();
        // this.mesh = new Mesh(new SphereGeometry(3, 400, 400), this.material);
        // this.mesh.onAfterRender = () => {
        //     this.appendUniforms.iTime.value += 0.01;
        // };
        // 使用 ShaderMaterial 不受光照影响 失去物理效果
        this.material = this.CustomMaterial();
        // this.mesh = new Mesh(new SphereGeometry(3, 400, 400), this.material);
        this.mesh = new Mesh(new IcosahedronGeometry(3, 100), this.material);
        this.mesh.onAfterRender = () => {
            (this.material as ShaderMaterial).uniforms.iTime.value += 0.01;
        };
    }

    CustomMaterial(): ShaderMaterial {
        const material = new ShaderMaterial({
            side: DoubleSide,
            uniforms: {
                iTime: { value: 1 },
            },
            vertexShader: `
            uniform float iTime;
            varying vec3 vNormal;
            varying vec3 vColor;
            #define PI 3.141592653589793
            ${perlinNoiseFragment}
            ${smoothModFragment}
            ${fitFragment}
            void main() {
                vec3 transformed = position;
                vNormal = normal;
                //vec3 myNormal = normal * 3. ; 
                vec3 myNormal = normal ;
                myNormal.y += iTime;
                float noiseValue = noise(myNormal) ;
                float pattern = fit(smoothMod(noiseValue * 5.,1.0,1.5),0.4,0.6,0.,1.);
                transformed += vec3(pattern) * normal;
                vColor = vec3(pattern);
                vec4 modelViewPosition = modelViewMatrix * vec4(transformed, 1.0);
                gl_Position = projectionMatrix * modelViewPosition;
            }`,
            fragmentShader: `
            varying vec3 vNormal;
            varying vec3 vColor;
            void main() {
                vec3 color = vec3(vColor);
                gl_FragColor = vec4(color, 1.0);
            }`,
        });

        return material;
    }

    ReplaceMaterial(): MeshStandardMaterial {
        const material = new MeshStandardMaterial({
            color: "#aaa",
            emissive: "#ff3311",
            metalness: 0.5,
            roughness: 0.5,
        });
        this.gui
            ?.add(<any>material, "metalness", 0, 1)
            .step(0.01)
            .name("金属度");
        this.gui
            ?.add(<any>material, "roughness", 0, 1)
            .step(0.01)
            .name("粗糙度");
        this.gui
            ?.add(<any>this.appendUniforms.iStepCount, "value", 1, 100)
            .step(0.01)
            .name("分段数量");
        material.onBeforeCompile = (shader) => {
            Object.assign(shader.uniforms, this.appendUniforms);
            shader.vertexShader = shader.vertexShader.replace(
                "#include <common>",
                `
                #include <common>
                uniform float iTime;
                uniform float iStepCount;
                #define PI 3.141592653589793
                ${perlinNoiseFragment}
                ${smoothModFragment}
                ${fitFragment}
            `
            );
            shader.vertexShader = shader.vertexShader.replace(
                "#include <begin_vertex>",
                `
                #include <begin_vertex>
                vec3 myNormal = normal;
                myNormal.y += iTime;
                float noiseValue = noise(myNormal);
                float pattern = fit(smoothMod(noiseValue * iStepCount,1.0,1.5),0.4,0.6,0.,1.);
                transformed += vec3(pattern) * normal;
            `
            );
        };

        return material;
    }
}
