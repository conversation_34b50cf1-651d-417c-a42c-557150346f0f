/*
 * @Author: hongbin
 * @Date: 2025-07-05 18:20:14
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-06 15:32:48
 * @Description:
 */
import * as THREE from "three";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass";
import { Main } from "../main";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";

export const appendRipplePass = (self: Main) => {
    const uniforms = {
        tDiffuse: { value: null },
        center: self.iRipple,
        time: self.rippleTime,
        // amplitude: { value: 0.03 },
        amplitude: self.iAmplitude,
        freq: { value: 40.0 },
        decay: { value: 5.0 },
        duration: { value: 1.5 }, // 涟漪持续时间
    };

    const folder = self.helper.gui?.addFolder("涟漪");

    if (folder) {
        folder.add(self.rippleTime, "value", -1, 2).step(0.01).name("涟漪");
        folder.add(self.iRipple.value, "x", 0, 1).step(0.01).name("x");
        folder.add(self.iRipple.value, "y", 0, 1).step(0.01).name("y");
        folder.add(self.iAmplitude, "value").name("amplitude");
        folder.add(uniforms.freq, "value").name("freq");
        folder.add(uniforms.decay, "value").name("decay");
    }

    const pass = new ShaderPass(
        new THREE.ShaderMaterial({
            uniforms: uniforms,
            vertexShader: `
                    varying vec2 vUv;

                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.0);
                    }
                `,
            fragmentShader: /*glsl*/ `
                uniform sampler2D tDiffuse;
                uniform vec2 center;
                uniform float time, amplitude, duration;

                varying vec2 vUv;

                void main() {
                    vec2 uv = vUv;
                    float dist = distance(uv, center);
                    float normT = time / duration;

                    if (normT > 1.0) {
                        gl_FragColor = texture2D(tDiffuse, uv);
                        return;
                    }

                    // 波纹 radius：使用 sin 函数先收缩再爆炸
                    // float waveRadius = sin(normT * 3.1415926);
                    float waveRadius = normT;
                    float thickness = 0.05;

                    float ring = smoothstep(waveRadius + thickness, waveRadius, dist) *
                                smoothstep(waveRadius - thickness, waveRadius, dist);

                    // 前半收缩（负向扰动），后半爆炸（正向扰动）
                    // float direction = normT < 0.5 ? -1.0 : 1.0;
                    float strength = ring * amplitude;

                    float inn = (normT > 0.0 ? 1.0 : -8.0);

                    vec2 dir = dist > 0.0 ? normalize(uv - center) : vec2(0.0);
                    uv += inn * dir * strength;

                    gl_FragColor = texture2D(tDiffuse, uv);
                }

                `,

            defines: {},
        }),
        "tDiffuse"
    );

    self.appendPass(pass);

    // TRANS_BLOOM_SCENE = 2;
    // transBloomLayer = new THREE.Layers();
    // this.transBloomLayer.set(UnrealBloomEffect.TRANS_BLOOM_SCENE);

    // const transitionComposer = new EffectComposer(self.helper.renderer);
    // const renderScene = new RenderPass(self.helper.scene, self.helper.camera);

    // transitionComposer.addPass(renderScene);

    // const appendPass = new ShaderPass(
    //     new THREE.ShaderMaterial({
    //         uniforms: {
    //             baseTexture: { value: null },
    //             bloomTexture: {
    //                 value: self.finalComposer?.renderTarget2.texture,
    //             },
    //         },
    //         vertexShader: `varying vec2 vUv;

    //                     void main() {
            
    //                         vUv = uv;
            
    //                         gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
            
    //                     }`,
    //         fragmentShader: `
    //                     uniform sampler2D baseTexture;
    //                     uniform sampler2D bloomTexture;
            
    //                     varying vec2 vUv;
            
    //                     void main() {
            
    //                         // gl_FragColor = ( texture2D( baseTexture, vUv ) +  texture2D( bloomTexture, vUv ) );

    //                         vec4 base = texture2D( baseTexture, vUv );
    //                         vec4 bloom = texture2D( bloomTexture, vUv );
                            
    //                         // gl_FragColor.rgb = base.rgb;

    //                         if(base.r > 0.0){
    //                         }
                            
    //                         gl_FragColor.rgb = mix( bloom.rgb, base.rgb, base.r );
                            
    //                         gl_FragColor.a = 1.;
    //                     }`,
    //         defines: {},
    //     }),
    //     "baseTexture"
    // );

    // transitionComposer.addPass(appendPass);
    // transitionComposer.renderToScreen = true;

    // self.finalComposer && (self.finalComposer.renderToScreen = false);

    // const prevRender = self.helper.render;

    // self.helper.render = () => {
    //     self.CircularTruncation && (self.CircularTruncation.visible = false);

    //     prevRender();

    //     self.helper.scene.traverse((obj) => {
    //         obj.type == "Mesh" && (obj.visible = false);
    //     });

    //     self.CircularTruncation && (self.CircularTruncation.visible = true);

    //     transitionComposer.render();

    //     self.helper.scene.traverse((obj) => {
    //         obj.type == "Mesh" && (obj.visible = true);
    //     });
    // };

    // scene.traverse((obj) =>
    // this.darkenNonBloomed(obj, this.transBloomLayer)
    // );
    // transitionComposer.render();
    // scene.traverse(this.restoreMaterial);
};
// fragmentShader: /*glsl*/ `
//     uniform sampler2D tDiffuse;
//     uniform vec2 center;
//     uniform float time, amplitude, freq, decay, duration;

//     varying vec2 vUv;

//     void main(){
//         vec2 uv = vUv;
//         float dist = distance(uv, center);
//         float normT = time / duration;

//         // 时间超过设置，则输出原图
//         if(normT > 1.0){
//             gl_FragColor = texture2D(tDiffuse, uv);
//             return;
//         }

//         // 圆形正弦波 + 衰减
//         float ripple = sin(dist * freq - time * 6.2831) * exp(-dist * decay);
//         float strength = amplitude * ripple * (1.0 - normT);

//         // 注意：若 dist == 0，dir 无意义，加入防护
//         vec2 dir = dist > 0.0 ? normalize(uv - center) : vec2(0.0);

//         uv += dir * strength;

//         gl_FragColor = texture2D(tDiffuse, uv);
//     }

// `,
