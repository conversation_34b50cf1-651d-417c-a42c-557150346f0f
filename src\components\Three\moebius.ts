/*
 * @Author: hong<PERSON>
 * @Date: 2024-09-07 09:56:39
 * @LastEditors: hongbin
 * @LastEditTime: 2024-10-25 11:41:54
 * @Description: 莫比乌斯风格渲染
 */
import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "./Canvas";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import type { GUI } from "dat.gui";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass.js";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass.js";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass.js";
import { FXAAShader } from "three/examples/jsm/shaders/FXAAShader";
import { SMAAPass } from "three/examples/jsm/postprocessing/SMAAPass";
import { MoebiusPass, MoebiusPassOutput, MoebiusPassSamplerMode } from "@/src/ThreeHelper/shader/moebius/moebius";
import Color4 from "three/src/renderers/common/Color4";
import { DepthShader } from "@/src/ThreeHelper/shader/DepthShader";
import { vertex } from "@/src/ThreeHelper/addons/vsm.glsl";
import { NormalShaderMaterial } from "@/src/ThreeHelper/shader/material/Normal";
import { SpecularShaderMaterial } from "@/src/ThreeHelper/shader/material/Specular";
import gsap from "gsap";
import { KeyBoardControls } from "@/src/ThreeHelper/utils/KeyBoardControls";

@Injectable
// @ThreeHelper.useWebGPU
export class Main extends MainScreen {
    static instance: Main;
    effectComposer?: EffectComposer;
    pointLight?: THREE.PointLight;
    moebiusPass?: MoebiusPass;
    Doors: THREE.Object3D[] = [];
    passed = false;
    dir = -1;
    clock = new THREE.Clock();

    constructor(private helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        this.init();
        Main.instance = this;
    }

    @MethodBaseSceneSet({
        addAxis: false,
        // cameraPosition: new THREE.Vector3(0, 4, 13),
        // cameraTarget: new THREE.Vector3(0, 3, 0),
        cameraPosition: new THREE.Vector3(-3, 4, 13),
        cameraTarget: new THREE.Vector3(-2, 3, 10),
        useRoomLight: false,
        near: 0.1,
        far: 800,
    })
    init() {
        this.helper.renderer.setPixelRatio(window.devicePixelRatio);
        // this.helper.renderer.setPixelRatio(window.devicePixelRatio+1);
        this.helper.renderer.setPixelRatio(2);

        // this.helper.setBackgroundHDR("/public/env/sunflowers_puresky_2k/");
        // this.helper.setBackgroundNode("/public/env/industrial_sunset_puresky_2k.jpg");

        const light = new THREE.PointLight(0xffffff, 150, 200, 2);
        light.position.set(10, 10, 0);
        // {
        //     const light = new THREE.PointLight(0xffffff, 15, 200, 2);
        //     light.position.set(-10, 10, 0);
        //     this.helper.scene.add(light);
        // }
        this.pointLight = light;

        // const sphereSize = 1;
        // const pointLightHelper = new THREE.PointLightHelper(light, sphereSize);
        // this.helper.scene.add(pointLightHelper);

        {
            const sphere = this.helper.create.sphere(0.3, 12, 12);
            this.helper.add(sphere.mesh);
            sphere.mesh.position.copy(light.position);
            sphere.material(new THREE.MeshBasicMaterial());
        }

        this.helper.scene.add(light);

        light.castShadow = true;
        light.shadow.mapSize.width = 1024 * 2;
        light.shadow.mapSize.height = 1024 * 2;
        light.shadow.camera.near = 0.3;
        light.shadow.camera.far = 200;
        light.shadow.bias = -0.0012;
        light.shadow.intensity = 1000;

        const shadowFolder = this.helper.gui?.addFolder("shadow");
        shadowFolder?.add(light.shadow, "intensity", 0, 100).name("强度💪");
        shadowFolder?.add(light.shadow, "bias").name("偏移量");
        shadowFolder?.add(light, "intensity", 0, 1000).name("点光源强度");

        this.helper.renderer.shadowMap.enabled = true;
        // this.helper.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.helper.renderer.shadowMap.type = THREE.BasicShadowMap;

        const skyColor = 0xffffff; // light blue
        const groundColor = 0x000000; // brownish orange
        const intensity = 2;
        const hemisphereLight = new THREE.HemisphereLight(skyColor, groundColor, intensity);
        this.helper.scene.add(hemisphereLight);

        const HsLFolder = this.helper.gui?.addFolder("半球光");
        HsLFolder?.add(hemisphereLight, "intensity", 0, 10).name("强度💪");

        this.addEffectComposer();
        // this.loadModel();
        this.loadWall();
    }

    addEffectComposer() {
        const effectComposer = new EffectComposer(this.helper.renderer as THREE.WebGLRenderer);

        this.effectComposer = effectComposer;

        const renderPass = new RenderPass(this.helper.scene, this.helper.camera);
        effectComposer.addPass(renderPass);

        const pixelRatio = this.helper.renderer.getPixelRatio();
        const container = this.helper.renderer.domElement;

        const fullWidth = container.offsetWidth * pixelRatio;
        const fullHeight = container.offsetHeight * pixelRatio;

        const resolution = new THREE.Vector2(1 / fullWidth, 1 / fullHeight);

        const fxaaPass = new ShaderPass(FXAAShader);
        fxaaPass.material.uniforms["resolution"].value.copy(resolution);

        const smaaPass = new SMAAPass(fullWidth, fullHeight);
        effectComposer.addPass(smaaPass);

        const depthTexture = new THREE.DepthTexture(fullWidth, fullHeight);
        depthTexture.format = THREE.DepthStencilFormat;
        depthTexture.type = THREE.UnsignedInt248Type;

        const normalRenderTarget = new THREE.WebGLRenderTarget(fullWidth, fullHeight, {
            minFilter: THREE.NearestFilter,
            magFilter: THREE.NearestFilter,
            type: THREE.HalfFloatType,
            depthTexture: depthTexture,
        });
        const doorsRenderTarget = new THREE.WebGLRenderTarget(fullWidth, fullHeight, {
            minFilter: THREE.NearestFilter,
            magFilter: THREE.NearestFilter,
        });

        const moebiusPass = new MoebiusPass({
            depthTexture: { value: depthTexture },
            normalTexture: { value: normalRenderTarget.texture },
            normalRenderTarget: { value: normalRenderTarget },
            doorsRenderTarget: { value: doorsRenderTarget.texture },
            camera: this.helper.camera,
            scene: this.helper.scene,
            width: fullWidth,
            height: fullHeight,
            pointLightPosition: this.pointLight!.position,
            // pointLightPosition: this.helper.camera.position,
        });
        // moebiusPass.enabled = false;
        moebiusPass.material.uniforms["resolution"].value.copy(resolution);
        this.moebiusPass = moebiusPass;

        effectComposer.addPass(moebiusPass);

        effectComposer.addPass(fxaaPass);
        fxaaPass.enabled = false;
        effectComposer.addPass(smaaPass);
        smaaPass.enabled = false;

        const outputPass = new OutputPass();

        effectComposer.addPass(outputPass);

        const normalMaterial = new THREE.MeshNormalMaterial();
        // const depthMaterial = new THREE.MeshDepthMaterial();
        // const normalMaterial = NormalShader;
        const clearColor = new THREE.Color(0x000000);

        const rotationMatrix = new THREE.Matrix4();
        const normal = new THREE.Vector3();
        const view = new THREE.Vector3();
        const cameraWorldPosition = new THREE.Vector3();
        /**
         * 判断是否穿过门和方向
         */
        const PassedDoor = () => {
            const nearDoor = this.Doors.map((door) => {
                const distance = door.position.distanceTo(this.helper.camera.position);
                door.userData.distance = distance;
                return distance;
            });

            // 找到最小的一项的索引
            const minIndex = nearDoor.reduce(
                (minIdx, currentValue, currentIndex, arr) => (currentValue < arr[minIdx] ? currentIndex : minIdx),
                0
            );

            // 最近的门
            const door = this.Doors[minIndex];

            // 离得远不进行计算
            if (door.userData.distance > 2) {
                this.dir = -1;
                this.passed = false;
                return;
            }

            cameraWorldPosition.setFromMatrixPosition(this.helper.camera.matrixWorld);

            rotationMatrix.extractRotation(door.matrixWorld);

            normal.set(0, 1, 0);
            // normal.set(0, 0, 1);
            normal.applyMatrix4(rotationMatrix);

            const doorWorldPos = door.getWorldPosition(new THREE.Vector3());

            view.subVectors(doorWorldPos, cameraWorldPosition);

            // console.log(view.normalize().dot(normal.normalize()) > 0);
            const front = +(view.normalize().dot(normal.normalize()) < 0);

            if (this.dir != -1 && this.dir != front) {
                console.log("Passed");
                moebiusPass.reversal = moebiusPass.reversal == "1" ? "mix1" : "mix2";
                moebiusPass.reversalTag = moebiusPass.reversalTag == "1" ? "2" : "1";
                moebiusPass.setITime(0);
            }

            this.dir = front;
            // console.log(this.dir);
        };

        // removeListen
        const keyBoardControls = new KeyBoardControls(this.helper.controls, 0.4);

        keyBoardControls.move((vector) => {
            this.helper.camera.position.add(vector);
            this.helper.controls.target.add(vector);
        });

        this.destroy(() => {
            keyBoardControls.removeListen();
        });

        this.helper.render = () => {
            const delta = this.clock.getDelta();

            moebiusPass.addITime(delta);

            if (this.Doors.length) {
                this.helper.scene.toggleRoughnessMaterial && this.helper.scene.toggleRoughnessMaterial("black");
                this.Doors.forEach((obj: any) => (obj.visible = true));

                this.helper.renderer.setRenderTarget(doorsRenderTarget);
                this.helper.renderer.render(this.helper.scene, this.helper.camera);

                const region = this.getDoorRegion(fullWidth - 1, fullHeight - 1);

                if (region == 1) {
                    const curr = moebiusPass.reversalTag;
                    moebiusPass.reversal = curr == "1" ? "2" : "1";
                }

                PassedDoor();

                this.helper.renderer.setRenderTarget(null);

                this.helper.scene.toggleRoughnessMaterial && this.helper.scene.toggleRoughnessMaterial("default");

                this.Doors.forEach((obj: any) => (obj.visible = false));
            }

            // this.renderOverride(normalMaterial, normalRenderTarget, clearColor, 1, ["floor"]);
            if (this.helper.scene.toggleRoughnessMaterial) {
                this.helper.scene.toggleRoughnessMaterial("specular", ["floor"]);

                this.helper.renderer.setRenderTarget(normalRenderTarget);

                this.helper.renderer.render(this.helper.scene, this.helper.camera);

                this.helper.scene.toggleRoughnessMaterial("default", ["floor"]);

                this.helper.renderer.setRenderTarget(null);
            }
            
            effectComposer.render();
            // this.overrideMaterialRender(normalMaterial);

            keyBoardControls.update();
        };

        this.helper.gui?.add(fxaaPass, "enabled").name("FXAA");
        this.helper.gui?.add(smaaPass, "enabled").name("SMAA");
        this.helper.gui?.add(moebiusPass, "enabled").name("MOEBIUS");

        this.helper.gui
            ?.add(moebiusPass, "samplerMode", {
                默认: MoebiusPassSamplerMode.Default,
                深度: MoebiusPassSamplerMode.Depth,
                法线: MoebiusPassSamplerMode.Normal,
            })
            .name("采样");

        this.helper.gui
            ?.add(moebiusPass, "output", {
                默认: MoebiusPassOutput.Default,
                深度: MoebiusPassOutput.Depth,
                法线: MoebiusPassOutput.Normal,
                深度增强采样: MoebiusPassOutput.GrowDepth,
                法线增强采样: MoebiusPassOutput.GrowNormal,
                深度边缘检测: MoebiusPassOutput.DepthEdgeDetection,
                亮度: MoebiusPassOutput.BrightnessMaterial,
                高亮区域: MoebiusPassOutput.Highlights,
                基础颜色: MoebiusPassOutput.BaseColor,
                穿梭门: MoebiusPassOutput.Doors,
            })
            .name("输出");

        this.helper.gui?.add(moebiusPass.uniforms.uScalar, "value", -10, 10).name("标量");
        this.helper.gui?.add(moebiusPass.uniforms.uIntensity, "value", 1, 10).name("sobel增强");
        this.helper.gui?.add(moebiusPass.uniforms.brightnessStep.value, "b1", 0, 1).name("b1");
        // this.helper.gui?.add(moebiusPass.uniforms.brightnessStep.value, "b2", 0, 1).name("b2");
        this.helper.gui?.add(moebiusPass.uniforms.brightnessStep.value, "b3", 0, 1).name("b3");
    }

    /**
     * 获取穿梭门的渲染范围在屏幕中的占比
     * 会掉帧 待优化
     */
    getDoorRegion(fullWidth: number, fullHeight: number) {
        // 采样颜色平均值 判断距离
        const _gl = this.helper.renderer.getContext() as WebGL2RenderingContext;
        const pixels = new Uint8Array(4 * 1 * 1);
        const count = 2;
        const ratio = 1 / (count - 1);
        let total = 0;

        // 左下角开始
        for (let i = 0; i < count; i++) {
            for (let j = 0; j < count; j++) {
                _gl.readPixels(
                    fullWidth * i * ratio,
                    fullHeight * j * ratio,
                    1,
                    1,
                    _gl.RGBA,
                    _gl.UNSIGNED_BYTE,
                    pixels
                );
                total += pixels[0];
                total += pixels[1];
                total += pixels[2];
            }
        }

        total /= count * count * 255 * 3;

        return total;
    }

    @LoadGLTF("/public/models/martelo.glb")
    loadModel(gltf?: GLTF) {
        if (gltf) {
            this.helper.add(gltf.scene);

            const model = this.helper.get("Ch03") as THREE.SkinnedMesh;

            (<THREE.MeshStandardMaterial>model.material).metalness = 1;
            (<THREE.MeshStandardMaterial>model.material).roughness = 1;

            const mixer = new this.helper.AnimationPlayer({
                root: gltf.scene,
                animations: gltf.animations,
            });

            // ThreeHelper.handles.push(() => {
            //     mixer.update();
            // });
        }

        // const box = this.helper.create.box(0.3, 0.3, 0.3);
        // this.helper.add(box.mesh);
        // box.mesh.position.set(0, 0, -1);
        // box.material(
        //     new THREE.MeshStandardMaterial({
        //         color: 0x5511ff,
        //         roughness: 0.5,
        //         metalness: 0.5,
        //     })
        // );
    }

    @LoadGLTF("/public/models/wall.glb")
    loadWall(gltf?: GLTF) {
        if (gltf) {
            this.helper.add(gltf.scene);

            const floor = gltf.scene.getObjectByName("floor") as Mesh;

            gltf.scene.traverse((obj: any) => {
                if (obj.isMesh) {
                    // obj.material = this.SpecularMaterial({
                    //     cameraPos: this.helper.camera.position,
                    //     lightPosition: this.pointLight!.position,
                    // });

                    // obj.material = new THREE.MeshBasicMaterial();

                    // obj.material = new THREE.MeshBasicMaterial({
                    //    color:obj.material.color
                    // });

                    // obj.material = new NormalShaderMaterial();

                    obj.specularMaterial = new SpecularShaderMaterial({
                        scene: this.helper.scene,
                        camera: this.helper.camera,
                        roughness: obj?.material?.roughness,
                    });

                    // obj.material = new THREE.MeshPhongMaterial({
                    //     specular: 0xffffff,
                    //     shininess: 30,
                    //     // flatShading:true
                    // });

                    // (obj.material as THREE.MeshPhysicalMaterial).onBeforeCompile = (shader) => {
                    //     // console.log(shader.fragmentShader);
                    //     // shader.fragmentShader = shader.fragmentShader.replace(
                    //     //     "vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;",
                    //     //     "vec3 totalSpecular = reflectedLight.directSpecular;"
                    //     // );
                    //     shader.fragmentShader = shader.fragmentShader.replace(
                    //         "vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;",
                    //         "vec3 outgoingLight = reflectedLight.directSpecular;"
                    //         // "vec3 outgoingLight = pointLight.color;"
                    //         // "vec3 outgoingLight = pointLight.position;"
                    //     );
                    // };
                    const original = {
                        uniforms: {},
                        defines: {},
                        vertexShader: /* glsl */ `
                            void main() {
            
                                vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);
                                gl_Position = projectionMatrix * modelViewPosition;
                            }`,
                        fragmentShader: /* glsl */ `
                            void main() {

                                gl_FragColor = vec4(vec3( ${obj?.material?.roughness ?? 0} ) ,1. );
                            }
                        `,
                    };

                    obj.roughnessMaterial = new THREE.ShaderMaterial(original);
                    obj.blackMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
                    obj.prevMaterial = obj.material;

                    obj.baseMaterial = new THREE.ShaderMaterial({
                        uniforms: {},
                        defines: {},
                        side: obj.material.side,
                        vertexShader: /* glsl */ `
                                varying vec2 vUv;
                                void main() {
                                    vUv = uv;
                                    vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);
                                    gl_Position = projectionMatrix * modelViewPosition;
                                }`,
                        fragmentShader: /* glsl */ `
                                varying vec2 vUv;
                                void main() {
                                    vec3 color = vec3( ${obj.material.color.r}, ${obj.material.color.g}, ${obj.material.color.b} );
                                    gl_FragColor = vec4( color ,1. );
                                }
                            `,
                    });

                    if (obj.name.includes("bg") && floor) {
                        // obj.baseMaterial.side = THREE.BackSide
                        // obj.material.side = THREE.BackSide
                        obj.baseMaterial.fragmentShader = /* glsl */ `
                            varying vec2 vUv;
                            void main() {
                                vec3 color = vec3( ${obj.material.color.r}, ${obj.material.color.g}, ${
                            obj.material.color.b
                        } );
                                vec3 floorColor = vec3( ${floor.material.color.r - 0.05}, ${
                            floor.material.color.g - 0.05
                        }, ${floor.material.color.b - 0.05} );
                                gl_FragColor = vec4(vec3( mix( color, floorColor, vUv.y ) ) ,1. );
                            }
                        `;
                    }

                    if (obj.name.includes("door")) {
                        this.Doors.push(obj);
                        delete obj.baseMaterial;
                        delete obj.roughnessMaterial;
                        delete obj.prevMaterial;
                        delete obj.blackMaterial;
                        obj.material = new THREE.MeshBasicMaterial({ side: 2 });
                    }

                    // }
                    // else {
                    //     obj.baseMaterial = new THREE.MeshBasicMaterial({
                    //         color: obj.material.color,
                    //         side: obj.material.side,
                    //     });
                    // }
                    // obj.material = new THREE.ShaderMaterial(original);

                    obj.castShadow = true;
                    if (obj.name == "floor") {
                        obj.receiveShadow = true;
                    }
                }
            });

            this.helper.scene.toggleRoughnessMaterial = (type = "default", hideObject: string[] = []) => {
                let curr = this.helper.scene.curr;

                if (!curr || curr == "default") {
                    curr = this.helper.scene.curr = "roughness";
                } else {
                    curr = this.helper.scene.curr = "default";
                }

                curr = type || curr;

                this.helper.scene.traverse((obj: Object3D) => {
                    if (hideObject.includes(obj.name)) {
                        obj.visible = false;
                    }

                    if (obj.roughnessMaterial && obj.prevMaterial && obj.blackMaterial) {
                        if (curr == "default") {
                            obj.material = obj.prevMaterial;
                            if (hideObject.includes(obj.name)) {
                                obj.visible = true;
                            }
                        } else if (curr == "base") {
                            obj.material = obj.baseMaterial;
                        } else if (curr == "roughness") {
                            obj.material = obj.roughnessMaterial;
                        } else if (curr == "black") {
                            obj.material = obj.blackMaterial;
                        } else if (curr == "specular") {
                            obj.material = obj.specularMaterial;
                        }
                    }
                });
            };

            this.helper.gui?.addFunction(() => {
                const p = {
                    cameraPosition_x: -3,
                    cameraPosition_y: 5,
                    cameraPosition_z: 13,
                    cameraTarget_x: 0,
                    cameraTarget_y: 3,
                    cameraTarget_z: 0,
                    progress: 0,
                };
                gsap.to(p, {
                    cameraPosition_x: -4.2,
                    cameraPosition_y: 5,
                    cameraPosition_z: 3.52,
                    cameraTarget_x: -3.6,
                    cameraTarget_y: 4,
                    cameraTarget_z: -2,
                    duration: 1,
                    progress: 1,
                    onUpdate: () => {
                        this.helper.camera.position.copy({
                            x: p.cameraPosition_x,
                            y: p.cameraPosition_y,
                            z: p.cameraPosition_z,
                        });
                        this.helper.controls.target.copy({
                            x: p.cameraTarget_x,
                            y: p.cameraTarget_y,
                            z: p.cameraTarget_z,
                        });
                    },
                    onStart: () => {
                        console.log("动画开始");
                    },
                    onComplete: () => {
                        console.log("动画完成");
                        // this.moebiusPass && (this.moebiusPass.reversal = true);
                        gsap.to(p, {
                            cameraPosition_x: -28.2,
                            cameraPosition_y: 5,
                            cameraPosition_z: 3.52,
                            cameraTarget_x: 3,
                            cameraTarget_y: 4,
                            cameraTarget_z: -1,
                            duration: 3,
                            onUpdate: () => {
                                this.helper.camera.position.copy({
                                    x: p.cameraPosition_x,
                                    y: p.cameraPosition_y,
                                    z: p.cameraPosition_z,
                                });
                                this.helper.controls.target.copy({
                                    x: p.cameraTarget_x,
                                    y: p.cameraTarget_y,
                                    z: p.cameraTarget_z,
                                });
                            },
                            onStart: () => {
                                console.log("动画开始");
                            },
                            onComplete: () => {
                                console.log("动画完成");
                            },
                        }).play();
                    },
                }).play();
            }, "play");
        }
    }

    SpecularMaterial(params: { cameraPos: THREE.Vector3; lightPosition: THREE.Vector3 }) {
        const material = new THREE.ShaderMaterial({
            uniforms: {
                cameraPos: { value: params.cameraPos },
                lightPosition: { value: params.lightPosition },
                fresnelPower: { value: 5 },
            },
            vertexShader: `
            varying vec2 vUv;
            varying vec4 vPosition;
            varying vec3 vNormal;
            
            void main() {
                vUv = uv;
                // vPosition = position;
                vNormal = normal;

                vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);
                gl_Position = projectionMatrix * modelViewPosition;
                vPosition = vec4(position, 1.0);
                // vPosition = modelViewPosition;

            }`,
            fragmentShader: `
            varying vec4 vPosition;
            uniform vec3 lightPosition;
            uniform vec3 cameraPos;
            uniform float fresnelPower;
            varying vec3 vNormal;               

            void main() {
                vec3 eye = normalize(-vPosition.xyz);
                vec3 light = normalize(lightPosition - vPosition.xyz);
                vec3 reflectedDirection = normalize(-reflect(light, vNormal));
                vec3 halfway = normalize(light + eye);

                // vec3 pp = pointLights[0].;

                float fresnelFactor = dot(halfway, eye);
                fresnelFactor = max(fresnelFactor, 0.0);
                fresnelFactor = 1.0 - fresnelFactor;
                fresnelFactor = pow(fresnelFactor, fresnelPower);
                    
                vec3 color = vec3(fresnelFactor);
                
                gl_FragColor = vec4(color, 1.0);
            }`,
        });

        return material;
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {}

    @ThreeHelper.AddGUI(Main)
    createEnvTexture(gui: GUI) {
        // gui.addFunction(async () => {}, "1");
    }

    overrideMaterialRender(overrideMaterial: THREE.Material) {
        this.helper.renderer.setRenderTarget(null);
        this.helper.scene.overrideMaterial = overrideMaterial;
        this.helper.renderer.render(this.helper.scene, this.helper.camera);
        this.helper.scene.overrideMaterial = null;
    }

    renderOverride(
        overrideMaterial: THREE.Material & {
            clearColor?: THREE.Color;
            clearAlpha?: number;
        },
        renderTarget: THREE.WebGLRenderTarget,
        clearColor: THREE.Color,
        clearAlpha: number,
        hideObject: string[] = []
    ) {
        const originalClearColor = new THREE.Color();
        const tempColor = new Color4();
        const { renderer, scene, camera } = this.helper;

        originalClearColor.copy(this.helper.renderer.getClearColor(tempColor));

        const originalClearAlpha = renderer.getClearAlpha();
        const originalAutoClear = renderer.autoClear;

        renderer.setRenderTarget(renderTarget);
        renderer.autoClear = false;

        clearColor = overrideMaterial.clearColor || clearColor;
        clearAlpha = overrideMaterial.clearAlpha || clearAlpha;

        if (clearColor !== undefined && clearColor !== null) {
            renderer.setClearColor(clearColor);
            renderer.setClearAlpha(clearAlpha || 0.0);
            renderer.clear();
        }

        scene.overrideMaterial = overrideMaterial;

        const hideObjects: Object3D[] = [];

        scene.traverse((obj) => {
            if (hideObject.includes(obj.name)) {
                obj.visible = false;
                hideObjects.push(obj);
            }
        });

        renderer.render(scene, camera);
        scene.overrideMaterial = null;
        hideObjects.forEach((obj) => {
            obj.visible = true;
        });

        // restore original state

        renderer.autoClear = originalAutoClear;
        renderer.setClearColor(originalClearColor);
        renderer.setClearAlpha(originalClearAlpha);
        // renderer.setRenderTarget(null);
    }
}
