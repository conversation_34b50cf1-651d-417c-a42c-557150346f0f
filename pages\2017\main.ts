import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "@/src/components/Three/Canvas";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import type { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import type { GUI } from "dat.gui";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer.js";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass.js";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass.js";
import { FXAAShader } from "three/examples/jsm/shaders/FXAAShader";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import * as CANNONES from "cannon-es";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";
import { ConvexMeshDecomposition } from "vhacd-js";
import { gsap } from "gsap";
import { data as modelData } from "./modelData";
import { tumbler } from "./tumblerModelData";
import { RandomColor } from "@/src/ThreeHelper/utils";

@Injectable
export class Main extends MainScreen {
    static instance: Main;
    clock = new THREE.Clock();
    iTime = { value: 1 };
    iProgress = { value: 0 };
    effectComposer?: EffectComposer;
    size = new THREE.Vector2();
    bones: THREE.Object3D[] = [];
    dynamicBodies: [THREE.Object3D, CANNON.Body][] = [];
    wrap: THREE.Object3D<THREE.Object3DEventMap> | undefined;
    world = new CANNON.World();
    startPoint = new THREE.Vector2();
    bgColor = [
        { bg: new THREE.Color("#26D07C"), floor: new THREE.Color("#00AF66"), fog: new THREE.Color("#26D07C") },
        { bg: new THREE.Color("#778dbf"), floor: new THREE.Color("#6f8bc9"), fog: new THREE.Color("#778dbf") },
        { bg: new THREE.Color("#5511ff"), floor: new THREE.Color("#2211ff"), fog: new THREE.Color("#5511ff") },
        { bg: new THREE.Color("#f05"), floor: new THREE.Color("#f01"), fog: new THREE.Color("#f05") },
    ];
    bgMesh?: { bg: THREE.MeshStandardMaterial; floor: THREE.MeshStandardMaterial; fog: THREE.FogExp2 };
    sceneWidth = 40;
    groundBody?: CANNON.Body;
    CameraModel?: CANNON.Body;
    pictureMaterial = new CANNON.Material({
        friction: 0.5,
        restitution: 0.3,
    });
    texturesList: THREE.Texture[] = [];
    picturesCache: [THREE.Object3D, CANNON.Body][] = [];
    showAABB = false;
    computeConvexHulls?: VoidFunction;
    /** 火箭尾焰  */
    rocketPlume: [THREE.Object3D, CANNON.Body][] = [];
    /** 纸飞机 */
    PaperAirplanePhysics: [THREE.Object3D, CANNON.Body][] = [];
    rocketBody?: CANNON.Body;
    rocketMesh?: THREE.Object3D;
    rocketDir?: THREE.Object3D;
    isHoldScene = false;
    dragAir?: THREE.Object3D = undefined;

    constructor(private helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        Main.instance = this;

        this.world.gravity.set(0, -29.18, 0);

        const { ScrollTrigger } = require("gsap/ScrollTrigger");

        gsap.registerPlugin(ScrollTrigger);

        this.init();
    }

    @MethodBaseSceneSet({
        addAxis: false,
        cameraPosition: new THREE.Vector3(0, 1, 20),
        cameraTarget: new THREE.Vector3(0, 1, 0),
        useRoomLight: true,
    })
    async init() {
        this.initShadow();

        this.addEffectComposer();

        this.handleScene();

        this.loadTextures();
    }

    @EventMesh.OnMouseDown(Main)
    onMouseDown(RayInfo: BackIntersection, _: undefined, event: MouseEvent) {
        if (RayInfo.object) {
            if (RayInfo.object.name == "cameraWrap" && this.CameraModel) {
                this.CameraModel.applyForce(
                    new CANNON.Vec3((Math.random() - 0.5) / 1, 10, -10 * Math.random()),
                    new CANNON.Vec3((event.clientX / innerWidth) * 2 - 1, -(event.clientY / innerHeight) * 2 + 1, 0)
                );
                this.emitPicture();
            } else {
                const pointName = RayInfo.object.userData?.point?.name;

                if (pointName) {
                    const point = this.helper.scene.getObjectByName(pointName) as THREE.PointLight;
                    point && (point.visible = !point.visible);
                }

                this.startPoint.set(event.clientX, event.clientY);
                EventMesh.enabledMouseMove = true;
            }

            if (RayInfo.object.userData.drag && !RayInfo.object.userData.fire) {
                this.dragAir = RayInfo.object;
                console.log("拾起小飞机 DOWN", RayInfo.object);
                EventMesh.enabledMouseMove = true;
            }
        }

        if (window.scrollY >= innerHeight && window.scrollY < innerHeight * 1.5) {
            this.isHoldScene = true;
            this.blowAudio.volume = 1;
        }
    }

    @EventMesh.OnMouseMove(Main)
    onMouseMove(event: MouseEvent, RayInfo: BackIntersection) {
        if (window.scrollY < innerHeight) {
            const x = event.clientX - this.startPoint.x;
            const y = event.clientY - this.startPoint.y;

            const xDiff = (x / innerWidth) * 2 * 40;
            const yDiff = (y / innerHeight) * -2 * 40;

            this.startPoint.set(event.clientX, event.clientY);

            this.dynamicBodies[0][1].applyImpulse(new CANNON.Vec3(xDiff, yDiff, 0), new CANNON.Vec3());
        }

        if (this.dragAir) {
            // 鼠标/画布 X, Y 至 Three.js 世界 X, Y, Z
            // source: https://stackoverflow.com/a/13091694/22090297
            const vec = new THREE.Vector3();
            const pos = new THREE.Vector3();

            vec.set((event.clientX / window.innerWidth) * 2 - 1, -(event.clientY / window.innerHeight) * 2 + 1, 0);

            // 根据二维坐标获取三维坐标
            vec.unproject(this.helper.camera);

            // 设置z轴
            vec.sub(this.helper.camera.position).normalize();

            // const distance = -this.helper.camera.position.z / vec.z;
            // 需要固定点z = targetZ，将距离计算替换为： (targetZ - camera.position) / vec.z
            const distance = (5 - this.helper.camera.position.z) / vec.z;
            pos.copy(this.helper.camera.position).add(vec.multiplyScalar(distance));

            pos.y = Math.max(pos.y, -4.75);

            this.dragAir.position.copy(pos);
            // this.dragAir.lookAt(0, 2, 0);
            // this.dragAir.rotateY(Math.PI)
        }
    }

    @EventMesh.OnMouseUp(Main)
    onMouseUp() {
        EventMesh.enabledMouseMove = false;
        this.isHoldScene = false;

        this.stopBlowAudio();

        if (this.dragAir) {
            const body = this.dragAir.userData.body as CANNON.Body;

            this.dragAir.userData.fire = true;

            body.position.set(...this.dragAir.position);
            body.quaternion.set(
                this.dragAir.quaternion.x,
                this.dragAir.quaternion.y,
                this.dragAir.quaternion.z,
                this.dragAir.quaternion.w
            );

            this.world.addBody(body);
            body.velocity.set(0, 0, 0);

            body.applyImpulse(new CANNON.Vec3(0, 0, -1000), new CANNON.Vec3());

            this.PaperAirplanePhysics.push([this.dragAir, body]);

            // 发射完毕4只小飞机 重置一只
            if (this.PaperAirplanePhysics.length == 4) {
                const first = this.PaperAirplanePhysics.shift()!;

                this.world.removeBody(first[1]);

                // first[0].position.set(0, -4.78, 5);
                first[0].rotation.set(0, 0, 0);
                first[0].userData.fire = false;

                gsap.to(first[0].position, {
                    y: -6,
                    duration: 0.3,
                    onComplete: () => {
                        gsap.fromTo(
                            first[0].position,
                            {
                                x: 20,
                                y: -4.78,
                                z: 5,
                            },
                            {
                                x: 0,
                                y: -4.78,
                                z: 5,
                                duration: 0.8,
                            }
                        );
                    },
                });
            }
        }
        this.dragAir = undefined;
    }

    async handleScene() {
        this.handleBG();
        EventMesh.setIntersectObjects([]);

        await this.loadModel();

        await this.loadModel2();

        await this.loadModel3();

        await this.loadModel4Part();

        this.loadModel4();
    }

    handleBG() {
        const sphere = this.helper.create.sphere<THREE.MeshStandardMaterial>(150).add();

        sphere.material(
            new THREE.MeshBasicMaterial({
                side: 1,
                color: this.bgColor[0].bg,
            })
        );

        const floorMesh = new THREE.Mesh(
            new THREE.BoxGeometry(300, 1, 300),
            new THREE.MeshStandardMaterial({
                color: this.bgColor[0].floor,
            })
        );

        floorMesh.receiveShadow = true;
        floorMesh.position.y = -3.5;
        floorMesh.position.y -= 2;

        this.helper.add(floorMesh);

        const groundShape = new CANNON.Box(new CANNON.Vec3(150, 0.51, 150));

        const groundBody = new CANNON.Body({ mass: 0, material: new CANNON.Material("plane") });

        this.groundBody = groundBody;

        // 图片和地面的碰撞
        const mm = new CANNON.ContactMaterial(groundBody.material!, this.pictureMaterial, {
            friction: 0.4,
            restitution: 0.6,
        });

        this.world.addContactMaterial(mm);

        groundBody.addShape(groundShape);

        groundBody.position.set(0, floorMesh.position.y, 0);

        this.world.addBody(groundBody);

        this.helper.scene.fog = new THREE.FogExp2(this.bgColor[0].fog, 0.02);

        this.bgMesh = {
            bg: sphere.mesh.material,
            floor: floorMesh.material,
            fog: this.helper.scene.fog,
        };

        document.addEventListener(
            "click",
            () => {
                const bgMusic = new Audio("/music/soundtrack.mp3");
                bgMusic.loop = true;
                bgMusic.volume = 0.5;
                bgMusic.play();
            },
            { once: true }
        );

        // 顶部和底部的阴影

        const blurBanner = this.helper.create.plane(50, 1);

        blurBanner.mesh.position.set(0, 8, 4);

        blurBanner.material(
            new THREE.MeshBasicMaterial({
                map: this.helper.loadTexture("/public/textures/blur.png", (t) => {
                    t.wrapT = t.wrapS = THREE.RepeatWrapping;
                }),
                transparent: true,
            })
        );

        const floorBlurBanner = this.helper.create.plane(10, 0.2);

        floorBlurBanner.mesh.position.set(0, 0, 17.7);

        floorBlurBanner.material(
            new THREE.MeshBasicMaterial({
                map: this.helper.loadTexture("/public/textures/blur.png", (t) => {
                    t.wrapT = t.wrapS = THREE.RepeatWrapping;
                    t.rotation = Math.PI;
                }),
                transparent: true,
            })
        );

        this.helper.add(blurBanner.mesh);
        this.helper.add(floorBlurBanner.mesh);
    }

    addEffectComposer() {
        const { helper } = this;

        const effectComposer = new EffectComposer(helper.renderer as THREE.WebGLRenderer);

        this.effectComposer = effectComposer;

        const renderPass = new RenderPass(helper.scene, helper.camera);
        effectComposer.addPass(renderPass);

        const pixelRatio = helper.renderer.getPixelRatio();

        helper.renderer.getSize(this.size).multiplyScalar(pixelRatio);

        const resolution = new THREE.Vector2(1 / this.size.x, 1 / this.size.y);

        const params = {
            threshold: 0.8,
            strength: 0.15,
            radius: 1,
        };

        const bloomPass = new UnrealBloomPass(new THREE.Vector2(this.size.x, this.size.y), 1.5, 0.4, 0.85);
        bloomPass.threshold = params.threshold;
        bloomPass.strength = params.strength;
        bloomPass.radius = params.radius;

        effectComposer.addPass(bloomPass);

        const fxaaPass = new ShaderPass(FXAAShader);

        fxaaPass.uniforms["resolution"].value.copy(resolution);

        this.helper.addResizeListen(() => {
            const pixelRatio = helper.renderer.getPixelRatio();

            helper.renderer.getSize(this.size).multiplyScalar(pixelRatio);

            fxaaPass.uniforms["resolution"].value.set(1 / this.size.x, 1 / this.size.y);
        });

        effectComposer.addPass(fxaaPass);

        const outputPass = new OutputPass();

        effectComposer.addPass(outputPass);

        helper.render = () => {
            effectComposer.render();
        };
    }

    initShadow() {
        const light = new THREE.DirectionalLight(0xffffff, 0.01);
        light.position.set(0, 45, 0);

        light.castShadow = true;
        light.shadow.camera.top = 45 * 1.3;
        light.shadow.camera.bottom = -45 * 1.3;
        light.shadow.camera.left = -45 * 1.3;
        light.shadow.camera.right = 45 * 1.3;
        light.shadow.camera.near = 0.1 * 1.3;
        light.shadow.camera.far = 45 * 1.3;
        light.shadow.bias = 0.0001;
        light.shadow.intensity = 300;

        light.shadow.mapSize.width = 1024;
        light.shadow.mapSize.height = 1024;

        this.helper.add(light);

        this.helper.renderer.shadowMap.enabled = true;
        this.helper.renderer.shadowMap.type = THREE.PCFShadowMap;
    }

    @LoadGLTF("/public/models/Bulb.glb")
    loadModel(gltf?: GLTF) {
        if (gltf) {
            this.helper.add(gltf.scene);
            console.log(gltf.scene);

            const wrap = gltf.scene.getObjectByName("wrap");

            if (!wrap) throw new Error("No wrap");

            this.wrap = wrap;

            EventMesh.appendIntersectObjects(wrap.children);

            const {
                max: { y },
            } = new THREE.Box3().setFromObject(wrap);

            wrap.userData.y = y;

            gltf.scene.traverse((obj) => {
                if (obj.name == "玻璃外壳") {
                    const params = {
                        // color: 0xffffff,
                        color: 0xe4f1e9,
                        transmission: 1,
                        opacity: 1,
                        metalness: 0.03,
                        roughness: 0,
                        ior: 1,
                        thickness: 0.1,
                        specularIntensity: 1,
                        specularColor: 0xffffff,
                        envMapIntensity: 1,
                    };

                    const material = new THREE.MeshPhysicalMaterial({
                        color: params.color,
                        metalness: params.metalness,
                        roughness: params.roughness,
                        ior: params.ior,
                        thickness: params.thickness,
                        transmission: params.transmission,
                        specularIntensity: params.specularIntensity,
                        specularColor: params.specularColor,
                        envMapIntensity: params.envMapIntensity,
                        opacity: params.opacity,
                        side: THREE.DoubleSide,
                        transparent: true,
                        dispersion: 1,
                        iridescence: 1,
                        iridescenceIOR: 1.1,
                    });

                    (<THREE.Mesh>obj).material = material;
                }
                if ((<Mesh>obj).isMesh) {
                    // obj.castShadow = true;
                }

                if ((<THREE.Bone>obj).isBone) {
                    this.bones.push(obj as THREE.Bone);
                }

                if ((<THREE.SkinnedMesh>obj).isSkinnedMesh) {
                    // obj.material = new THREE.MeshNormalMaterial();
                }
            });

            // 添加物理
            const boxShape = new CANNON.Box(new CANNON.Vec3(0.2, 0.2, 0.2));

            let previous;

            const count = this.bones.length;

            const bodies: CANNON.Body[] = [];

            for (let i = 0; i < count; i++) {
                const mesh = this.bones[i];

                const boxBody = new CANNON.Body({
                    // mass: !i ? 0 : i == count - 1 ? 5 : 0.4,
                    mass: i == count - 1 ? 0 : Math.min(1, (count - i) / count + 0.3),
                    shape: boxShape,
                    // position: new CANNON.Vec3(4, (count - i) * 0.3 + 6, 0),
                    position: new CANNON.Vec3(mesh.position.x, i / 3, mesh.position.z),
                    // linearDamping: ( i) / count / 5,
                    linearDamping: 0.1,
                });

                this.world.addBody(boxBody);

                if (previous) {
                    // 将当前主体连接到上一个主体
                    const lockConstraint = new CANNON.LockConstraint(boxBody, previous);
                    this.world.addConstraint(lockConstraint);
                }

                previous = boxBody;

                bodies.push(boxBody);

                this.dynamicBodies.push([mesh, boxBody]);
            }

            // wrap && this.bones.unshift(wrap);

            document.body.style.height = "300vh";

            const moveScene = (x: number, x2: number, from: number, to: number) => {
                gsap.killTweensOf(gltf.scene.position);

                gsap.fromTo(
                    gltf.scene.position,
                    { x: x, y: 0, z: 0 },
                    {
                        x: x2,
                        y: 0,
                        z: 0,
                        duration: 1,
                    }
                );

                gsap.fromTo(
                    this.bgMesh!.fog.color,
                    { ...this.bgColor[from].fog },
                    {
                        ...this.bgColor[to].fog,
                        duration: 1,
                    }
                );
                gsap.fromTo(
                    this.bgMesh!.floor.color,
                    { ...this.bgColor[from].floor },
                    {
                        ...this.bgColor[to].floor,
                        duration: 1,
                    }
                );
            };

            gsap.to(
                {},
                {
                    scrollTrigger: {
                        trigger: document.body,
                        start: 0,
                        end: innerHeight / 2,
                        scrub: true,
                        onEnterBack: () => {
                            console.log("onEnterBack");
                            moveScene(-this.sceneWidth, 0, 1, 0);
                            bodies.forEach((body) => {
                                this.world.addBody(body);
                            });
                        },
                        onLeave: () => {
                            console.log("onLeave");
                            moveScene(0, -this.sceneWidth, 0, 1);

                            bodies.forEach((body) => {
                                this.world.removeBody(body);
                            });
                        },
                    },
                }
            );
        }
    }

    @LoadGLTF("/public/models/Polaroid.glb")
    loadModel2(gltf?: GLTF) {
        if (gltf) {
            const wrap = gltf.scene.getObjectByName("cameraWrap");

            if (!wrap) throw new Error("not wrap");

            this.helper.add(gltf.scene);

            EventMesh.appendIntersectObjects(gltf.scene.children);

            gltf.scene.traverse((obj) => {
                if ((<Mesh>obj).isMesh) {
                    obj.castShadow = true;
                }
            });

            const box = new THREE.Box3().setFromObject(wrap);

            const size = new THREE.Vector3();

            box.getSize(size);

            const boxShape = new CANNON.Box(new CANNON.Vec3(size.x / 2, 0.03, size.z / 2));

            box.getCenter(size);

            const boxBody = new CANNON.Body({
                mass: 0.04,
                shape: boxShape,
                position: new CANNON.Vec3(size.x, size.y, size.z),
                material: new CANNON.Material("camera"),
            });

            boxBody.quaternion.setFromEuler(0, Math.PI / -5, 0, "XYZ");

            const mm = new CANNON.ContactMaterial(this.groundBody!.material!, boxBody.material!, {
                friction: 1,
                restitution: 0.2,
            });

            this.world.addContactMaterial(mm);

            this.dynamicBodies.push([wrap, boxBody]);

            this.CameraModel = boxBody;

            gltf.scene.position.x = this.sceneWidth;

            const moveScene = (x: number, x2: number, from: number, to: number, changeColor = false) => {
                gsap.killTweensOf(gltf.scene.position);

                gsap.fromTo(
                    gltf.scene.position,
                    { x: this.sceneWidth + x, y: 0, z: 0 },
                    {
                        x: this.sceneWidth + x2,
                        y: 0,
                        z: 0,
                        duration: 1,
                    }
                );

                if (!changeColor) return;

                gsap.fromTo(
                    this.bgMesh!.fog.color,
                    { ...this.bgColor[from].fog },
                    {
                        ...this.bgColor[to].fog,
                        duration: 1,
                    }
                );
                gsap.fromTo(
                    this.bgMesh!.floor.color,
                    { ...this.bgColor[from].floor },
                    {
                        ...this.bgColor[to].floor,
                        duration: 1,
                    }
                );
            };

            gsap.to(
                {},
                {
                    scrollTrigger: {
                        trigger: document.body,
                        start: innerHeight / 2,
                        end: innerHeight,
                        scrub: true,
                        onEnter: () => {
                            moveScene(0, -this.sceneWidth, 1, 2, false);
                            boxBody.position = new CANNON.Vec3(size.x, size.y + 4, size.z);
                            boxBody.quaternion.setFromEuler(0, Math.PI / -5, 0, "XYZ");
                            this.world.addBody(boxBody);

                            this.removePictureMesh();
                        },
                        onEnterBack: () => {
                            moveScene(-this.sceneWidth * 2, -this.sceneWidth, 1, 2, false);
                            boxBody.position = new CANNON.Vec3(size.x, size.y + 4, size.z);
                            boxBody.quaternion.setFromEuler(0, Math.PI / -5, 0, "XYZ");
                            this.world.addBody(boxBody);
                            this.removePictureMesh();
                        },
                        onLeave: () => {
                            moveScene(-this.sceneWidth, -this.sceneWidth * 2, 1, 2, false);
                            this.removePictureMesh();
                            this.world.removeBody(boxBody);
                        },
                        onLeaveBack: () => {
                            moveScene(-this.sceneWidth, 0, 2, 1, false);
                            this.removePictureMesh();
                            this.world.removeBody(boxBody);
                        },
                    },
                }
            );
        }
    }

    shutterAudio = new Audio("/music/快门.mp3");

    emitPicture() {
        const planeTemp = this.helper.scene.getObjectByName("planeTemp");

        if (!planeTemp) throw new Error("not planeTemp");

        const plane = this.helper.create.plane(3, 3);

        plane.material(
            new PictureMaterial({ color: "#ffffff", side: 2, map: this.randomTexture, transparent: true, opacity: 1 })
        );

        // 控制阴影透明度
        // const customDepthMaterial = new THREE.MeshDepthMaterial({
        //     opacity: 1,
        //     depthPacking: THREE.BasicDepthPacking,
        //     transparent: true,
        // });

        // plane.mesh.customDepthMaterial = customDepthMaterial;

        plane.mesh.castShadow = true;

        plane.mesh.position.copy(planeTemp.getWorldPosition(new THREE.Vector3()));
        plane.mesh.quaternion.copy(planeTemp.getWorldQuaternion(new THREE.Quaternion()));
        plane.mesh.rotateX(Math.PI / -2);

        const planeBody = new CANNON.Body({
            mass: 0.1 * Math.min(1, Math.random() + 0.4),
            shape: new CANNON.Box(new CANNON.Vec3(1.5, 1.5, 0.02)),
            material: this.pictureMaterial,
        });

        planeBody.position.set(plane.mesh.position.x, plane.mesh.position.y, plane.mesh.position.z);

        planeBody.quaternion.setFromEuler(
            plane.mesh.rotation.x,
            plane.mesh.rotation.y,
            plane.mesh.rotation.z,
            plane.mesh.rotation.order
        );

        this.world.addBody(planeBody);

        this.picturesCache.push([plane.mesh, planeBody]);

        this.helper.add(plane.mesh);

        const v = new THREE.Vector3(5, 0, 0).applyEuler(plane.mesh.rotation);

        // plane.mesh.position.add(v);

        planeBody.applyImpulse(new CANNON.Vec3(v.x, v.y, v.z), new CANNON.Vec3());

        this.shutterAudio.currentTime = 0;
        this.shutterAudio.play();
        // new Audio("/music/快门.mp3").play();
    }

    removePictureMesh() {
        console.log("移除照片缓存");

        const { length } = this.picturesCache;

        this.picturesCache.forEach(([mesh, body], index) => {
            const delay = index / length / 10;

            gsap.to(mesh.position, {
                duration: 0.6 + delay,
                y: 20,
                onComplete: () => {
                    mesh.parent?.remove(mesh);
                    gsap.killTweensOf(mesh.position);
                },
                onUpdate: () => {
                    (<Mesh>mesh).material.opacity *= 0.9;
                    // (<Mesh>mesh).customDepthMaterial.opacity *= 0.7;
                    (<THREE.Mesh>mesh).castShadow = false;
                },
                delay,
            });
            this.world.removeBody(body);
        });
    }

    /**
     * 关键点：
     * 不知为何都会有报错和警告 但不影响使用
     * 模型的几何顶点尽力不更改 如果全部移动到0以上 碰撞的中心就有偏颇 就像是不倒翁一样 始终不会倒
     * 使用vhacd-js（ConvexMeshDecomposition） 解析数据后 将数据保存 运行时解析模型会卡顿很久
     */
    @LoadGLTF("/public/models/rocket.glb")
    async loadModel3(gltf?: GLTF) {
        if (gltf) {
            this.helper.add(gltf.scene);

            const mesh = gltf.scene.getObjectByProperty("name", "main") as THREE.Mesh;
            const rocketWrap = gltf.scene.getObjectByProperty("name", "rocketWrap") as THREE.Mesh;

            const box3 = new THREE.Box3().setFromObject(rocketWrap);

            // 离开限制 重置小火煎
            ThreeHelper.handles.push(() => {
                if (window.scrollY > innerHeight && window.scrollY < innerHeight * 1.5) {
                    const isContainsPoint = box3.containsPoint(mesh.position);

                    if (!isContainsPoint) {
                        this.rocketResetDisplay();
                    }
                }
            });

            rocketWrap.visible = false;

            const rocketBody = new CANNON.Body({ mass: 10 });

            const decomposer = await ConvexMeshDecomposition.create();
            const options = { maxHulls: 64 };

            // 从网格生成一系列凸包。
            this.computeConvexHulls = () => {
                console.log(
                    decomposer.computeConvexHulls(
                        {
                            positions: mesh.geometry.attributes.position.array as Float64Array,
                            indices: mesh.geometry.index!.array as Uint32Array,
                        },
                        options
                    )
                );
            };

            // 数据从文件加载 数据从computeConvexHulls函数 copy
            const hulls = modelData;

            hulls.forEach(({ positions, indices }) => {
                const vertices: CANNON.Vec3[] = [];
                const faces: number[][] = [];

                for (let index = 0; index < Object.keys(positions).length; index += 3) {
                    vertices.push(new CANNON.Vec3(positions[index], positions[index + 1], positions[index + 2]));
                }

                for (let index = 0; index < Object.keys(indices).length; index += 3) {
                    faces.push([indices[index], indices[index + 1], indices[index + 2]]);
                }

                const part = new CANNON.ConvexPolyhedron({ vertices, faces });

                rocketBody.addShape(part);
            });

            //@ts-ignore 控制台调试使用
            window.applyImpulse = (x: number, y: number, z: number, x2: number, y2: number, z2: number) => {
                rocketBody.applyImpulse(new CANNON.Vec3(x, y, z), new CANNON.Vec3(x2, y2, z2));
            };

            this.dynamicBodies.push([mesh, rocketBody]);

            const collisionAudio = new Audio("/music/碰.m4a");

            rocketBody.addEventListener("collide", (event: any) => {
                if (!!event.body.mass) return;

                const volume = event.contact.getImpactVelocityAlongNormal();

                const distance = this.helper.camera.position.distanceTo(event.target.position);

                // 过滤低强度碰撞和距离衰减
                if (volume > 1 && distance < 30) {
                    collisionAudio.currentTime = 0; // 确保从头播放
                    collisionAudio.volume = (Math.min(volume / 10, 1) * distance) / 30;
                    collisionAudio.play();
                }
            });

            this.rocketBody = rocketBody;
            this.rocketMesh = mesh;
            this.rocketDir = mesh.getObjectByName("空物体");

            gltf.scene.traverse((obj) => {
                if ((<Mesh>obj).isMesh) {
                    obj.castShadow = true;
                }
            });

            gltf.scene.position.x = this.sceneWidth * 2;

            const moveScene = (x: number, x2: number, from: number, to: number, changeColor = false) => {
                gsap.killTweensOf(gltf.scene.position);

                gsap.fromTo(
                    gltf.scene.position,
                    { x: this.sceneWidth + x, y: 0, z: 0 },
                    {
                        x: this.sceneWidth + x2,
                        y: 0,
                        z: 0,
                        duration: 1,
                    }
                );

                if (!changeColor) return;

                gsap.fromTo(
                    this.bgMesh!.fog.color,
                    { ...this.bgColor[from].fog },
                    {
                        ...this.bgColor[to].fog,
                        duration: 1,
                    }
                );
                gsap.fromTo(
                    this.bgMesh!.floor.color,
                    { ...this.bgColor[from].floor },
                    {
                        ...this.bgColor[to].floor,
                        duration: 1,
                    }
                );
            };

            this.initRocketPlume();

            gsap.to(
                {},
                {
                    scrollTrigger: {
                        trigger: document.body,
                        start: innerHeight,
                        end: innerHeight * 1.5,
                        scrub: true,
                        onEnter: () => {
                            this.world.addBody(rocketBody);
                            moveScene(0, -this.sceneWidth, 1, 2, true);
                            this.rocketResetDisplay();
                            this.ignite(mesh, rocketBody);

                            console.log("小火煎 onEnter");
                        },
                        onEnterBack: () => {
                            this.world.addBody(rocketBody);
                            moveScene(-this.sceneWidth * 2, -this.sceneWidth, 3, 2, true);
                            this.rocketResetDisplay();
                            this.ignite(mesh, rocketBody);
                            console.log("小火煎 onEnterBack");
                        },
                        onLeave: () => {
                            this.world.removeBody(rocketBody);

                            console.log("小火煎 onLeave");
                            moveScene(-this.sceneWidth, -this.sceneWidth * 2, 2, 3, true);
                            this.stopBlowAudio();
                        },
                        onLeaveBack: () => {
                            this.world.removeBody(rocketBody);

                            console.log("小火煎 onLeaveBack");
                            moveScene(-this.sceneWidth, 0, 2, 1, true);
                            this.stopBlowAudio();
                        },
                    },
                }
            );
        }
    }

    stopBlowAudio() {
        this.blowAudio.pause();
        this.blowAudio.currentTime = 0;
        this.stopRocketIgnite();
    }

    rocketResetDisplay() {
        this.rocketBody?.position.set(0, 6, 0);
        this.rocketBody?.quaternion.setFromEuler(0, 0, 0, "XYZ");
        this.rocketBody?.velocity.set(0, 0, 0);
        this.rocketBody?.angularVelocity.set(0, 0, 0);
    }

    ignite(mesh: Mesh, body: CANNON.Body) {
        this.blowAudio.volume = 1;
        const obj = { progress: 0 };

        gsap.fromTo(
            obj,
            { progress: 0 },
            {
                duration: 1.3,
                progress: 1,
                onUpdate: () => {
                    body.applyImpulse(new CANNON.Vec3(0, obj.progress * 10, 0), new CANNON.Vec3(0, 0, 0));

                    for (let i = 0; i < 5; i++) {
                        this.addRocketPlume(mesh);
                    }
                },
                onComplete: () => {
                    gsap.fromTo(
                        this.blowAudio,
                        {
                            volume: 1,
                        },
                        {
                            volume: 0,
                            duration: 0.2,
                            onComplete: () => {
                                this.blowAudio.pause();
                                this.blowAudio.currentTime = 0;
                            },
                        }
                    );
                    // this.world.removeBody(body);
                },
            }
        );

        this.stopRocketIgnite = () => {
            gsap.killTweensOf(obj);
        };
    }

    stopRocketIgnite() {}

    v1 = new THREE.Vector3();
    v2 = new THREE.Vector3();

    blowAudio = new Audio("/music/喷气短4.wav");

    pressScene() {
        const { rocketBody, rocketMesh, rocketDir } = this;

        if (rocketBody && rocketMesh && rocketDir) {
            const dir = rocketDir.getWorldPosition(this.v1).sub(rocketMesh.getWorldPosition(this.v2)).multiplyScalar(3);

            rocketBody.applyImpulse(new CANNON.Vec3(dir.x, dir.y, dir.z), new CANNON.Vec3(0, 0, 0));
            // this.blowAudio.currentTime = 0;
            for (let i = 0; i < 5; i++) {
                this.addRocketPlume(rocketMesh as Mesh);
            }
        }
    }

    rocketPlumeGeo = new THREE.SphereGeometry(0.25, 32 / 4, 16 / 4);
    rocketPlumeMat = new THREE.MeshStandardMaterial({ roughness: 0.5 });
    rocketPlumeCount = 200;
    rocketPlumeIndex = 0;

    initRocketPlume() {
        for (let index = 0; index < this.rocketPlumeCount; index++) {
            const sphere = new THREE.Mesh(this.rocketPlumeGeo, this.rocketPlumeMat);

            sphere.castShadow = true;

            sphere.visible = false;

            this.helper.add(sphere);

            const body = new CANNON.Body({ mass: 0.0001 });

            body.addShape(new CANNON.Sphere(0.1));

            this.rocketPlume.push([sphere, body]);
        }
    }

    /** 增加火箭尾焰 */
    addRocketPlume(mesh: THREE.Mesh) {
        this.blowAudio.play();

        this.rocketPlumeIndex = (this.rocketPlumeIndex + 1) % this.rocketPlumeCount;

        const [sphere, body] = this.rocketPlume[this.rocketPlumeIndex];

        sphere.visible = true;

        sphere.position.copy(mesh.getWorldPosition(new THREE.Vector3()));
        sphere.scale.setScalar(3);

        const random = (Math.random() - 0.5) / 1;

        const increment = new THREE.Vector3(random, -3 + random, random);

        increment.applyEuler(mesh.rotation);

        sphere.position.add(increment);

        body.position.set(sphere.position.x, sphere.position.y, sphere.position.z);

        this.world.addBody(body);

        body.applyImpulse(new CANNON.Vec3(random * 0.0001, 0.0002, random * 0.0001), new CANNON.Vec3());
    }

    RocketPlumeDissipate(mesh: THREE.Object3D, body: CANNON.Body) {
        // mesh.scale.multiplyScalar(0.98 * mesh.scale.x * (mesh.scale.x + 0.02));
        // mesh.scale.multiplyScalar(0.99 * (mesh.scale.x + 0.01));
        mesh.scale.multiplyScalar(0.95);

        //销毁
        if (mesh.scale.x < 0.001) {
            this.world.removeBody(body);
            mesh.visible = false;
        }
    }

    @LoadGLTF("/public/models/PaperAirplane.glb")
    async loadModel4Part(gltf?: GLTF) {
        if (gltf?.scene) {
            const PaperAirplane = gltf.scene.children[0] as THREE.Mesh;

            const box3 = this.helper.getBox3(PaperAirplane);

            const size = box3.getSize();
            size.y += 0.1;

            const startPos = [
                new THREE.Vector3(-17, 27, 16),
                new THREE.Vector3(17, 27, 16),
                new THREE.Vector3(17, 10, 16),
                new THREE.Vector3(-17, 10, 16),
            ];

            const meshes: THREE.Mesh[] = [];

            for (let i = 0; i < 4; i++) {
                const body = new CANNON.Body({ mass: 10 });
                body.addShape(new CANNON.Box(new CANNON.Vec3(...size)));

                const collisionAudio = new Audio("/music/碰撞.wav");

                body.addEventListener("collide", (event: any) => {
                    const volume = event.contact.getImpactVelocityAlongNormal();

                    const distance = this.helper.camera.position.distanceTo(event.target.position);

                    // 过滤低强度碰撞和距离衰减
                    if (volume > 1 && distance < 50) {
                        const mass = event.contact.bj.mass;

                        collisionAudio.currentTime = 0; // 确保从头播放
                        collisionAudio.volume = (Math.min(volume / 100, 1) * distance) / 50;
                        collisionAudio.play();
                    }
                });

                const mesh = new THREE.Mesh(
                    PaperAirplane.geometry.clone(),
                    new THREE.MeshStandardMaterial({
                        color: new RandomColor(),
                        roughness: 1,
                        metalness: 0,
                        side: 2,
                    })
                );

                mesh.rotateX(Math.PI / -16);

                mesh.scale.setScalar(2);

                mesh.castShadow = true;

                mesh.userData.body = body;
                mesh.userData.drag = "AirPlane";
                mesh.userData.showPos = new THREE.Vector3(i * 2 - 3, -4.78, 5);
                mesh.userData.startPos = startPos[i];

                mesh.position.copy(mesh.userData.startPos);

                meshes.push(mesh);

                this.helper.add(mesh);
            }

            EventMesh.appendIntersectObjects(meshes);

            this.airPlaneEnter = () => {
                meshes.forEach((mesh, index) => {
                    gsap.killTweensOf(mesh.position);

                    mesh.quaternion.set(0, 0, 0, 1);

                    mesh.userData.fire = false;

                    gsap.fromTo(
                        mesh.position,
                        {
                            x: mesh.userData.startPos.x,
                            y: mesh.userData.startPos.y,
                            z: mesh.userData.startPos.z,
                        },
                        {
                            x: mesh.userData.showPos.x,
                            y: mesh.userData.showPos.y,
                            z: mesh.userData.showPos.z,
                            duration: 2,
                            ease: "expo.out",
                            delay: 0.1 * index,
                            onComplete: () => {
                                // mesh.userData.body.position.set(
                                //     mesh.userData.showPos.x,
                                //     mesh.userData.showPos.y,
                                //     mesh.userData.showPos.z
                                // );
                                // this.world.addBody(mesh.userData.body);
                                // this.PaperAirplanePhysics.push([mesh,mesh.userData.body])
                            },
                        }
                    );
                });
            };

            this.airPlaneLeave = () => {
                this.PaperAirplanePhysics = [];

                meshes.forEach((mesh) => {
                    gsap.killTweensOf(mesh.position);

                    gsap.to(mesh.position, {
                        x: mesh.userData.startPos.x,
                        y: mesh.userData.startPos.y,
                        z: mesh.userData.startPos.z,
                        duration: 2,
                    });
                });
            };
        }
    }

    airPlaneEnter() {
        console.log("小飞机入场");
    }

    airPlaneLeave() {
        console.log("小飞机离场");
    }

    @LoadGLTF("/public/models/tumbler.glb")
    async loadModel4(gltf?: GLTF) {
        if (gltf) {
            this.helper.add(gltf.scene);

            // const mesh = gltf.scene.getObjectByProperty("name", "tumbler") as THREE.Mesh;
            const mesh = gltf.scene.getObjectByProperty("type", "Mesh") as THREE.Mesh;

            const body = new CANNON.Body({ mass: 100 });

            const decomposer = await ConvexMeshDecomposition.create();
            const options = { maxHulls: 16 };

            // console.log(
            //     decomposer.computeConvexHulls(
            //         {
            //             positions: mesh.geometry.attributes.position.array as Float64Array,
            //             indices: mesh.geometry.index!.array as Uint32Array,
            //         },
            //         options
            //     )
            // );

            // return;

            // 数据从文件加载 数据从computeConvexHulls函数 copy
            const hulls = tumbler;

            hulls.forEach(({ positions, indices }) => {
                const vertices: CANNON.Vec3[] = [];
                const faces: number[][] = [];

                for (let index = 0; index < Object.keys(positions).length; index += 3) {
                    vertices.push(new CANNON.Vec3(positions[index], positions[index + 1], positions[index + 2]));
                }

                for (let index = 0; index < Object.keys(indices).length; index += 3) {
                    faces.push([indices[index], indices[index + 1], indices[index + 2]]);
                }

                const part = new CANNON.ConvexPolyhedron({ vertices, faces });

                body.addShape(part);
            });

            this.dynamicBodies.push([mesh, body]);

            body.position.z = -4;

            const startPos = new CANNON.Vec3(body.position.x, body.position.y, body.position.z);

            gltf.scene.traverse((obj) => {
                if ((<Mesh>obj).isMesh) {
                    obj.castShadow = true;
                }
            });

            gltf.scene.position.x = this.sceneWidth * 3;

            const moveScene = (x: number, x2: number, from: number, to: number, changeColor = false) => {
                gsap.killTweensOf(gltf.scene.position);

                gsap.fromTo(
                    gltf.scene.position,
                    { x: this.sceneWidth * 2 + x, y: 0, z: 0 },
                    {
                        x: this.sceneWidth * 2 + x2,
                        y: 0,
                        z: 0,
                        duration: 1,
                    }
                );

                if (!changeColor) return;

                gsap.fromTo(
                    this.bgMesh!.fog.color,
                    { ...this.bgColor[from].fog },
                    {
                        ...this.bgColor[to].fog,
                        duration: 1,
                    }
                );
                gsap.fromTo(
                    this.bgMesh!.floor.color,
                    { ...this.bgColor[from].floor },
                    {
                        ...this.bgColor[to].floor,
                        duration: 1,
                    }
                );
            };

            gsap.to(
                {},
                {
                    scrollTrigger: {
                        trigger: document.body,
                        start: innerHeight * 1.5,
                        end: innerHeight * 2,
                        scrub: true,
                        onEnter: () => {
                            this.world.addBody(body);
                            body.position.set(startPos.x, startPos.y, startPos.z);
                            body.quaternion.set(0, 0, 0, 1);
                            body.velocity.set(0, 0, 0);
                            moveScene(0, -this.sceneWidth * 2, 2, 3, false);
                            this.airPlaneEnter();
                        },
                        onEnterBack: () => {},
                        onLeave: () => {},
                        onLeaveBack: () => {
                            this.world.removeBody(body);
                            moveScene(-this.sceneWidth * 2, 0, 3, 2, false);
                            this.airPlaneLeave();
                        },
                    },
                }
            );
        }
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {
        const delta = this.clock.getDelta();
        this.iTime.value += delta / 3;

        this.world.step(1 / 60, delta, 20);

        this.updateMeshPhysics(this.dynamicBodies, (mesh, body) => {
            if (mesh.name == "骨骼") {
                // mesh.position.y += 5.6;
                if (this.wrap) {
                    this.wrap.position.copy(body.interpolatedPosition);
                    this.wrap.position.y += this.wrap.userData.y;

                    this.wrap.quaternion.copy(body.interpolatedQuaternion);
                }
            }
            if (mesh.name == "cameraWrap") {
                mesh.position.y += 1.3;
            }
        });

        this.updateMeshPhysics(this.picturesCache);

        this.updateMeshPhysics(this.rocketPlume, (mesh, body) => {
            // 尾焰消散
            this.RocketPlumeDissipate(mesh, body);
        });

        this.updateMeshPhysics(this.PaperAirplanePhysics);

        if (this.isHoldScene) {
            this.pressScene();
        }
    }

    updateMeshPhysics(
        MeshPhysics: typeof this.dynamicBodies,
        call?: (mesh: THREE.Object3D, body: CANNON.Body) => void
    ) {
        for (let i = 0; i < MeshPhysics.length; i++) {
            const [mesh, body] = MeshPhysics[i];

            const position = body.interpolatedPosition;
            const quaternion = body.interpolatedQuaternion;

            mesh.position.copy(position);
            mesh.quaternion.copy(quaternion);

            call && call(mesh, body);

            if (this.showAABB) {
                body.updateAABB();
                // @ts-ignore
                const box3 = new THREE.Box3(body.aabb.lowerBound, body.aabb.upperBound);

                const box3helper = new THREE.Box3Helper(box3);

                // box3helper.rotation.copy(mesh.rotation);

                mesh.parent && mesh.parent.add(box3helper);

                if (mesh.userData.box3helper) {
                    mesh.parent && mesh.parent.remove(mesh.userData.box3helper);
                }

                mesh.userData.box3helper = box3helper;
            } else {
                if (mesh.userData.box3helper) {
                    mesh.parent && mesh.parent.remove(mesh.userData.box3helper);
                    mesh.userData.box3helper = undefined;
                }
            }
        }
    }

    @ThreeHelper.AddGUI(Main)
    Gui(gui: GUI) {
        this.helper.controls.enabled = false;

        this.helper.gui?.add(this.helper.controls, "enabled").name("controls enabled");
        this.helper.gui?.add(this, "showAABB").name("Show AABB");

        this.helper.gui
            ?.addFunction(() => {
                console.time();
                this.computeConvexHulls && this.computeConvexHulls();
                console.timeEnd();
            })
            .name("分解模型");

        this.helper.gui
            ?.addFunction(() => {
                this.rocketBody?.position.set(0, 2, 0);
                this.rocketBody?.quaternion.setFromEuler(0, 0, 0, "XYZ");
                this.rocketBody?.velocity.set(0, 0, 0);
                this.rocketBody?.angularVelocity.set(0, 0, 0);
            })
            .name("🚀 复位");

        // const progressBar = gui.add(this.iProgress, "value", 0, 1).step(0.001);
        // const play = () => {
        //     this.iProgress.value = 0;
        //     gsap.to(this.iProgress, {
        //         value: 1,
        //         duration: 1,
        //         onUpdate: () => {
        //             progressBar.updateDisplay();
        //         },
        //         onComplete: () => {},
        //     });
        // };
        // gui.addFunction(() => play()).name("play");
        this.helper.gui
            ?.addFunction(() => {
                this.dynamicBodies[0][1].applyImpulse(new CANNON.Vec3(10, 0, 0), new CANNON.Vec3());
            })
            .name("💡🫱");
        this.helper.gui
            ?.addFunction(() => {
                this.dynamicBodies[0][1].applyImpulse(new CANNON.Vec3(0, 10, 0), new CANNON.Vec3());
            })
            .name("💡⬆️");
        this.helper.gui
            ?.addFunction(() => {
                this.dynamicBodies[0][1].applyImpulse(new CANNON.Vec3(0, -40, 0), new CANNON.Vec3());
            })
            .name("💡⬇️");
    }

    loadTextures() {
        const list = [
            this.helper.loadTexture(
                "/public/textures/1A2461_3D70DB_2C3C8F_2C6CAC-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/1B1B1B_999999_575757_747474-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/3E2335_D36A1B_8E4A2E_2842A5-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/3F3A2F_91D0A5_7D876A_94977B-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/48270F_C4723B_9B5728_7B431B-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/4C462E_6D876C_9AAC8F_9AABA6-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/4F5246_8C8D84_7B7C74_131611-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/5E5855_C6C4CD_C89B67_8F8E98-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/B6B8B1_994A24_315C81_927963-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
            this.helper.loadTexture(
                "/public/textures/C7C7D7_4C4E5A_818393_6C6C74-512px.png",
                (t) => (t.colorSpace = THREE.SRGBColorSpace)
            ),
        ];

        this.texturesList = list;

        return list;
    }

    get randomTexture() {
        return this.texturesList[Math.floor(Math.random() * this.texturesList.length)];
    }
}

class PictureMaterial extends THREE.MeshStandardMaterial {
    constructor(params: ConstructorParameters<typeof THREE.MeshStandardMaterial>[0]) {
        super(params);

        this.onBeforeCompile = (parameters) => {
            parameters.fragmentShader = parameters.fragmentShader.replace(
                "#include <map_fragment>",
                `
                vec4 sampledDiffuseColor = texture2D( map, vMapUv * 1.2 - 0.1 );

	            diffuseColor *= sampledDiffuseColor;

                `
            );

            parameters.fragmentShader = parameters.fragmentShader.replace(
                "#include <dithering_fragment>",
                `
                #include <dithering_fragment>

                if(vMapUv.x > 0.89 || vMapUv.x < 0.09 || vMapUv.y > 0.89 || vMapUv.y < 0.09){
                    gl_FragColor.rgb = vec3(1.);
                }

                `
            );
        };
    }
}
