/*
 * @Author: hongbin
 * @Date: 2025-06-30 17:37:27
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-04 11:44:56
 * @Description:
 */
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass";
import { OutputPass } from "three/examples/jsm/postprocessing/OutputPass";
import { FXAAShader } from "three/examples/jsm/shaders/FXAAShader";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass";
import * as THREE from "three";

import shader from "../../2019/shader";
import { ThreeHelper } from "@/src/ThreeHelper";

interface Main {
    helper: ThreeHelper;
    selectedObjects: THREE.Object3D[];
    addSelectedObjects: (Object3D: Object3D) => void;
}

export function usedEffect(self: Main, use_outputPass = true) {
    const { renderer, scene, camera } = self.helper;

    const pixel = renderer.getPixelRatio();

    const fullWidth = innerWidth * pixel;
    const fullHeight = innerHeight * pixel;

    const composer = new EffectComposer(renderer);

    const renderPass = new RenderPass(scene, camera);
    composer.addPass(renderPass);

    const bgTilePass = new ShaderPass(
        new THREE.ShaderMaterial({
            uniforms: {
                tileMap: {
                    value: self.helper.loadTexture("/public/textures/2019/bg-tile.png", (t) => {
                        t.wrapS = t.wrapT = THREE.RepeatWrapping;
                    }),
                },
                tDiffuse: { value: null },
                brightness: { value: 1 },
            },
            vertexShader: shader.bgTile.vt,
            fragmentShader: shader.bgTile.fm,
        }),
        "tDiffuse"
    );
    composer.addPass(bgTilePass);

    const resolution = new THREE.Vector2(fullWidth, fullHeight);

    const params = {
        threshold: 0,
        strength: 0.1,
        radius: 0,
        exposure: 1,
    };

    const bloomPass = new UnrealBloomPass(new THREE.Vector2(window.innerWidth, window.innerHeight), 1.5, 0.4, 0.85);
    bloomPass.threshold = params.threshold;
    bloomPass.strength = params.strength;
    bloomPass.radius = params.radius;

    composer.addPass(bloomPass);

    {
        const gui = self.helper.gui!

        const bloomFolder = gui.addFolder("bloom");

        bloomFolder.add(params, "threshold", 0.0, 1.0).onChange(function (value) {
            bloomPass.threshold = Number(value);
        });

        bloomFolder.add(params, "strength", 0.0, 3.0).onChange(function (value) {
            bloomPass.strength = Number(value);
        });

        gui.add(params, "radius", 0.0, 1.0)
            .step(0.01)
            .onChange(function (value) {
                bloomPass.radius = Number(value);
            });
    }

    const outputPass = new OutputPass();
    composer.addPass(outputPass);

    const effectFXAA = new ShaderPass(FXAAShader);
    effectFXAA.uniforms["resolution"].value.set(1 / fullWidth, 1 / fullHeight);
    composer.addPass(effectFXAA);

    self.addSelectedObjects = (Object3D: Object3D) => {
        self.selectedObjects.push(Object3D);
    };

    self.helper.render = () => {
        composer.render();
    };
}
