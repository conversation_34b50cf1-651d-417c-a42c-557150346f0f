export class ReconnectingWebSocket {
    constructor(url, protocols = []) {
        this.url = url;
        this.protocols = protocols;
        this.ws = null;

        this.reconnectDelay = 1000; // 初始重连间隔
        this.maxReconnectDelay = 30000; // 最大重连间隔
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;

        this.shouldReconnect = true;

        this.connect();
    }

    connect() {
        this.ws = new WebSocket(this.url, this.protocols);

        this.ws.onopen = (event) => {
            console.log("WebSocket 连接成功");
            this.reconnectAttempts = 0;
            if (this.onopen) this.onopen(event);
        };

        this.ws.onmessage = (event) => {
            if (this.onmessage) this.onmessage(event);
        };

        this.ws.onerror = (event) => {
            console.warn("WebSocket 发生错误", event);
            if (this.onerror) this.onerror(event);
        };

        this.ws.onclose = (event) => {
            console.warn("WebSocket 已关闭", event);
            if (this.onclose) this.onclose(event);
            if (this.shouldReconnect) {
                this.scheduleReconnect();
            }
        };
    }

    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error("已达到最大重连次数，停止尝试");
            return;
        }

        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), this.maxReconnectDelay);
        this.reconnectAttempts++;

        console.log(`将在 ${delay / 1000} 秒后尝试重连...`);

        setTimeout(() => this.connect(), delay);
    }

    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(data);
        } else {
            console.warn("WebSocket 未连接，无法发送数据");
        }
    }

    close() {
        this.shouldReconnect = false;
        this.ws && this.ws.close();
    }
}
