/*
 * @Author: hongbin
 * @Date: 2025-07-07 10:46:39
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-31 21:56:13
 * @Description:
 */

import { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { Main } from "../../main2";
// import { SaiDaoConvex } from "../constant/SaiDaoConvex.js";
import * as THREE from "three";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";
import { ConvexMeshDecomposition } from "vhacd-js";
import { ThreeHelper } from "@/src/ThreeHelper";
import gsap from "gsap";
import { CustomEase } from "gsap/CustomEase";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";

const v3 = new THREE.Vector3();

export const handleSceneModel = (self: Main, gltf: GLTF) => {
    self.helper.add(gltf.scene);

    const mesh = gltf.scene.getObjectByProperty("name", "手柄") as THREE.Mesh;
    const 手柄001 = gltf.scene.getObjectByProperty("name", "手柄001") as THREE.Mesh;
    const tempMesh = gltf.scene.getObjectByProperty("name", "赛道") as THREE.Mesh;
    const 柱体 = gltf.scene.getObjectByProperty("name", "柱体") as THREE.Mesh;
    const 阻拦 = gltf.scene.getObjectByProperty("name", "阻拦") as THREE.Mesh;
    const 弹簧 = gltf.scene.getObjectByProperty("name", "弹簧") as THREE.Mesh;
    const 底座 = gltf.scene.getObjectByProperty("name", "底座") as THREE.Mesh;
    

    self.lookAtObject.push(柱体,阻拦,弹簧,底座,手柄001);

    self.handleSphere = mesh;

    if (tempMesh) {
        // 从网格生成一系列凸包。
        self.helper.gui
            ?.addFunction(async () => {
                const decomposer = await ConvexMeshDecomposition.create();
                const options = { maxHulls: 64 };

                console.log(
                    decomposer.computeConvexHulls(
                        {
                            positions: tempMesh.geometry.attributes.position.array as Float64Array,
                            indices: tempMesh.geometry.index!.array as Uint32Array,
                        },
                        options
                    )
                );
            })
            .name("生成模型凸包");
    }

    // 没有mesh则抛出错误
    if (!mesh) throw new Error("没有获取到手柄Mesh");

    EventMesh.appendIntersectObjects([mesh]);

    ThreeHelper.handles.push(() => {});

    console.log(gltf);

    // const sphere = addSphereToScene(self);

    // self.sphere = sphere;
};

/**
 * 添加球体到场景并处理物理交互
 * @param self Main 类实例
 */
const addSphereToScene = (self: Main) => {
    const sphere = createSphere(0.5, 1, 0.8);

    self.helper.scene.add(sphere[0]);

    self.world.addBody(sphere[1]);

    self.dynamicBodies.push(sphere);

    self.helper.gui
        ?.add(sphere[0].position, "x")
        .name("x轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });
    self.helper.gui
        ?.add(sphere[0].position, "y")
        .name("y轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });
    self.helper.gui
        ?.add(sphere[0].position, "z")
        .name("z轴")
        .onChange(() => {
            sphere[0].userData.sync && sphere[0].userData.sync();
        });

    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(1, 0, 0);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加x轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(-1, 0, 0);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加-x轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(0, 0, 1);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加z轴力");
    self.helper.gui
        ?.addFunction(() => {
            const force = new CANNON.Vec3(0, 0, -1);
            // sphere[1].applyForce(force, sphere[1].position);
            sphere[1].applyImpulse(force, new CANNON.Vec3(0, 0, 0));
        })
        .name("添加-z轴力");

    if (self.floorBody?.material && sphere[1].material) {
        const contactMaterial = new CANNON.ContactMaterial(sphere[1].material, self.floorBody.material, {
            restitution: 0.5, // 越接近 1 越弹
            friction: 0.4, // 可选：摩擦力
        });

        self.world.addContactMaterial(contactMaterial);
    }

    return sphere;
};

/**
 * 创建一个cannon球体
 */
const createSphere = (x: number, y: number, z: number): [THREE.Mesh, CANNON.Body] => {
    // 定义球体半径
    const radius = 0.04;

    const sphereMesh = new THREE.Mesh(
        new THREE.SphereGeometry(radius, 32, 32),
        new THREE.MeshStandardMaterial({ color: 0xffffff })
    );

    sphereMesh.name = "ball";

    // 创建球形状
    const sphereShape = new CANNON.Sphere(radius);

    // 创建刚体
    const sphereBody = new CANNON.Body({
        mass: 1, // 设置质量（非零表示可移动）
        shape: sphereShape,
        position: new CANNON.Vec3(x, y, z), // 初始位置（x, y, z）
        material: new CANNON.Material("ball_material"),
    });

    return [sphereMesh, sphereBody];
};

const ConvexAddCannon = () => {
    // SaiDaoMesh.visible = false;
    // const Body = new CANNON.Body({
    //     mass: 0,
    //     position: new CANNON.Vec3(...mesh.getWorldPosition(v3)),
    //     // position: new CANNON.Vec3(0,0.6,0),
    //     material: new CANNON.Material("BODY"),
    // });
    // // 数据从文件加载 数据从computeConvexHulls函数 copy
    // const hulls = [];
    // hulls.forEach(({ positions, indices }) => {
    //     const vertices: CANNON.Vec3[] = [];
    //     const faces: number[][] = [];
    //     for (let index = 0; index < Object.keys(positions).length; index += 3) {
    //         // @ts-ignore
    //         vertices.push(new CANNON.Vec3(positions[index], positions[index + 1], positions[index + 2]));
    //     }
    //     for (let index = 0; index < Object.keys(indices).length; index += 3) {
    //         // @ts-ignore
    //         faces.push([indices[index], indices[index + 1], indices[index + 2]]);
    //     }
    //     const part = new CANNON.ConvexPolyhedron({ vertices, faces });
    //     Body.addShape(part);
    // });
    // self.world.addBody(Body);
    // self.dynamicBodies.push([mesh, Body]);
};
