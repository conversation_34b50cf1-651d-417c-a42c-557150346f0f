import * as THREE from "three";
import { <PERSON><PERSON>el<PERSON> } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "@/src/components/Three/Canvas";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import type { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import type { GUI } from "dat.gui";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { gsap } from "gsap";
import { CustomEase } from "gsap/CustomEase";
import { usedEffect } from "./function/usedEffect";

import { handleSceneModel } from "./function/handleSceneModel";
import { TempAudioPlayback } from "../2019/function/TempAudioPlayback";
// import { handleMouseMove } from "./function/handleMouseMove";

import * as CANNONES from "cannon-es";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";
import { ConvexMeshDecomposition } from "vhacd-js";
import { addStatusFloor } from "./function/floor";
import { updateMeshPhysics } from "./function/updateMeshPhysics";
import { findClosestPointOnCurve } from "./function/findClosestPointOnCurve";
import { UnrealBloomEffect } from "@/src/ThreeHelper/decorators/UnrealBloomEffect";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass";
import { ShaderPass } from "three/examples/jsm/postprocessing/ShaderPass";
import { createFragment } from "./function/createFragment";
import { appendRipplePass } from "./function/appendRipplePass";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";

const p1 = new THREE.Vector3();
const p2 = new THREE.Vector3();

type Mesh<T extends THREE.MeshBasicMaterial = THREE.MeshBasicMaterial> = THREE.Mesh<THREE.BufferGeometry, T>;

const AA = () => {};

@UnrealBloomEffect({
    gui: true,
    close: false,
    smaa: false,
    fxaa: true,
    bloom: { threshold: 0.0, strength: 0, radius: 1, exposure: 1 },
})
@Injectable
export class Main extends MainScreen {
    static instance: Main;
    clock = new THREE.Clock();
    iTime = { value: 1 };
    selectedObjects: THREE.Object3D[] = [];
    world = new CANNON.World();
    dynamicBodies: [THREE.Object3D, CANNON.Body][] = [];
    floorBody?: CANNON.Body;
    iProgress = { value: 0 };
    iOpacity = { value: 0 };
    rippleTime = { value: 0 };
    iAmplitude = { value: 0 };
    sphere?: [THREE.Object3D, CANNON.Body];
    /** 加速 */
    accelerating = false;
    /** 巡航(curve路径) */
    cruise = false;
    speed = 1;
    curve?: THREE.CatmullRomCurve3;
    findClosestPointOnCurve?: () => ReturnType<typeof findClosestPointOnCurve>;
    /** UnrealBloomEffect装饰器插入的方法 选择应用辉光效果的物体 */
    select!: (object: Object3D) => void;
    appendPass!: (pass: ShaderPass) => void;
    bloomPass?: UnrealBloomPass;
    iRipple = { value: new THREE.Vector2(0.5, 0.5) };
    fragment?: ReturnType<typeof createFragment>;
    finalComposer?: EffectComposer;
    accelerateAudio?: ReturnType<typeof TempAudioPlayback>;

    constructor(public helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        Main.instance = this;

        this.world.gravity.set(0, -9.18, 0);

        const { ScrollTrigger } = require("gsap/ScrollTrigger");

        gsap.registerPlugin(ScrollTrigger);
        gsap.registerPlugin(CustomEase);

        this.init();
    }

    @MethodBaseSceneSet({
        addAxis: 0,
        fov: 30,
        cameraPosition: new THREE.Vector3(6, 5, 6.2),
        // cameraPosition: new THREE.Vector3(0, 10, 0),
        cameraTarget: new THREE.Vector3(0, 0, 0),
        useRoomLight: true,
    })
    async init() {
        this.helper.renderer.setPixelRatio(1);

        // this.helper.controls.enabled = false;
        this.initShadow();
        // usedEffect(this);
        // 加载模型等
        await this.handleScene();
        appendRipplePass(this);

        const fragment = createFragment();

        this.fragment = fragment;

        this.helper.add(fragment.group);
    }

    rippleExplosion = () => {
        if (this.sphere) {
            TempAudioPlayback("/public/music/explosion.wav", 1);

            // 转换世界坐标至 NDC，再转换至 uv (0~1)
            const ndc = this.sphere[0].position.clone().project(this.helper.camera);
            const center = new THREE.Vector2(ndc.x * 0.5 + 0.5, ndc.y * 0.5 + 0.5);
            console.log("💥center:", center);
            this.iRipple.value.copy(center);
            this.rippleTime.value = -0.2;

            gsap.fromTo(
                this.iAmplitude,
                {
                    value: 0,
                },
                {
                    value: 0.03,
                    duration: 0.5,
                }
            );

            if (this.fragment) {
                this.fragment.group.position.copy(this.sphere[0].position);
                setTimeout(() => {
                    this.fragment && this.fragment.explode();
                }, 200);
            }

            if (this.sphere) {
                // @ts-ignore
                this.onMouseUp({ target: { tagName: "CANVAS", ripple: true } });

                this.accelerating = false;
                this.cruise = false;

                this.sphere[0].visible = false;

                setTimeout(() => {
                    if (this.sphere) {
                        this.sphere[0].visible = true;
                        this.sphere[1].position.set(0.5, 1, 0.8);
                        this.sphere[1].velocity.set(0, 0, 0);
                    }
                }, 600);
            }
        }
    };

    /** 开始加速 */
    handleAccelerate() {
        this.accelerating = true;

        if (this.findClosestPointOnCurve && this.sphere) {
            const curve = this.findClosestPointOnCurve();
            const audio = TempAudioPlayback("/public/music/accelerate.wav", 0);

            this.accelerateAudio = audio;

            this.sphere[1].type = CANNON.Body.STATIC;
            // this.sphere[1].quaternion.set(0,0,0,'XYZ');

            this.sphere[0].userData.dir = curve.dir;

            gsap.killTweensOf(this.iOpacity);
            gsap.killTweensOf(this.sphere[0].position);

            gsap.to(this.sphere[0].position, {
                x: curve.nextPosition.x,
                z: curve.nextPosition.z,
                y: curve.nextPosition.y + 0.1,
                duration: 0.3,
                onComplete: () => {
                    console.log("down complete");
                    // this.iTime.value = curve.progress;
                    this.iTime.value = curve.nextProgress;
                    this.cruise = true;
                    this.speed = 0.1;

                    gsap.killTweensOf(this.iOpacity);

                    gsap.to(this.iOpacity, {
                        value: 1,
                        duration: 3,
                        onUpdate: () => {
                            if (this.bloomPass) {
                                audio.volume = this.iOpacity.value;
                                this.bloomPass.strength = this.iOpacity.value * 3;
                            }
                        },
                        onComplete: () => {
                            console.log("爆炸💥");
                            this.rippleExplosion();
                        },
                    });
                },
            });
        }
    }

    /**
     * 鼠标按下
     */
    @EventMesh.OnMouseDown(Main)
    onMouseDown(RayInfo: BackIntersection, _: undefined, event: MouseEvent) {
        // 点击目标需要是canvas
        if (RayInfo.object && (<HTMLElement>event.target).tagName == "CANVAS") {
            this.handleAccelerate();
        }
    }

    /**
     * 鼠标移动
     */
    // @EventMesh.OnMouseMove(Main)
    // onMouseMove(event: MouseEvent) {}

    /**
     * 鼠标抬起
     */
    @EventMesh.OnMouseUp(Main)
    onMouseUp(event: MouseEvent) {
        if ((<HTMLElement>event.target).tagName != "CANVAS") return;

        // @ts-ignore
        const isRipple = event.target.ripple;

        if (this.sphere) {
            // 慢慢减速
            gsap.killTweensOf(this.iOpacity);
            gsap.killTweensOf(this.sphere[0].position);

            if (!this.cruise) {
                return;
            }

            gsap.to(this.iOpacity, {
                value: 0,
                duration: isRipple ? 0.3 : 1,
                onUpdate: () => {
                    if (this.bloomPass) {
                        this.bloomPass.strength = this.iOpacity.value;
                        this.speed /= 1.1;
                        this.accelerateAudio && (this.accelerateAudio.volume = this.iOpacity.value);
                    }
                },
                onComplete: () => {
                    this.accelerating = false;
                    this.cruise = false;

                    console.log("up complete");

                    if (this.sphere) {
                        this.sphere[1].type = CANNON.Body.DYNAMIC;

                        this.sphere[1].quaternion.set(0, 0, 0, 1);
                        this.sphere[1].position.set(...this.sphere[0].position);
                        // this.sphere[1].position.y += 0.01;

                        this.sphere[1].velocity.set(0, 0, 0);

                        // const dir = this.sphere[0].userData.dir;
                        // this.sphere[1].velocity.set(...dir);

                        // this.sphere[1].velocity.set(...this.sphere[0].userData.dir);
                        // this.sphere[1].applyImpulse(new CANNON.Vec3(dir.x * 2,0,dir.z * 2), new CANNON.Vec3());
                    }
                },
            });

            //TODO 应记录上一次的位置 和这一次的位置 计算出前进方向 朝这个方向作为施加的力
        }
    }

    async handleScene() {
        // EventMesh.setIntersectObjects([]);

        addStatusFloor(this);

        await this.loadModel();
    }

    initShadow() {
        const light = new THREE.PointLight(0xffffff, 1, 100);
        light.position.set(2, 10, 1);
        light.castShadow = true; // default false
        this.helper.scene.add(light);

        //Set up shadow properties for the light
        light.shadow.mapSize.width = 512 * 4; // default
        light.shadow.mapSize.height = 512 * 4; // default
        light.shadow.camera.near = 0.1; // default
        light.shadow.camera.far = 20; // default
        light.shadow.intensity = 300;
        light.shadow.bias = 0.02;

        this.helper.renderer.shadowMap.enabled = true;
        this.helper.renderer.shadowMap.type = THREE.PCFShadowMap;
    }

    @LoadGLTF("/public/models/2018/环形赛道.glb")
    async loadModel(gltf?: GLTF) {
        if (gltf) handleSceneModel(this, gltf);
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {
        const delta = this.clock.getDelta();
        this.iTime.value += delta * this.speed;
        this.rippleTime.value += delta;

        if (this.accelerating) {
            if (this.cruise) {
                if (this.sphere && this.curve) {
                    this.speed += 0.01;

                    this.speed = Math.min(1, this.speed);

                    const p = this.curve.getPointAt(this.iTime.value % 1);

                    this.sphere[0].position.copy(p);

                    // this.sphere[0].position.y += 0.05;
                    this.iProgress.value = (this.iTime.value - 0.01) % 1;
                }
            }
        } else {
            this.world.step(1 / 60, delta, 20);

            updateMeshPhysics(this.dynamicBodies, (mesh, body) => {
                // if (mesh.name == "ball") {
                //     mesh.userData.sync && mesh.userData.sync();
                // }
            });
        }
    }

    @ThreeHelper.AddGUI(Main)
    Gui(gui: GUI) {
        if (gui) {
            gui.add(this.iProgress, "value", 0, 1, 0.01);
            gui.addFunction(() => {
                this.handleAccelerate();
            }).name("启动");
            gui.addFunction(() => {
                this.rippleExplosion();
            }).name("💥");
            gui.addFunction(() => {}).name("🎉");
        }
    }

    addSelectedObjects(Object3D: Object3D) {}
}
