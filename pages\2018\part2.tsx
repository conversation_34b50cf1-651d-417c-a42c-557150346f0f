/*
 * @Author: hongbin
 * @Date: 2025-06-30 17:32:42
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-07 10:43:19
 * @Description:
 */
/*
 * @Author: hongbin
 * @Date: 2025-06-30 15:43:53
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-30 17:32:15
 * @Description:
 */
import { css } from "styled-components";
import Layout from "@/src/components/Three/Layout";
import { Main } from "./main2";
import { useEffect } from "react";

interface IProps {}

const Index: React.FC<IProps> = () => {
    useEffect(() => {}, []);

    return (
        <>
            <Layout
                main={Main}
                seoTitle="👊"
                style={css`
                    width: 100vw;
                    height: 100vh;
                    position: fixed;
                    z-index: 1;
                    border: none;
                `}
            />
        </>
    );
};

export default Index;
