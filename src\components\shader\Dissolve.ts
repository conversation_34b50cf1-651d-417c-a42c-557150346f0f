/*
 * @Author: hongbin
 * @Date: 2023-10-17 14:26:54
 * @LastEditors: hongbin
 * @LastEditTime: 2023-10-17 16:43:41
 * @Description:溶解
 */

// Dissolve

import { ThreeHelper } from "@/src/ThreeHelper";
import {
    BoxGeometry,
    BufferGeometry,
    DoubleSide,
    IUniform,
    IcosahedronGeometry,
    Mesh,
    MeshStandardMaterial,
    PlaneGeometry,
    ShaderMaterial,
    SphereGeometry,
    TextureLoader,
    Uniform,
} from "three";

/** 柏林噪声片段 */
const perlinNoiseFragment = `
//	Classic Perlin 3D Noise 
//	by <PERSON>
//
vec4 permute(vec4 x){return mod(((x*34.0)+1.0)*x, 289.0);}
vec4 taylorInvSqrt(vec4 r){return 1.79284291400159 - 0.85373472095314 * r;}
vec3 fade(vec3 t) {return t*t*t*(t*(t*6.0-15.0)+10.0);}

float noise(vec3 P){
  vec3 Pi0 = floor(P); // Integer part for indexing
  vec3 Pi1 = Pi0 + vec3(1.0); // Integer part + 1
  Pi0 = mod(Pi0, 289.0);
  Pi1 = mod(Pi1, 289.0);
  vec3 Pf0 = fract(P); // Fractional part for interpolation
  vec3 Pf1 = Pf0 - vec3(1.0); // Fractional part - 1.0
  vec4 ix = vec4(Pi0.x, Pi1.x, Pi0.x, Pi1.x);
  vec4 iy = vec4(Pi0.yy, Pi1.yy);
  vec4 iz0 = Pi0.zzzz;
  vec4 iz1 = Pi1.zzzz;

  vec4 ixy = permute(permute(ix) + iy);
  vec4 ixy0 = permute(ixy + iz0);
  vec4 ixy1 = permute(ixy + iz1);

  vec4 gx0 = ixy0 / 7.0;
  vec4 gy0 = fract(floor(gx0) / 7.0) - 0.5;
  gx0 = fract(gx0);
  vec4 gz0 = vec4(0.5) - abs(gx0) - abs(gy0);
  vec4 sz0 = step(gz0, vec4(0.0));
  gx0 -= sz0 * (step(0.0, gx0) - 0.5);
  gy0 -= sz0 * (step(0.0, gy0) - 0.5);

  vec4 gx1 = ixy1 / 7.0;
  vec4 gy1 = fract(floor(gx1) / 7.0) - 0.5;
  gx1 = fract(gx1);
  vec4 gz1 = vec4(0.5) - abs(gx1) - abs(gy1);
  vec4 sz1 = step(gz1, vec4(0.0));
  gx1 -= sz1 * (step(0.0, gx1) - 0.5);
  gy1 -= sz1 * (step(0.0, gy1) - 0.5);

  vec3 g000 = vec3(gx0.x,gy0.x,gz0.x);
  vec3 g100 = vec3(gx0.y,gy0.y,gz0.y);
  vec3 g010 = vec3(gx0.z,gy0.z,gz0.z);
  vec3 g110 = vec3(gx0.w,gy0.w,gz0.w);
  vec3 g001 = vec3(gx1.x,gy1.x,gz1.x);
  vec3 g101 = vec3(gx1.y,gy1.y,gz1.y);
  vec3 g011 = vec3(gx1.z,gy1.z,gz1.z);
  vec3 g111 = vec3(gx1.w,gy1.w,gz1.w);

  vec4 norm0 = taylorInvSqrt(vec4(dot(g000, g000), dot(g010, g010), dot(g100, g100), dot(g110, g110)));
  g000 *= norm0.x;
  g010 *= norm0.y;
  g100 *= norm0.z;
  g110 *= norm0.w;
  vec4 norm1 = taylorInvSqrt(vec4(dot(g001, g001), dot(g011, g011), dot(g101, g101), dot(g111, g111)));
  g001 *= norm1.x;
  g011 *= norm1.y;
  g101 *= norm1.z;
  g111 *= norm1.w;

  float n000 = dot(g000, Pf0);
  float n100 = dot(g100, vec3(Pf1.x, Pf0.yz));
  float n010 = dot(g010, vec3(Pf0.x, Pf1.y, Pf0.z));
  float n110 = dot(g110, vec3(Pf1.xy, Pf0.z));
  float n001 = dot(g001, vec3(Pf0.xy, Pf1.z));
  float n101 = dot(g101, vec3(Pf1.x, Pf0.y, Pf1.z));
  float n011 = dot(g011, vec3(Pf0.x, Pf1.yz));
  float n111 = dot(g111, Pf1);

  vec3 fade_xyz = fade(Pf0);
  vec4 n_z = mix(vec4(n000, n100, n010, n110), vec4(n001, n101, n011, n111), fade_xyz.z);
  vec2 n_yz = mix(n_z.xy, n_z.zw, fade_xyz.y);
  float n_xyz = mix(n_yz.x, n_yz.y, fade_xyz.x); 
  return 2.2 * n_xyz;
}
`;
/** 模糊取余片段 */
const smoothModFragment = `
/* 
* SMOOTH MOD
* - authored by @charstiles -
* based on https://math.stackexchange.com/questions/2491494/does-there-exist-a-smooth-approximation-of-x-bmod-y
* (axis) input axis to modify
* (amp) amplitude of each edge/tip
* (rad) radius of each edge/tip
* returns => smooth edges
*/

float smoothMod(float axis, float amp, float rad){
    float top = cos(PI * (axis / amp)) * sin(PI * (axis / amp));
    float bottom = pow(sin(PI * (axis / amp)), 2.0) + pow(rad, 2.0);
    float at = atan(top / bottom);
    return amp * (1.0 / 2.0) - (1.0 / PI) * at;
}
`;
/** 区间映射片段 */
const fitFragment = `
float fit (float unscaled, float originalMin,float originalMax,float minAllowed, float maxAllowed) {
    return (maxAllowed - minAllowed) * (unscaled - originalMin) / (originalMax - originalMin) + minAllowed;
}
`;

interface IMapPath {
    map: string;
    dissolve?: string;
}

export class Dissolve {
    material: ShaderMaterial | MeshStandardMaterial;
    mesh: Mesh<BufferGeometry, typeof this.material>;

    constructor(private mapPaths: IMapPath, private gui: ThreeHelper["gui"]) {
        this.material = this.CustomMaterial();
        this.mesh = new Mesh(new IcosahedronGeometry(3, 5), this.material);
        // this.mesh = new Mesh(new SphereGeometry(3, 36, 36), this.material);
        // this.mesh = new Mesh(new BoxGeometry(3, 3, 3), this.material);
        gui?.add(<any>this.material.uniforms.iProgress, "value", 0, 1);
        this.mesh.onAfterRender = () => {
            (this.material as ShaderMaterial).uniforms.iProgress.value += 0.01;
        };
    }

    CustomMaterial(): ShaderMaterial {
        const material = new ShaderMaterial({
            transparent: true,
            uniforms: {
                iTime: { value: 0 },
                iProgress: { value: 0 },
                map: {
                    value: new TextureLoader().load(this.mapPaths.map),
                },
                dissolveMap: {
                    value:
                        this.mapPaths.dissolve &&
                        new TextureLoader().load(this.mapPaths.dissolve),
                },
            },
            vertexShader: `
            varying vec2 vUv;

            void main() {
                vUv = uv;
                vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);
                gl_Position = projectionMatrix * modelViewPosition;
            }`,
            fragmentShader: `
            uniform float iTime;
            varying vec2 vUv;
            uniform float iProgress;
            uniform sampler2D map;
            uniform sampler2D dissolveMap;

            void main( ) {
                vec3 diffuse = texture(map,vUv).rgb;
                ${
                    this.mapPaths.dissolve
                        ? "vec3 dissolve = texture(dissolveMap,vUv).rgb;"
                        : "vec3 dissolve = diffuse;"
                }
                float t = iProgress;

                float edge_width = mix(0.0,0.5, t);
                
                float edge =  smoothstep(dissolve.r - edge_width, dissolve.r, t);

                vec4 edgeColor = vec4(0.6,0.2,0.1, 1.0);
              
                diffuse += edgeColor.rgb * (1.0 - edge);
                gl_FragColor = vec4(diffuse,edge);
            }
            `,
        });

        return material;
    }

    CustomMaterial2(): ShaderMaterial {
        const material = new ShaderMaterial({
            side: DoubleSide,
            transparent: true,
            uniforms: {
                iTime: { value: 1 },
                dissolveChannel: {
                    value: new TextureLoader().load("/textures/building.png"),
                },
                map: {
                    value: new TextureLoader().load(
                        "/textures/painted_concrete_diff_1k.jpg"
                    ),
                },
            },
            vertexShader: `
            varying vec3 vNormal;
            varying vec2 vUv;

            void main() {
                vNormal = normal;
                vUv = uv;
                vec4 modelViewPosition = modelViewMatrix * vec4(position, 1.0);
                gl_Position = projectionMatrix * modelViewPosition;
            }`,
            fragmentShader: `
            uniform float iTime;
            varying vec2 vUv;
            uniform sampler2D dissolveChannel;
            uniform sampler2D map;

            void main() {
                vec4 color = texture2D( map, vUv);
                
                float t = fract(iTime);
                float h = texture2D( dissolveChannel, vUv).r;
            
                float dissolveWidth = 3.0;
          
                float condition_if_1 = step(h + vUv.x*dissolveWidth, t*(dissolveWidth + 1.0) + 0.04);
                float condition_if_2 = step(h + vUv.x*dissolveWidth, t*(dissolveWidth + 1.0));
                color = mix(color, vec4(0.0 ,1.0 , 1.0, 1.0), condition_if_1 );
                color = color * (1. - condition_if_2);
          
              
                gl_FragColor = color;
            }
            `,
        });

        return material;
    }

    // ReplaceMaterial(): MeshStandardMaterial {
    //     const material = new MeshStandardMaterial({
    //         color: "#aaa",
    //         emissive: "#ff3311",
    //         metalness: 0.5,
    //         roughness: 0.5,
    //     });

    //     material.onBeforeCompile = (shader) => {
    //         Object.assign(shader.uniforms, this.appendUniforms);
    //         shader.vertexShader = shader.vertexShader.replace(
    //             "#include <common>",
    //             `
    //             #include <common>
    //             uniform float iTime;
    //             uniform float iStepCount;
    //             #define PI 3.141592653589793
    //             ${perlinNoiseFragment}
    //             ${smoothModFragment}
    //             ${fitFragment}
    //         `
    //         );
    //         shader.vertexShader = shader.vertexShader.replace(
    //             "#include <begin_vertex>",
    //             `
    //             #include <begin_vertex>
    //             vec3 myNormal = normal;
    //             myNormal.y += iTime;
    //             float noiseValue = noise(myNormal);
    //             float pattern = fit(smoothMod(noiseValue * iStepCount,1.0,1.5),0.4,0.6,0.,1.);
    //             transformed += vec3(pattern) * normal;
    //         `
    //         );
    //     };

    //     return material;
    // }
}

/**
* // background grid from https://www.shadertoy.com/view/XtBfzz

const float N = 2.0; // grid ratio
float gridTexture( in vec2 p )
{
    // coordinates
    vec2 i = step( fract(p), vec2(1.0/N) );
    //pattern
    //return (1.0-i.x)*(1.0-i.y);   // grid (N=10)
    
    // other possible patterns are these
    //return 1.0-i.x*i.y;           // squares (N=4)
    return 1.0-i.x-i.y+2.0*i.x*i.y; // checker (N=2)
}

#define mask_tile 0.3

void mainImage( out vec4 fragColor, in vec2 fragCoord ) {

    // UVs of the main Color texture
	vec2 uv = fragCoord.xy/iResolution.xy;

    // color textures
    vec4 clrA = texture(iChannel0, uv);
    
    // background grid
    vec4 clrBG = 0.2 * vec4(1., 1., 1., 1.) * gridTexture(fragCoord.xy/iResolution.xx * vec2(5., 5.)) + 0.6;
    
    // set this to fade the alpha (0-1)
    float t = (sin(iTime) + 1.) / 2.;
    
	// set these to increase/decrease the edge width
    float edge_width_start = 0.15; // width at the start of the dissolve (alpha = 1)
    float edge_width_end = 0.05; // width at the end of the dissolve (alpha = 0)
    
    float edge_width = mix(edge_width_start, edge_width_end, smoothstep(0., 1., t)); // 
    
    // increase the alpha range by the edge width so we are not left with only glowy edges 
    float myAlpha = mix(0. - edge_width, 1., t); 
    
    // fade mask uv
    vec2 uv_mask = fragCoord.xy/iResolution.xy;
    
    // fade mask texture
    // use a linear texture that has values between 0-1
    vec4 alphaTex = texture(iChannel1, uv_mask * mask_tile);

    // alpha mask (1-bit)
    float a = step(alphaTex.r, myAlpha);

    // edge mask which is a slightly progressed version of the alpha
    // this mask doesn't need to be 1 bit as it will just be added to the color
    float edge = smoothstep(alphaTex.r - edge_width, alphaTex.r, myAlpha);

    vec4 edgeColor = vec4(0., 0.1, 1.0, 1.0);
    edgeColor *= edge * 10.;
    
    // add edge color to the color
    clrA += edgeColor;

    fragColor = mix(clrA, clrBG, a);
}
*/
