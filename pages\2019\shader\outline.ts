import vertexShader from "./vertex.glsl";
import fragmentShader from "./fragment.glsl";
import {
    IUniform,
    MeshNormalMaterial,
    PerspectiveCamera,
    Scene,
    ShaderMaterial,
    Vector2,
    WebGLRenderTarget,
    WebGLRenderer,
} from "three";
import { Pass, FullScreenQuad } from "three/examples/jsm/postprocessing/Pass";

export const OutLineShader = new ShaderMaterial({
    uniforms: {
        tDiffuse: { value: null },
        selectObjectMap: { value: null },
        resolution: { value: new Vector2() },
    },
    vertexShader,
    fragmentShader,
});

class OutLineShaderPass extends Pass {
    uniforms: {
        [uniform: string]: IUniform<any>;
    } = {};
    fsQuad: FullScreenQuad;
    templateRenderTarget: WebGLRenderTarget;

    constructor(public scene: Scene, public camera: PerspectiveCamera, public outLineObjects: Object3D[]) {
        super();

        this.templateRenderTarget = new WebGLRenderTarget(window.innerWidth, window.innerHeight);

        this.fsQuad = new FullScreenQuad(OutLineShader);
    }

    render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget) {
        if (this.outLineObjects.length) {
            // 渲染选定网格
            this.scene.traverse((obj) => {
                if (obj.isMesh) {
                    obj.visible = false;
                }
            });

            this.outLineObjects.forEach((obj) => {
                obj.visible = true;
                // obj.prevMaterial = obj.material;
                // obj.material = new MeshNormalMaterial({side:2});
            });

            renderer.setRenderTarget(this.templateRenderTarget);
            // renderer.setRenderTarget(writeBuffer);
            renderer.render(this.scene, this.camera);

            this.scene.traverse((obj) => {
                if (obj.isMesh) {
                    obj.visible = true;
                }
            });

            // this.outLineObjects.forEach((obj) => {
            // obj.material = obj.prevMaterial;
            // });

            // outline
            OutLineShader.uniforms["tDiffuse"].value = this.templateRenderTarget.texture;
            OutLineShader.uniforms["selectObjectMap"].value = readBuffer.texture;
            // OutLineShader.uniforms["selectObjectMap"].value = this.templateRenderTarget.texture;
        }else {
            OutLineShader.uniforms["tDiffuse"].value = readBuffer.texture;
            OutLineShader.uniforms["selectObjectMap"].value = readBuffer.texture;
        }
        
        renderer.setRenderTarget(writeBuffer);

        this.fsQuad.render(renderer);
    }

    dispose() {
        this.fsQuad.dispose();
    }
}

export { OutLineShaderPass };
