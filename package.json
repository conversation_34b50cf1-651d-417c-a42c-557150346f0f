{"name": "three-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev_p": "PORT=4000 next dev", "ip_dev": "next dev -H *************", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dimforge/rapier3d-compat": "^0.14.0", "@next/font": "13.1.2", "@react-three/drei": "^9.56.5", "@react-three/fiber": "^8.10.1", "@react-three/postprocessing": "^2.7.0", "@types/cannon": "^0.1.8", "@types/dat.gui": "^0.7.7", "@types/node": "18.11.18", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@types/stats.js": "^0.17.0", "@types/styled-components": "^5.1.26", "@types/three": "0.170.0", "ammo.js": "^0.0.10", "cannon": "^0.6.2", "cannon-es": "^0.20.0", "dat.gui": "^0.7.9", "eslint": "8.31.0", "eslint-config-next": "^13.2.4", "file-loader": "^6.2.0", "gsap": "^3.12.2", "lgl-tracer": "^2.0.0", "next": "^13.2.4", "next-seo": "^5.15.0", "nipplejs": "^0.10.0", "oimo": "^1.0.9", "omggif": "^1.0.10", "path-browserify": "^1.0.1", "raw-loader": "^4.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "reflect-metadata": "^0.2.1", "stats.js": "^0.17.0", "styled-components": "^5.3.6", "three": "^0.170.0", "three-gif-texture": "^1.0.15", "three-to-cannon": "^5.0.2", "typescript": "^5.0.2", "vhacd-js": "^0.0.1"}}