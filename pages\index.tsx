/*
 * @Author: hong<PERSON>
 * @Date: 2023-10-16 13:49:57
 * @LastEditors: hongbin
 * @LastEditTime: 2024-09-07 09:58:03
 * @Description:
 */
import { css } from "styled-components";
import Layout from "@/src/components/Three/Layout";
// import { Main } from "@/src/components/Three/Main";
import { Main } from "@/src/components/Three/moebius";
interface IProps {}

const Index: React.FC<IProps> = () => {
    return (
        <Layout
            main={Main}
            seoTitle="Hello World"
            style={css`
                width: 100%;
                height: 100%;
                z-index: 1;
                position: absolute;
                top: 0;
            `}
        />
    );
};

export default Index;
