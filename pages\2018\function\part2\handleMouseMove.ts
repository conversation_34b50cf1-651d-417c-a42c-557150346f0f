/*
 * @Author: hongbin
 * @Date: 2025-07-08 21:48:51
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-31 18:47:45
 * @Description:
 */
import * as THREE from "three";
import { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import { Main } from "../../main2";
import { pointToWorld } from "@/src/ThreeHelper/utils";
import { ThreeHelper } from "@/src/ThreeHelper";

export const handleMouseMove = (self: Main, event: MouseEvent) => {
    if (!self.startDrag) return;

    const point = {
        x: event.clientX,
        y: event.clientY,
    };

    // 获取当前鼠标位置对应3维坐标
    // const worldPos = pointToWorld(point.x, point.y, self.helper.camera, self.downPoint.z);
    const worldPos = getWorldPosition(event, self.downPoint.y);

    if(self.handleSphere){
        self.handleSphere.position.copy(worldPos);
        self.handleSphere.position.add(self.handleSphere.userData.diffPos)

        self.lookAtObject.forEach(item => {
            item.lookAt(self.handleSphere!.position);
        })
    }

    // console.log(worldPos);
};

const getWorldPosition = (event: MouseEvent, yTarget: number) => {
    const ndc = new THREE.Vector2(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
    );

    const near = new THREE.Vector3(ndc.x, ndc.y, -1).unproject(ThreeHelper.instance.camera);
    const far = new THREE.Vector3(ndc.x, ndc.y, 1).unproject(ThreeHelper.instance.camera);
    const dir = far.clone().sub(near).normalize();

    const t = (yTarget - near.y) / dir.y;
    const intersect = near.clone().add(dir.multiplyScalar(t));

    // console.log(`与 y=${yTarget} 的交点:`, intersect);

    return intersect;
};
