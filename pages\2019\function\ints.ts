/*
 * @Author: hong<PERSON>
 * @Date: 2025-06-16 16:22:53
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-16 16:22:53
 * @Description:
 */
import * as THREE from "three";

const m4 = new THREE.Matrix4();

export const createRotateMatrix = (rotationAngle: number) => {
    m4.set(
        Math.cos(rotationAngle),
        -Math.sin(rotationAngle),
        0.0,
        0.0,
        Math.sin(rotationAngle),
        Math.cos(rotationAngle),
        0.0,
        0.0,
        0.0,
        0.0,
        1.0,
        0.0,
        0.0,
        0.0,
        0.0,
        1.0
    );

    return m4;
};
