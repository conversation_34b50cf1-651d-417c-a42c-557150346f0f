uniform sampler2D tDiffuse;
uniform sampler2D selectObjectMap;
uniform vec2 resolution;
varying vec2 vUv;

void main() {

    vec2 texel = vec2(1.0 / resolution.x, 1.0 / resolution.y);

    // const mat3 Gx = mat3(-1, -2, -1, 0, 0, 0, 1, 2, 1); // x direction kernel
    // const mat3 Gy = mat3(-1, 0, 1, -2, 0, 2, -1, 0, 1); // y direction kernel

    // float offset = 1.;

    // float tx0y0 = texture2D(tDiffuse, vUv + texel * vec2(-offset, -offset)).r;
    // float tx0y1 = texture2D(tDiffuse, vUv + texel * vec2(-offset, 0)).r;
    // float tx0y2 = texture2D(tDiffuse, vUv + texel * vec2(-offset, offset)).r;

    // float tx1y0 = texture2D(tDiffuse, vUv + texel * vec2(0, -offset)).r;
    // float tx1y1 = texture2D(tDiffuse, vUv + texel * vec2(0, 0)).r;
    // float tx1y2 = texture2D(tDiffuse, vUv + texel * vec2(0, offset)).r;

    // float tx2y0 = texture2D(tDiffuse, vUv + texel * vec2(offset, -offset)).r;
    // float tx2y1 = texture2D(tDiffuse, vUv + texel * vec2(offset, 0)).r;
    // float tx2y2 = texture2D(tDiffuse, vUv + texel * vec2(offset, offset)).r;

    // float valueGx = Gx[0][0] * tx0y0 + Gx[1][0] * tx1y0 + Gx[2][0] * tx2y0 +
    //     Gx[0][1] * tx0y1 + Gx[1][1] * tx1y1 + Gx[2][1] * tx2y1 +
    //     Gx[0][2] * tx0y2 + Gx[1][2] * tx1y2 + Gx[2][2] * tx2y2;

    // float valueGy = Gy[0][0] * tx0y0 + Gy[1][0] * tx1y0 + Gy[2][0] * tx2y0 +
    //     Gy[0][1] * tx0y1 + Gy[1][1] * tx1y1 + Gy[2][1] * tx2y1 +
    //     Gy[0][2] * tx0y2 + Gy[1][2] * tx1y2 + Gy[2][2] * tx2y2;

    // float G = ((valueGx * valueGx) + (valueGy * valueGy));
    // // float G = ((valueGx * valueGx) + (valueGy * valueGy));

    // gl_FragColor += diffuse;

    // if(G > 0.) {
    //     G = 1.0;
    // }

    // gl_FragColor = vec4(vec3(G), 1);

    // vec3 color1 = texture2D(selectObjectMap, vUv + texel * vec2(-1, -1) * 0.005).rgb;
    // vec3 color2 = texture2D(selectObjectMap, vUv + texel * vec2(1, 1) * 0.005).rgb;
    // // float edge = sqrt(length(color1 - color2), 10.);
    // float edge = length(color1 - color2) * 200.;

    // gl_FragColor = vec4(vec3(edge), 1.0);

    float laplacian = (texture2D(tDiffuse, vUv + texel * vec2(-1, -1)).r +
        texture2D(tDiffuse, vUv + texel * vec2(1, -1)).r +
        texture2D(tDiffuse, vUv + texel * vec2(-1, 1)).r +
        texture2D(tDiffuse, vUv + texel * vec2(1, 1)).r -
        4.0 * texture2D(tDiffuse, vUv).r);

    // gl_FragColor = vec4(vec3(1.0 - (laplacian * 10.)), 1.0);

    // vec4 diffuse = texture2D(tDiffuse, vUv);

    // gl_FragColor.rgb = diffuse.rgb;
    // gl_FragColor.rgb = vec3(1.);
    // gl_FragColor.rgb = vec3(laplacian);
    gl_FragColor.a = 1.0;

    // gl_FragColor = diffuse;

    vec4 selectObject = texture2D(selectObjectMap, vUv);

    gl_FragColor.rgb = selectObject.rgb;
    
    if((laplacian) > 0.06) {
        gl_FragColor.rgb = vec3(0.);
    }


    // gl_FragColor = selectObject;

    

    // if(G > 0.1) {
        // gl_FragColor.rgb = vec3(0.);
    // }
    //gl_FragColor.rgb -= vec3(G);

    // gl_FragColor = selectObject;

}