/*
 * @Author: hongbin
 * @Date: 2025-07-04 14:37:12
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-04 18:56:37
 * @Description:
 */
import * as THREE from "three";
import { Main } from "../main";

export const TextureBlenderMaterial = (
    mesh: THREE.Mesh<THREE.BufferGeometry, THREE.MeshStandardMaterial>,
    self: Main
) => {
    mesh.material.onBeforeCompile = (shader) => {
        shader.uniforms.iProgress = self.iProgress;
        shader.uniforms.iOpacity = self.iOpacity;
        shader.uniforms.bloomTexture = {
            value: self.helper.loadTexture("/public/textures/2018/阿玛尼浅灰.jpg", (t) => {
                t.colorSpace = THREE.SRGBColorSpace;
            }),
        };

        shader.fragmentShader = shader.fragmentShader.replace(
            "void main() {",
            `
            uniform float iProgress;
            uniform float iOpacity;
            uniform sampler2D bloomTexture;

            void main() {
            `
        );

        shader.fragmentShader = shader.fragmentShader.replace(
            "#include <map_fragment>",
            `
            vec4 sampledDiffuseColor = texture2D( map, vMapUv );
            vec4 bloomDiffuseColor = texture2D( bloomTexture, vMapUv );
            
            // diffuseColor *= sampledDiffuseColor;

            float diff = iProgress - (1.0 - vMapUv.y);
            diff = mod(diff , 1.0);

            float strength = max(0.0, 1.0 - (diff / 0.1));
            vec4 bc = mix(bloomDiffuseColor, sampledDiffuseColor * 1., strength * sampledDiffuseColor.r * iOpacity);

            // diffuseColor *= bloomDiffuseColor;
            diffuseColor *= bc;
            
            `
        );
    };
};
