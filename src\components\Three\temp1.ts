// @ts-nocheck
const a = {
    activate: function () {
        this.inputs.start(), e.onMove.on(this.mouseMove), this.mouseMove();
    },

    deactivate: function () {
        this.inputs.release(), e.onMove.off(this.mouseMove);
    },
    init: function () {
        this.seedFloor = this.chapter.gltf.getPicking("picking_seed_flower");
        this.makeFlower(l, this.inputs.fakeTouch());
        this.removeAll();
    },
    removeAll: function () {
        for (var a = 0; a < this.flowers.length; a++) this.flowers[a].dispose();
    },
    closeAll: function () {
        for (var a = 0; a < this.flowers.length; a++) this.flowers[a].close();
    },
    makeFlower: function (a, b) {
        var c = new g(this.chapter, b);
        c.on("grown", this.onGrown), this.chapter.root.add(c.referentiel);
        var d = this.flowerModel.getMeshes(c);
        return (
            c.setMeshes(d),
            this.flowers.push(c),
            this.activeFlowers.push(c),
            c.referentiel.position.set(a),
            c.referentiel.updateWorldMatrix(),
            c.pickPlane.origin.set(a),
            c.reset(),
            this.activeFlowers.length > n &&
                (this.activeFlowers[0].off("grown", this.onGrown), this.activeFlowers.shift().close()),
            c
        );
    },
    getLoadables: function () {
        var a = [];
        return (
            (a = a.concat(this.flowerModel.getLoadables())),
            a.push(this.plantGrowthAudios[0].load()),
            a.push(this.plantGrownAudio.load()),
            a
        );
    },
    preRender: function () {
        for (var a = 0; a < this.flowers.length; a++) {
            var b = this.flowers[a];
            if (null != b.spline) b.preRender();
            else {
                this.flowers.splice(a, 1);
                var c = this.activeFlowers.indexOf(b);
                c > -1 && this.activeFlowers.splice(c, 1), a--;
            }
        }
    },
    onGrown: function () {
        this.emit("flower-grown");
    },
    _mouseMove: function () {
        var a = this.seedFloor.run(this.chapter.getMouseRay(), l),
            b = 0 !== a;
        this._hoverPick !== b && ((this._hoverPick = b), k.pushState(b ? k.STATES.DRAG : k.STATES.DEFAULT));
    },
    _onTouchAdded: function (a) {
        m.unproject(a.coords, this.chapter.camera);
        var b = this.seedFloor.run(m, l);
        0 !== b && this.makeFlower(l, a);
    },
    _onTouchRemoved: function (a) {},
};


35: [function(a, b, c) {
    function d(a, b) {
        this.chapter = a,
        this.flowerModel = new f(a),
        this.flowers = [],
        this.activeFlowers = [],
        this.seedFloor = null,
        this.inputs = new h(window),
        this._hoverPick = !1,
        this.mouseMove = this._mouseMove.bind(this),
        this.onTouchAdded = this._onTouchAdded.bind(this),
        this.onTouchRemoved = this._onTouchRemoved.bind(this),
        this.onGrown = this.onGrown.bind(this),
        this.inputs.onTouchAdded.on(this.onTouchAdded),
        this.inputs.onTouchRemoved.on(this.onTouchRemoved),
        this.plantGrowthAudios = [a.scene.audio.createAudioSource("plant_growth")],
        this.plantGrownAudio = a.scene.audio.createAudioSource("plant_grown")
    }
    var e = a("lib/mouse")
      , f = a("./flower-model")
      , g = a("./flower")
      , h = a("./inputs")
      , i = a("math/ray")
      , j = a("event-emitter")
      , k = a("components/cursor/cursor-manager")
      , l = new Float32Array(3)
      , m = new i
      , n = 6;
    d.prototype = {
        activate: function() {
            this.inputs.start(),
            e.onMove.on(this.mouseMove),
            this.mouseMove()
        },

        

        deactivate: function() {
            this.inputs.release(),
            e.onMove.off(this.mouseMove)
        },
        init: function() {
            this.seedFloor = this.chapter.gltf.getPicking("picking_seed_flower");
            this.makeFlower(l, this.inputs.fakeTouch());
            this.removeAll()
        },
        removeAll: function() {
            for (var a = 0; a < this.flowers.length; a++)
                this.flowers[a].dispose()
        },
        closeAll: function() {
            for (var a = 0; a < this.flowers.length; a++)
                this.flowers[a].close()
        },
        makeFlower: function(a, b) {
            var c = new g(this.chapter,b);
            c.on("grown", this.onGrown),
            this.chapter.root.add(c.referentiel);
            var d = this.flowerModel.getMeshes(c);
            return c.setMeshes(d),
            this.flowers.push(c),
            this.activeFlowers.push(c),
            c.referentiel.position.set(a),
            c.referentiel.updateWorldMatrix(),
            c.pickPlane.origin.set(a),
            c.reset(),
            this.activeFlowers.length > n && (this.activeFlowers[0].off("grown", this.onGrown),
            this.activeFlowers.shift().close()),
            c
        },
        getLoadables: function() {
            var a = [];
            return a = a.concat(this.flowerModel.getLoadables()),
            a.push(this.plantGrowthAudios[0].load()),
            a.push(this.plantGrownAudio.load()),
            a
        },
        preRender: function() {
            for (var a = 0; a < this.flowers.length; a++) {
                var b = this.flowers[a];
                if (null != b.spline)
                    b.preRender();
                else {
                    this.flowers.splice(a, 1);
                    var c = this.activeFlowers.indexOf(b);
                    c > -1 && this.activeFlowers.splice(c, 1),
                    a--
                }
            }
        },
        onGrown: function() {
            this.emit("flower-grown")
        },
        _mouseMove: function() {
            var a = this.seedFloor.run(this.chapter.getMouseRay(), l)
              , b = 0 !== a;
            this._hoverPick !== b && (this._hoverPick = b,
            k.pushState(b ? k.STATES.DRAG : k.STATES.DEFAULT))
        },
        _onTouchAdded: function(a) {
            m.unproject(a.coords, this.chapter.camera);
            var b = this.seedFloor.run(m, l);
            0 !== b && this.makeFlower(l, a)
        },
        _onTouchRemoved: function(a) {}
    },
    j(d.prototype),
    b.exports = d
}
, {
    "./flower": 37,
    "./flower-model": 36,
    "./inputs": 39,
    "components/cursor/cursor-manager": 65,
    "event-emitter": 343,
    "lib/mouse": 210,
    "math/ray": 224
}],

68: [function(a, b, c) {
    function d(a) {
        this.vue = a,
        this.view = a.$el,
        this.screenWidth = window.innerWidth,
        this.screenHeigth = window.innerHeight,
        this.dpr = window.devicePixelRatio,
        this.width = 150,
        this.height = 150,
        this.canvas = document.createElement("canvas"),
        this.ctx = this.canvas.getContext("2d"),
        this.x = 0,
        this.y = 0,
        this.canvas.width = this.width * this.dpr,
        this.canvas.height = this.height * this.dpr,
        this.canvas.style.width = this.width + "px",
        this.canvas.style.height = this.height + "px",
        this.canvas.classList.add("cursor"),
        a.$el.appendChild(this.canvas),
        this._scale = 0,
        this._scaleTeton = 0,
        this._scaleHold = 0,
        this._imageAlpha = 0,
        this._arrowAlpha = 0,
        this.targetScale = 0,
        this.targetScaleHold = 0,
        this.targetScaleTeton = 0,
        this.targetImageAlpha = 0,
        this.targetArrowAlpha = 0,
        this.ovaleScale = .65,
        this.circleSubdiv = 50,
        this.ovalePos = new Float32Array(2 * this.circleSubdiv),
        this.holdProgress = 0,
        this.holdScaleProgress = 0,
        this.isHolding = !1,
        this.holdState = !1,
        this.damping = .12,
        this.active = !1,
        this.prevent = !1,
        this.time = 0,
        this.pattern = null,
        this.patternImg = new Image,
        this.patternImg.onload = this.onPatternLoaded.bind(this),
        this.patternImg.src = "./assets/img/background_white_pattern.jpg",
        this.image = null,
        this.texs = {
            scene_1: {
                img: new Image,
                src: "./assets/img/picto_scene_1.png"
            },
            scene_2: {
                img: new Image,
                src: "./assets/img/picto_scene_2.png"
            },
            scene_3: {
                img: new Image,
                src: "./assets/img/picto_scene_3.png"
            },
            scene_4: {
                img: new Image,
                src: "./assets/img/picto_scene_4.png"
            },
            arrow_up: {
                img: new Image,
                src: "./assets/img/arrow_up_small.png"
            }
        },
        this.texLoadedCount = 0,
        this.loaded = !1,
        this.load(),
        window.addEventListener("resize", function() {
            this.resize()
        }
        .bind(this))
    }
    var e = a("lib/raf")
      , f = a("lib/mouse")
      , g = a("math/ease")
      , h = a("./cursor-manager")
      , i = a("math");
    d.prototype = {
        load: function() {
            for (var a = Object.keys(this.texs), b = 0; b < a.length; b++)
                this.texs[a[b]].img.onload = this.onTexLoaded.bind(this),
                this.texs[a[b]].img.src = this.texs[a[b]].src
        },
        onTexLoaded: function() {
            this.texLoadedCount += 1,
            this.texLoadedCount == Object.keys(this.texs).length + 1 && (this.loaded = !0)
        },
        setOvalePath: function(a) {
            for (var b, c = a.getTotalLength(), b = a.getPointAtLength(.5 * c), d = c / this.circleSubdiv, e = 0, f = 0; f < c; f += d)
                b = a.getPointAtLength(f),
                this.ovalePos[e] = (b.x - this.width / 2) * this.dpr * this.ovaleScale,
                e += 1,
                this.ovalePos[e] = (b.y - this.height / 2) * this.dpr * this.ovaleScale,
                e += 1
        },
        onPatternLoaded: function() {
            this.pattern = this.ctx.createPattern(this.patternImg, "repeat"),
            this.onTexLoaded()
        },
        resize: function() {
            this.screenWidth = window.innerWidth,
            this.screenHeigth = window.innerHeight
        },
        activate: function() {
            this.active = !0,
            document.body.classList.add("noCursor")
        },
        drawCircle: function(a, b, c, d, e, f) {
            var g = this.ctx;
            g.beginPath(),
            g.fillStyle = this.pattern,
            g.lineWidth = d,
            g.strokeStyle = e,
            g.arc(this.width / 2 * this.dpr, this.height / 2 * this.dpr, a * this.dpr, 0 - f, 2 * Math.PI * b - f),
            c && g.fill(),
            g.stroke()
        },
        drawNoisyCircleStroke: function(a) {
            var b = this.ctx;
            b.save(),
            b.translate(this.width / 2 * this.dpr, this.height / 2 * this.dpr),
            b.beginPath();
            var c = this._imageAlpha
              , d = 0
              , e = -this.height / 2 * this.dpr
              , d = i.lerp(0, 0, c)
              , e = i.lerp(-a * this.dpr, -this.height / 2 * this.dpr * this.ovaleScale, c);
            b.moveTo(d, e),
            b.lineWidth = 2 * this.dpr,
            b.strokeStyle = "black",
            b.fillStyle = this.pattern;
            for (var f = this.circleSubdiv, g = 0; g < f; g++) {
                var h = Math.cos((g + 1) / f * Math.PI * 2 - .5 * Math.PI) * a * this.dpr
                  , j = Math.sin((g + 1) / f * Math.PI * 2 - .5 * Math.PI) * a * this.dpr;
                h += (.35 * Math.cos(39847239487 * g) - .175) * this.dpr,
                j += (.35 * Math.sin(392847329847 * g) - .175) * this.dpr,
                h = i.lerp(h, this.ovalePos[2 * g + 0], c),
                j = i.lerp(j, this.ovalePos[2 * g + 1], c),
                b.lineTo(h, j)
            }
            b.closePath(),
            b.stroke(),
            b.fill(),
            b.restore()
        },
        drawArrow: function() {
            if (!(this._arrowAlpha < 1e-4)) {
                var a = this.texs.arrow_up.img
                  , b = this.ctx;
                b.save(),
                b.globalAlpha = this._arrowAlpha;
                var c = .25 * this._arrowAlpha
                  , d = a.width * c
                  , e = d * this.dpr
                  , f = a.height * c
                  , g = f * this.dpr;
                b.translate(this.width / 2 * this.dpr, (this.height / 2 - .5 * f) * this.dpr),
                b.drawImage(a, .5 * -e, .5 * -g, e, g),
                b.restore()
            }
        },
        drawImage: function() {
            if (!(null == this.image || this._imageAlpha < 1e-4)) {
                var a = this.ctx;
                a.save(),
                a.globalAlpha = this._imageAlpha,
                a.translate(this.width / 2 * this.dpr, this.height / 2 * this.dpr);
                var b = .4 * this._imageAlpha
                  , c = this.image.width * b * this.dpr
                  , d = this.image.height * b * this.dpr;
                a.drawImage(this.image, .5 * -c, .5 * -d, c, d),
                a.restore()
            }
        },
        drawOvoid: function() {},
        draw: function() {
            var a = this.ctx;
            a.clearRect(0, 0, this.width * this.dpr, this.height * this.dpr);
            var b = this._scaleTeton
              , c = this._scale - this._scaleHold * g.easeOutCubic(this.holdScaleProgress);
            this.drawNoisyCircleStroke(75 * c),
            b > .4 && (a.globalAlpha = Math.min(3 * (b - .4), 1),
            this.drawCircle(4.5 * b, 1, !1, 2 * this.dpr, "black", 0),
            a.globalAlpha = 1);
            var d = g.easeInOutCubic(this.holdProgress);
            d > 1e-5 && (this.drawCircle(44, d, !1, 7 * this.dpr, "black", Math.PI / 2),
            this.drawCircle(44, d, !1, 5 * this.dpr, "rgba(249, 245, 236, 1)", Math.PI / 2)),
            this.drawArrow(),
            this.drawImage(),
            this.drawOvoid()
        },
        updateMouseCoords: function() {
            this.x = .5 * (f.coords[0] + 1) * this.screenWidth - this.width / 2,
            this.y = .5 * (-f.coords[1] + 1) * this.screenHeigth - this.height / 2
        },
        resetTargets: function() {
            this.targetScale = 0,
            this.targetScaleHold = 0,
            this.targetScaleTeton = 0,
            this.targetImageAlpha = 0,
            this.damping = .12,
            this.targetArrowAlpha = 0,
            this.targetImageAlpha = 0,
            this.isHolding = !1,
            this.holdState = !1
        },
        updateTargets: function() {
            switch (this.resetTargets(),
            h.getCurrentState()) {
            case h.STATES.DEFAULT:
                this.targetScale = BrowserDetect.isMobile ? .5 : .12;
                break;
            case h.STATES.HOVER_UI:
                this.targetScale = .075;
                break;
            case h.STATES.DRAG:
                this.targetScale = .5,
                this.targetArrowAlpha = 1,
                f.isDown(1) && (this.targetArrowAlpha = 0,
                this.targetScale = .24,
                this.targetScaleTeton = .8);
                break;
            case h.STATES.TEXT:
                this.targetScale = .5;
                break;
            case h.STATES.HOLD:
                this.targetScale = .5,
                this.targetScaleHold = .25,
                this.holdState = !0;
                break;
            case h.STATES.IDLE_SCENE_1:
                this.targetScale = .12,
                this.targetImageAlpha = 1,
                this.image = this.texs.scene_1.img;
                break;
            case h.STATES.IDLE_SCENE_2:
                this.targetScale = .12,
                this.targetImageAlpha = 1,
                this.image = this.texs.scene_2.img;
                break;
            case h.STATES.IDLE_SCENE_3:
                this.targetScale = .12,
                this.targetImageAlpha = 1,
                this.image = this.texs.scene_3.img;
                break;
            case h.STATES.IDLE_SCENE_4:
                this.targetScale = .12,
                this.targetImageAlpha = 1,
                this.image = this.texs.scene_4.img
            }
        },
        updateFactors: function() {
            this._scale += (this.targetScale - this._scale) * this.damping,
            this._scaleTeton += (this.targetScaleTeton - this._scaleTeton) * this.damping,
            this._scaleHold += (this.targetScaleHold - this._scaleHold) * this.damping,
            this._imageAlpha += (this.targetImageAlpha - this._imageAlpha) * this.damping,
            this._arrowAlpha += (this.targetArrowAlpha - this._arrowAlpha) * this.damping;
            var a = .001 * e.dt;
            this.isHolding = f.isDown(1),
            this.holdProgress += this.isHolding && this.holdState ? .5 * a : .9 * -a,
            this.holdScaleProgress += this.isHolding && this.holdState ? 2 * a : 2.5 * -a,
            this._scale = Math.min(1, Math.max(this._scale, 0)),
            this._scaleTeton = Math.min(1, Math.max(this._scaleTeton, 0)),
            this._scaleHold = Math.min(1, Math.max(this._scaleHold, 0)),
            this._arrowAlpha = Math.min(1, Math.max(this._arrowAlpha, 0)),
            this.holdProgress = Math.min(1, Math.max(this.holdProgress, 0)),
            this.holdScaleProgress = Math.min(1, Math.max(this.holdScaleProgress, 0)),
            this._imageAlpha = Math.min(1, Math.max(this._imageAlpha, 0))
        },
        update: function() {
            this.time += e.dt,
            this.updateMouseCoords(),
            this.updateTargets(),
            this.updateFactors(),
            this.loaded && this.draw(),
            BrowserDetect.isMobile || (this.view.style.transform = "translate3d(" + this.x + "px, " + this.y + "px, 0)")
        }
    },
    b.exports = d
}
, {
    "./cursor-manager": 65,
    "lib/mouse": 210,
    "lib/raf": 215,
    math: 220,
    "math/ease": 219
}],

40: [function(a, b, c) {
    function d(a, b) {
        this.catmullDef = a,
        this.flower = b,
        this.deformer = new h(!0),
        this.xform = new Float32Array(4)
    }
    function e(a) {
        this.chapter = a,
        this.scene = a.scene,
        this.hatchedShadow = new k,
        this.hatchedShadow.projector = this.chapter.camera,
        this.hatchedShadow.pattern = this.scene.cmnAssets.texs.get("hatching");
        var b = this.scene.cmnAssets.texs.get("windnoise");
        this.windDeform = new l(this.scene),
        this.windDeform.setTexture(b),
        this.morphes = [],
        this._init()
    }
    var f = a("glsl/standard")
      , g = (a("glsl/outline"),
    a("glsl/depthpass"))
      , h = a("glsl/effects/morph-deform")
      , i = a("glsl/effects/flower-tige-deform")
      , j = a("glsl/effects/flower-acceleration-deform")
      , k = (a("glsl/effects/blendshapes-deform"),
    a("glsl/effects/hatched-shadow"))
      , l = a("glsl/effects/wind-deform")
      , n = (a("nanogl-pbr/lib/input"),
    a("lib/color"),
    a("lib/mouse"),
    a("glsl/effects/pointColor"))
      , o = a("glsl/effects/noiseTitleMask")
      , p = a("math")
      , q = a("gl/masks");
    a("config");
    d.prototype = {
        update: function() {
            var a = this.catmullDef
              , b = this.xform;
            a.update(),
            i.computeParams(b, a, this.flower.openingProgress),
            b[3] = -.15;
            var c = 0 * a.spline_params[1] + a.spline_params[0]
              , d = 1
              , e = p.clamp01((c - b[1]) * b[3]);
            e = e * e * (3 - 2 * e),
            this.deformer.progress = d * e
        }
    };
    var r = {
        flower: {
            texture: "flower_color",
            doubleSided: !0,
            numTgt: 10,
            stdMorph: !0,
            po: [4, 4e3],
            fadStrength: 1
        },
        tige: {
            color: 7185818,
            doubleSided: !1,
            numTgt: 4,
            stdMorph: !1,
            po: [4, 4e3],
            fadStrength: 5
        }
    };
    e.prototype = {
        preRender: function() {
            for (var a = 0; a < this.morphes.length; a++)
                this.morphes[a].update();
            this.windDeform.preRender(),
            this.hatchedShadow.resolution.set([this.scene.glview.canvasWidth, this.scene.glview.canvasHeight])
        },
        opaqueConfig: function(a) {
            a.enableCullface(!0).enableDepthTest().depthMask(!0)
        },
        multiplyConfig: function(a) {
            var b = this.scene.gl;
            a.enableDepthTest().depthMask(!1).enableBlend().blendFunc(b.ZERO, b.SRC_COLOR)
        },
        makeDepthPass: function(a) {
            var b = new g(this.scene.gl);
            return b.setLightSetup(this.chapter.lights.setup),
            a.setupMaterial(b),
            b
        },
        getFlowerMaterial: function(a, b, c) {
            var e = a.name.split("/")[1]
              , g = r[e]
              , h = !!g.stdMorph
              , k = this.scene.gl;
            if (m = new f(k),
            this.opaqueConfig(m.config),
            g.doubleSided && m.config.enableCullface(!1),
            g.texture ? m.textureColor(this.chapter.texs.get(g.texture), m.uvs().scale(.5, .5).translate(.5 * Math.floor(1.9999 * Math.random()), .5 * Math.floor(1.9999 * Math.random())).token()) : m.solidColor(g.color),
            this.chapter.npr.addOutlinePass(m),
            m.outlineMaterial.iThickness.attachConstant(8e-4 / (this.scene.glview.pixelRatio > 1 ? .5 * this.scene.glview.pixelRatio : 1)),
            g.po && m.outlineMaterial.config.polygonOffset(g.po[0], g.po[1]),
            h) {
                var l = new d(b,c);
                this.morphes.push(l),
                m.addDeformer(l.deformer)
            } else {
                var l = new i(g.numTgt,b,c);
                m.addDeformer(l)
            }
            var n = new j(c);
            return n.strength = g.fadStrength,
            m.addDeformer(n),
            m.addDeformer(b),
            m.name = a.name,
            m._mask = m._mask | q.SHADOW_CASTER,
            m.compile(),
            m
        },
        _init: function() {
            var a, b = this.scene.gl, c = this.chapter.mats, d = this.chapter.texs, e = (c.lib,
            this.chapter.npr);
            this.grain5 = this.chapter.npr.makeGrain(.5);
            c.registerMaterial(e.dummyMaterial(), "flower"),
            c.registerMaterial(e.dummyMaterial(), "ailes"),
            c.registerMaterial(e.dummyMaterial(), "tige"),
            c.registerMaterial(e.dummyMaterial(), "feuilles"),
            d.makeTex("flower_color", "eclosion/flower_color.jpg").setFilter(!0, !0, !1).clamp().aniso(4),
            d.makeTex("plants_color", "eclosion/plants_color.jpg").setFilter(!0, !0, !1).clamp().aniso(4),
            d.makeTex("bush_texture_01", "eclosion/bush_texture_01.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("bush_texture_02", "eclosion/bush_texture_02.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("bush_texture_03", "eclosion/bush_texture_03.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("bush_texture_04", "eclosion/bush_texture_04.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_01", "eclosion/herbe_texture_01.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_02", "eclosion/herbe_texture_02.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_03", "eclosion/herbe_texture_03.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_04", "eclosion/herbe_texture_04.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_07", "eclosion/herbe_texture_07.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_texture_08", "eclosion/herbe_texture_08.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_trait_01", "eclosion/herbe_trait_01.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("herbe_trait_02", "eclosion/herbe_trait_02.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("clouds-atlas", "eclosion/clouds-atlas.png", b.RGBA).setFilter(!0, !0, !1).clamp().nobbc().genMipmap(),
            d.makeTex("trees", "eclosion/trees.png", b.RGBA).setFilter(!0, !1, !1).clamp().nobbc(),
            d.makeTex("RockShadow", "eclosion/RockShadow.png", b.RGBA).setFilter(!0, !1, !1).clamp().nobbc(),
            d.makeTex("palmier_texture_01", "eclosion/palmier_texture_01.png").setFilter(!0, !1, !1).clamp().nobbc(),
            d.makeTex("rocks", "eclosion/rocks.jpg").setFilter(!0, !1, !1).clamp().nobbc();
            var a = this.chapter.npr.makeStandard({
                color: 6189937,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Green_04");
            var a = this.chapter.npr.makeStandard({
                color: 14872815,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Cloud");
            var a = this.chapter.npr.makeStandard({
                color: 7527637,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Crystla");
            var a = this.chapter.npr.makeStandard({
                grain: .2,
                outlines: !0
            })
              , f = new n;
            f.setupMaterial(a),
            c.registerMaterial(a, "Sky");
            var a = this.chapter.npr.makeStandard({
                color: 11064485,
                grain: .2,
                outlines: !0
            });
            a.addDeformer(this.windDeform),
            a.outlineMaterial.config.enableCullface(!1).polygonOffset(4, 1500),
            c.registerMaterial(a, "Green_02");
            var a = this.chapter.npr.makeStandard({
                color: 6790803,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Green_05");
            var a = this.chapter.npr.makeStandard({
                color: 7582906,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Green_08");
            var a = this.chapter.npr.makeStandard({
                color: 5663340,
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Green_09");
            var a = this.chapter.npr.makeStandard({
                color: 13102543,
                grain: .2,
                outlines: !0
            });
            a.setLightSetup(this.chapter.lights.setup),
            this.hatchedShadow.setupMaterial(a),
            c.registerMaterial(a, "Green_01");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("rocks"),
                grain: .2,
                outlines: !0
            });
            c.registerMaterial(a, "Rock");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("plants_color"),
                grain: .2,
                outlines: !0,
                doublesided: !0
            });
            a.addDeformer(this.windDeform),
            c.registerMaterial(a, "UV_Texture"),
            c.registerMaterial(a, "DefaultMaterial"),
            a.outlineMaterial.config.enableCullface(!1).polygonOffset(10, 1e4);
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_texture_01"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_01");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_texture_02"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_02");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_texture_04"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_04");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_texture_07"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_07");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_texture_08"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_08");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_trait_01"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_trait_01");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("herbe_trait_02"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "herbe_texture_trait_02");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("bush_texture_01"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Bush_Texture_01");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("bush_texture_02"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Bush_Texture_02");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("bush_texture_03"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Bush_texture_03");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("bush_texture_04"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Bush_texture_04");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("RockShadow"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "RockShadow");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("clouds-atlas"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Cloud_texture");
            var a = this.chapter.npr.makeStandard({
                texture: d.get("trees"),
                grain: .2,
                alpha: !0,
                doublesided: !0
            });
            c.registerMaterial(a, "Background_trees");
            var a = this.chapter.npr.makeStandard({
                texture: this.scene.cmnAssets.texs.get("marieLouise"),
                grain: .2
            });
            a.config.enableBlend().depthMask(!1).enableDepthTest(!1).blendFunc(b.SRC_ALPHA, b.ONE_MINUS_SRC_ALPHA).enableCullface(!1),
            a._mask = 64,
            c.registerMaterial(a, "Marie_Louise");
            var a = this.chapter.npr.makeStandard({
                texture: this.scene.cmnAssets.texs.get("title-1"),
                grain: .2
            })
              , g = new o;
            g.setupMaterial(a),
            g.setTexture(this.scene.cmnAssets.texs.get("noiseHD")),
            c.registerMaterial(a, "Title"),
            this.title = g,
            a.config.enableBlend().blendFunc(b.SRC_ALPHA, b.ONE_MINUS_SRC_ALPHA).enableCullface(!1),
            a._mask = 2
        }
    },
    b.exports = e
}
, {
    config: 118,
    "gl/masks": 125,
    "glsl/depthpass": 131,
    "glsl/effects/blendshapes-deform": 136,
    "glsl/effects/flower-acceleration-deform": 143,
    "glsl/effects/flower-tige-deform": 145,
    "glsl/effects/hatched-shadow": 151,
    "glsl/effects/morph-deform": 153,
    "glsl/effects/noiseTitleMask": 157,
    "glsl/effects/pointColor": 158,
    "glsl/effects/wind-deform": 170,
    "glsl/outline": 174,
    "glsl/standard": 187,
    "lib/color": 194,
    "lib/mouse": 210,
    math: 220,
    "nanogl-pbr/lib/input": 261
}],