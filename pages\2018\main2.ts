/*
 * @Author: hongbin
 * @Date: 2025-07-06 21:58:42
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-31 20:44:28
 * @Description:
 */
import * as THREE from "three";
import { ThreeHelper } from "@/src/ThreeHelper";
import { MethodBaseSceneSet, LoadGLTF } from "@/src/ThreeHelper/decorators";
import { MainScreen } from "@/src/components/Three/Canvas";
import { Injectable } from "@/src/ThreeHelper/decorators/DI";
import EventMesh from "@/src/ThreeHelper/decorators/EventMesh";
import type { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import type { GUI } from "dat.gui";
import type { GLTF } from "three/examples/jsm/loaders/GLTFLoader";
import { gsap } from "gsap";
import { CustomEase } from "gsap/CustomEase";
import { usedEffect } from "./function/usedEffect";

import { handleSceneModel } from "./function/part2/handleSceneModel";
import { TempAudioPlayback } from "../2019/function/TempAudioPlayback";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";
import { addStatusFloor } from "./function/floor";
import { updateMeshPhysics } from "./function/updateMeshPhysics";
import { createFragment } from "./function/createFragment";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { handleMouseDown } from "./function/part2/handleMouseDown";
import { handleMouseMove } from "./function/part2/handleMouseMove";

const p1 = new THREE.Vector3();
const p2 = new THREE.Vector3();

type Mesh<T extends THREE.MeshBasicMaterial = THREE.MeshBasicMaterial> = THREE.Mesh<THREE.BufferGeometry, T>;

@Injectable
export class Main extends MainScreen {
    static instance: Main;
    clock = new THREE.Clock();
    iTime = { value: 1 };
    selectedObjects: THREE.Object3D[] = [];
    world = new CANNON.World();
    dynamicBodies: [THREE.Object3D, CANNON.Body][] = [];
    floorBody?: CANNON.Body;

    sphere?: [THREE.Object3D, CANNON.Body];
    fragment?: ReturnType<typeof createFragment>;

    startDrag = false;
    downPoint = new THREE.Vector3();
    handleSphere?: THREE.Mesh;
    lookAtObject:THREE.Object3D[] = []

    constructor(public helper: ThreeHelper) {
        super(helper);
        helper.main = this;
        Main.instance = this;

        this.world.gravity.set(0, -9.18, 0);

        const { ScrollTrigger } = require("gsap/ScrollTrigger");

        gsap.registerPlugin(ScrollTrigger);
        gsap.registerPlugin(CustomEase);

        this.init();
    }

    @MethodBaseSceneSet({
        addAxis: 1,
        fov: 30,
        cameraPosition: new THREE.Vector3(6, 5, 6.2),
        // cameraPosition: new THREE.Vector3(0, 10, 0),
        cameraTarget: new THREE.Vector3(0, 0, 0),
        useRoomLight: true,
    })
    async init() {
        this.helper.renderer.setPixelRatio(1);

        this.helper.controls.enabled = false;
        this.initShadow();
        // usedEffect(this);
        // 加载模型等
        await this.handleScene();

    }

    /**
     * 鼠标按下
     */
    @EventMesh.OnMouseDown(Main)
    onMouseDown(RayInfo: BackIntersection, _: undefined, event: MouseEvent) {
        // 点击目标需要是canvas
        if (RayInfo.object && (<HTMLElement>event.target).tagName == "CANVAS") {
            handleMouseDown(this, RayInfo, event);
        }
    }

    /**
     * 鼠标移动
     */
    @EventMesh.OnMouseMove(Main)
    onMouseMove(event: MouseEvent) {
        if ((<HTMLElement>event.target).tagName == "CANVAS") {
            handleMouseMove(this, event);
        }
    }

    /**
     * 鼠标抬起
     */
    @EventMesh.OnMouseUp(Main)
    onMouseUp(event: MouseEvent) {
        this.startDrag = false;
        EventMesh.enabledMouseMove = false;

        if ((<HTMLElement>event.target).tagName != "CANVAS") return;

        if (this.sphere) {
        }
    }

    async handleScene() {
        // EventMesh.setIntersectObjects([]);

        await this.loadModel();
    }

    initShadow() {
        const light = new THREE.PointLight(0xffffff, 1, 100);
        light.position.set(2, 10, 1);
        light.castShadow = true; // default false
        this.helper.scene.add(light);

        //Set up shadow properties for the light
        light.shadow.mapSize.width = 512 * 4; // default
        light.shadow.mapSize.height = 512 * 4; // default
        light.shadow.camera.near = 0.1; // default
        light.shadow.camera.far = 20; // default
        light.shadow.intensity = 300;
        light.shadow.bias = 0.02;

        this.helper.renderer.shadowMap.enabled = true;
        this.helper.renderer.shadowMap.type = THREE.PCFShadowMap;
    }

    @LoadGLTF("/public/models/2018/弹珠.glb")
    async loadModel(gltf?: GLTF) {
        if (gltf) handleSceneModel(this, gltf);
    }

    @ThreeHelper.InjectAnimation(Main)
    animation() {
        const delta = this.clock.getDelta();
        this.iTime.value += delta;

        this.world.step(1 / 60, delta, 20);

        updateMeshPhysics(this.dynamicBodies, (mesh, body) => {
            // if (mesh.name == "ball") {
            //     mesh.userData.sync && mesh.userData.sync();
            // }
        });
    }

    @ThreeHelper.AddGUI(Main)
    Gui(gui: GUI) {
        if (gui) {
            gui.addFunction(() => {}).name("🎉");
        }
    }

    addSelectedObjects(Object3D: Object3D) {}
}
