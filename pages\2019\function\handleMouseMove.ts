import { Main } from "../main";

export const handleMouseMove = (self: Main, event: MouseEvent, call?: (x: number, y: number, angle: number) => void) => {
    self.prevPoint = { x: event.clientX, y: event.clientY };

    let lastPoint = self.pathData[self.pathData.length - 1]; // 获取上一个点
    let currentPoint = { x: event.clientX, y: event.clientY };

    if (lastPoint) {
        let dx = currentPoint.x - lastPoint.x;
        let dy = currentPoint.y - lastPoint.y;
        let distance = Math.sqrt(dx * dx + dy * dy);

        let step = 5; // 插值步长，值越小插值点越多
        if (distance > step) {
            let numSteps = Math.ceil(distance / step);
            for (let i = 1; i < numSteps; i++) {
                let intermediateX = lastPoint.x + (dx * i) / numSteps;
                let intermediateY = lastPoint.y + (dy * i) / numSteps;

                const point = { x: intermediateX, y: intermediateY };

                self.pathData.push(point);

                const angle = self.getAngle(self.basePoint, point);

                call?.(intermediateX, intermediateY, angle);
            }
        }
    }

    self.pathData.push(currentPoint);
    if (self.pathData.length > 2) {
        const angle = self.getAngle(self.basePoint, currentPoint);

        call?.(currentPoint.x, currentPoint.y, angle);
    }
    // }
};
