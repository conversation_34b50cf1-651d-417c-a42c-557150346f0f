/*
 * @Author: hongbin
 * @Date: 2025-06-15 20:49:32
 * @LastEditors: hongbin
 * @LastEditTime: 2025-06-20 10:44:29
 * @Description:
 */

export function TempAudioPlayback(url: string, volume = 1, loop = false) {
    //创建一个临时的音频播放 播放音频
    const audio = new Audio();
    // audio.src = "/music/IT-Grass.mp3";
    audio.src = url.replace("/public", "");
    audio.play();
    audio.volume = volume;

    if (loop) {
        audio.loop = true;
    }

    const vc = () => {
        if (document.visibilityState === "hidden") {
            console.log("用户离开了标签页");
            if (!audio.paused) {
                audio.pause();
            }
        }
    };

    const f = () => {
        console.log("窗口获得焦点");
        audio.play().catch((err) => {
            // 某些浏览器可能需要用户交互才能播放
            console.warn("音频恢复失败:", err);
        });
    };

    window.addEventListener("focus", f);

    document.addEventListener("visibilitychange", vc);

    audio.onended = () => {
        audio.remove();
        document.removeEventListener("visibilitychange", vc);
        window.removeEventListener("focus", f);
    };

    // audio.playbackRate = 2

    return audio;
}
