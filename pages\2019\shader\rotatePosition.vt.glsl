varying vec2 vUv;
varying vec3 vPos;
varying float vRotationAngle;
uniform float scalePosX;
uniform float scalePosY;
uniform float pillar;
uniform float leaveProgress;
uniform sampler2D rotateTexture;
uniform sampler2D posTexture;

#include <common>
#include <skinning_pars_vertex>
#include <normal_pars_vertex>
#include <morphtarget_pars_vertex>

void main() {
    vUv = uv;

    // float uvY = clamp(position.z, 0., 1.);

    // float rotationAngle = texture2D(rotateTexture, vec2(0, uvY)).r;
    // float rotationAngle1 = texture2D(rotateTexture, vec2(0, uvY + 0.03)).r;
    // vec2 diffPos = texture2D(posTexture, vec2(0., uvY)).rg;

    #include <morphinstance_vertex>
	#include <morphcolor_vertex>

	#include <beginnormal_vertex>
    #include <morphnormal_vertex>
	#include <skinbase_vertex>
	#include <skinnormal_vertex>
	#include <defaultnormal_vertex>
	#include <normal_vertex>

	#include <begin_vertex>
	// #include <morphtarget_vertex>

    vec3 oldTransformed = transformed;

    transformed.z -= leaveProgress;
    // oldTransformed.z -= leaveProgress;

    // 形态键处理
    transformed *= morphTargetBaseInfluence;
    oldTransformed *= morphTargetBaseInfluence;

    vec4 mp = modelMatrix * vec4(position, 1.0);
    float mpScale = clamp((mp.y + 6.0) / 7., 0., 1.);
    mpScale = .0;

    for(int i = 0; i < MORPHTARGETS_COUNT; i++) {
        // 不等于0的形态键 应用影响
        // if(i == 0){
        //     mpScale = 0.0;
        // }

        if(morphTargetInfluences[i] != 0.0) {
            vec3 morphTrans = getMorph(gl_VertexID, i, 0).xyz * morphTargetInfluences[i] * (1.0 - mpScale);
            transformed += morphTrans;
            oldTransformed += morphTrans;
        }
    }

    // float uvY = clamp(transformed.z, 0., 1.);
    // uv要保持原来的 不能 使用 -leaveProgress的向量 会导应该隐藏的部分出现
    float uvY = clamp(transformed.z, 0., 1.);
    // float uvY = smoothstep(0., 1., transformed.z);

    float rotationAngle = texture2D(rotateTexture, vec2(0, uvY)).r;
    float rotationAngle1 = texture2D(rotateTexture, vec2(0, uvY + 0.03)).r;

    // transformed
    // 创建一个旋转矩阵，围绕 Z 轴旋转
    // mat4 rotationMatrix = mat4(cos(rotationAngle), -sin(rotationAngle), 0.0, 0.0, sin(rotationAngle), cos(rotationAngle), 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0);
    // 创建绕 Y 轴的旋转矩阵
    mat4 rotationMatrix = mat4(cos(rotationAngle), 0.0, sin(rotationAngle), 0.0, 0.0, 1.0, 0.0, 0.0, -sin(rotationAngle), 0.0, cos(rotationAngle), 0.0, 0.0, 0.0, 0.0, 1.0);

    // 创建一个绕 Y 轴旋转 180° 的矩阵
    mat4 rotationY180 = mat4(-1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 1.0);

    vPos = oldTransformed;

    vRotationAngle = rotationAngle1;

    // transformed.x += diffPos.r;
    // transformed.y = diffPos.g;
    // transformed.z = transformed.z;

    // vec4 transformedPosition = rotationMatrix * vec4(position, 1.0);

    float leaf = transformed.x;
    // 枝叶部分
    if(abs(leaf) > 0.011) {
        vec4 wp = modelMatrix * vec4(transformed, 1.0);

        float scale = clamp(2.5 * (wp.y + 6.0) / 7., 0., 1.);
        transformed.x *= scale;
        transformed.y *= scale;

        // float scale = clamp(transformed.z * 2.5, 0., 1.);
        // transformed.x *= scale;
        // transformed.y *= scale;

        // transformed.x *= scalePosX * (smoothstep(rand(vec2(transformed.z)), 0.995, 1.));
        transformed.x *= scalePosX;
        // transformed.y *= scalePosY;
        // transformed.y *= smoothstep(rand(vec2(transformed.z)), 0.99, 1.1);
    } else {
        // 给茎部除了叶子部分 增加一点扭曲
        // transformed.x *= scalePosX * pillar * (smoothstep(0., 0.21, rand(uv)));
        // transformed.y *= scalePosY * pillar;
    }

    vec3 rTransformed = (rotationMatrix * vec4(transformed, 1.0)).xyz;

    // rTransformed.x *= scalePosX;
    // rTransformed.y *= scalePosY;

    transformed = rTransformed;

	#include <skinning_vertex>

	#include <project_vertex>

    // // 变换顶点位置
    // // vec4 transformedPosition = rotationMatrix * vec4(position, 1.0);
    // vec4 transformedPosition = vec4(position, 1.0);

    // // 输出最终的顶点位置
    // gl_Position = projectionMatrix * modelViewMatrix * transformedPosition;
}