/*
 * @Author: hongbin
 * @Date: 2025-06-30 19:54:08
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-06 21:38:41
 * @Description:
 */
import * as THREE from "three";
import { Main } from "../main";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";

/**
 * 创建一个cannon-es的静态地板
 */
export const addStatusFloor = (self: Main) => {
    const size = 30;

    const floorMesh = new THREE.Mesh(
        new THREE.BoxGeometry(size, 0.01, size),
        new THREE.MeshStandardMaterial({
            color: new THREE.Color("#D9D9D6"),
            map: self.helper.loadTexture("/public/textures/2019/bg-tile.png", (t) => {
                t.wrapS = t.wrapT = THREE.RepeatWrapping;
                t.repeat.set(4, 4);
                t.colorSpace = THREE.SRGBColorSpace;
            }),
            roughness: 1,
            metalness: 0.3,
        })
    );

    floorMesh.position.y -= 0.05;

    floorMesh.receiveShadow = true;

    self.helper.add(floorMesh);

    //形状
    const floorShape = new CANNON.Box(new CANNON.Vec3(15, 0.05, 15));

    const floorBody = new CANNON.Body({ mass: 0, material: new CANNON.Material("floor") });

    floorBody.addShape(floorShape);

    floorBody.position.set(0, -0.05, 0);

    self.world.addBody(floorBody);

    self.floorBody = floorBody;

    floorBody.addEventListener("collide", (event: any) => {
        if (!event.body.mass) return;

        // console.log('collide:',)

        if (event.body.material.name == "ball_material") {
            console.log("落地");
        }

        // const volume = event.contact.getImpactVelocityAlongNormal();

        // const distance = this.helper.camera.position.distanceTo(event.target.position);

        // 过滤低强度碰撞和距离衰减
        // if (volume > 1 && distance < 30) {
        // collisionAudio.currentTime = 0; // 确保从头播放
        // collisionAudio.volume = (Math.min(volume / 10, 1) * distance) / 30;
        // collisionAudio.play();
        // }
    });
};
