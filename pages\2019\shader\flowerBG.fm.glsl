varying vec2 vUv;
varying vec3 vPos;
varying vec3 vNormal;
varying vec3 VVNormal;
varying vec3 vViewPosition;
uniform vec3 color;
uniform vec3 cameraPos;

void main() {

    // vec3 fdx = dFdx(vViewPosition);
    // vec3 fdy = dFdy(vViewPosition);
    // vec3 normal = normalize(cross(fdx, fdy));

    // vec3 geometryPosition = -vViewPosition;

    // float diff = pow(max(dot(normalize(cameraPos - geometryPosition), VVNormal), .0), 1.);

    if(vPos.z < 0.149) {
        if(length(vPos.xz) > 0.032) {
            discard;
        }
    }

    // if(vPos.z > 0.016418 || length(vPos.xy) < 0.0001856) {

    // gl_FragColor = vec4(vec3(VVNormal), 1.0);
    // gl_FragColor = vec4(vec3(length(vPos.xy) * 10.), 1.0);

    gl_FragColor = vec4(vec3(0.), 1.0);
}