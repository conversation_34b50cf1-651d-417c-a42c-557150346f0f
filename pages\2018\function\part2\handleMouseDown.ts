/*
 * @Author: hongbin
 * @Date: 2025-07-08 20:59:16
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-31 18:32:02
 * @Description:
 */
import * as THREE from "three";
import EventMesh, { BackIntersection } from "@/src/ThreeHelper/decorators/EventMesh";
import { Main } from "../../main2";

const vec = new THREE.Vector3();
const pos = new THREE.Vector3();

export const handleMouseDown = (self: Main, RayInfo: BackIntersection, event: MouseEvent) => {
    if (RayInfo.object.name == "手柄" && self.handleSphere) {
        // console.log("开始计算拖拽和拉伸", RayInfo.point);
        // console.log("小球初始位置：", RayInfo.object.position);
        // console.log("event.button:",event.button)

        const point = {
            x: event.clientX,
            y: event.clientY,
        };

        // 获取当前鼠标位置对应3维坐标
        // const worldPos = pointToWorld(point.x, point.y, self.helper.camera, RayInfo.point.y);

        // console.log(worldPos);

        const diffPos = self.handleSphere.position.clone().sub(RayInfo.point);

        self.handleSphere.userData.diffPos = diffPos;

        self.startDrag = true;
        self.downPoint.copy(RayInfo.point);
        // self.downPoint.copy(self.handleSphere.position);

        EventMesh.enabledMouseMove = true;
    }
};
