/*
 * @Author: hongbin
 * @Date: 2025-06-30 19:58:22
 * @LastEditors: hongbin
 * @LastEditTime: 2025-07-03 19:25:00
 * @Description:
 */
import * as THREE from "three";
import * as CANNON from "@/src/ThreeHelper/addons/cannon-es/cannon-es";

/** 同步物理世界和3D世界的状态 */
export const updateMeshPhysics = (
    MeshPhysics: [THREE.Object3D, CANNON.Body][],
    call?: (mesh: THREE.Object3D, body: CANNON.Body) => void
) => {
    for (let i = 0; i < MeshPhysics.length; i++) {
        const [mesh, body] = MeshPhysics[i];

        const position = body.interpolatedPosition;
        const quaternion = body.interpolatedQuaternion;

        mesh.position.copy(position);
        // mesh.quaternion.copy(quaternion);

        call && call(mesh, body);

        // if (this.showAABB) {
        //     body.updateAABB();
        //     // @ts-ignore
        //     const box3 = new THREE.Box3(body.aabb.lowerBound, body.aabb.upperBound);

        //     const box3helper = new THREE.Box3Helper(box3);

        //     // box3helper.rotation.copy(mesh.rotation);

        //     mesh.parent && mesh.parent.add(box3helper);

        //     if (mesh.userData.box3helper) {
        //         mesh.parent && mesh.parent.remove(mesh.userData.box3helper);
        //     }

        //     mesh.userData.box3helper = box3helper;
        // } else {
        // if (mesh.userData.box3helper) {
        //     mesh.parent && mesh.parent.remove(mesh.userData.box3helper);
        //     mesh.userData.box3helper = undefined;
        // }
        // }
    }
};
