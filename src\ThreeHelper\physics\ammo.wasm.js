
// This is ammo.js, a port of Bullet Physics to JavaScript. zlib licensed.
var Ammo = function (Ammo) {
    Ammo = Ammo || {};
    var Module = Ammo;

    var b; b || (b = eval("(function() { try { return Ammo || {} } catch(e) { return {} } })()")); var aa = {}, ba; for (ba in b) b.hasOwnProperty(ba) && (aa[ba] = b[ba]); var ca = !1, da = !1, ea = !1, fa = !1;
    if (b.ENVIRONMENT) if ("WEB" === b.ENVIRONMENT) ca = !0; else if ("WORKER" === b.ENVIRONMENT) da = !0; else if ("NODE" === b.ENVIRONMENT) ea = !0; else if ("SHELL" === b.ENVIRONMENT) fa = !0; else throw Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL."); else ca = "object" === typeof window, da = "function" === typeof importScripts, ea = "object" === typeof process && "function" === typeof require && !ca && !da, fa = !ca && !ea && !da;
    if (ea) {
        b.print || (b.print = console.log); b.printErr || (b.printErr = console.warn); var ga, ha; b.read = function (a, c) { ga || (ga = require("fs")); ha || (ha = require("path")); a = ha.normalize(a); var d = ga.readFileSync(a); return c ? d : d.toString() }; b.readBinary = function (a) { a = b.read(a, !0); a.buffer || (a = new Uint8Array(a)); assert(a.buffer); return a }; b.load = function (a) { ja(read(a)) }; b.thisProgram || (b.thisProgram = 1 < process.argv.length ? process.argv[1].replace(/\\/g, "/") : "unknown-program"); b.arguments = process.argv.slice(2); "undefined" !==
            typeof module && (module.exports = b); process.on("uncaughtException", function (a) { if (!(a instanceof ka)) throw a; }); b.inspect = function () { return "[Emscripten Module object]" }
    } else if (fa) b.print || (b.print = print), "undefined" != typeof printErr && (b.printErr = printErr), b.read = "undefined" != typeof read ? read : function () { throw "no read() available"; }, b.readBinary = function (a) { if ("function" === typeof readbuffer) return new Uint8Array(readbuffer(a)); a = read(a, "binary"); assert("object" === typeof a); return a }, "undefined" != typeof scriptArgs ?
        b.arguments = scriptArgs : "undefined" != typeof arguments && (b.arguments = arguments), "function" === typeof quit && (b.quit = function (a) { quit(a) }), eval("if (typeof gc === 'function' && gc.toString().indexOf('[native code]') > 0) var gc = undefined"); else if (ca || da) b.read = function (a) { var c = new XMLHttpRequest; c.open("GET", a, !1); c.send(null); return c.responseText }, da && (b.readBinary = function (a) { var c = new XMLHttpRequest; c.open("GET", a, !1); c.responseType = "arraybuffer"; c.send(null); return c.response }), b.readAsync = function (a,
            c, d) { var e = new XMLHttpRequest; e.open("GET", a, !0); e.responseType = "arraybuffer"; e.onload = function () { 200 == e.status || 0 == e.status && e.response ? c(e.response) : d() }; e.onerror = d; e.send(null) }, "undefined" != typeof arguments && (b.arguments = arguments), "undefined" !== typeof console ? (b.print || (b.print = function (a) { console.log(a) }), b.printErr || (b.printErr = function (a) { console.warn(a) })) : b.print || (b.print = function () { }), da && (b.load = importScripts), "undefined" === typeof b.setWindowTitle && (b.setWindowTitle = function (a) {
                document.title =
                    a
            }); else throw "Unknown runtime environment. Where are we?"; function ja(a) { eval.call(null, a) } !b.load && b.read && (b.load = function (a) { ja(b.read(a)) }); b.print || (b.print = function () { }); b.printErr || (b.printErr = b.print); b.arguments || (b.arguments = []); b.thisProgram || (b.thisProgram = "./this.program"); b.quit || (b.quit = function (a, c) { throw c; }); b.print = b.print; b.h = b.printErr; b.preRun = []; b.postRun = []; for (ba in aa) aa.hasOwnProperty(ba) && (b[ba] = aa[ba]);
    var aa = void 0, k = {
        e: function (a) { return tempRet0 = a }, J: function () { return tempRet0 }, N: function () { return la }, M: function (a) { la = a }, s: function (a) { switch (a) { case "i1": case "i8": return 1; case "i16": return 2; case "i32": return 4; case "i64": return 8; case "float": return 4; case "double": return 8; default: return "*" === a[a.length - 1] ? k.j : "i" === a[0] ? (a = parseInt(a.substr(1)), assert(0 === a % 8), a / 8) : 0 } }, H: function (a) { return Math.max(k.s(a), k.j) }, O: 16, aa: function (a, c) {
            "double" === c || "i64" === c ? a & 7 && (assert(4 === (a & 7)), a += 4) : assert(0 ===
                (a & 3)); return a
        }, U: function (a, c, d) { return d || "i64" != a && "double" != a ? a ? Math.min(c || (a ? k.H(a) : 0), k.j) : Math.min(c, 8) : 8 }, l: function (a, c, d) { return d && d.length ? b["dynCall_" + a].apply(null, [c].concat(d)) : b["dynCall_" + a].call(null, c) }, g: [], v: function (a) { for (var c = 0; c < k.g.length; c++)if (!k.g[c]) return k.g[c] = a, 2 * (1 + c); throw "Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."; }, L: function (a) { k.g[(a - 2) / 2] = null }, f: function (a) { k.f.n || (k.f.n = {}); k.f.n[a] || (k.f.n[a] = 1, b.h(a)) },
        m: {}, W: function (a, c) { assert(c); k.m[c] || (k.m[c] = {}); var d = k.m[c]; d[a] || (d[a] = 1 === c.length ? function () { return k.l(c, a) } : 2 === c.length ? function (d) { return k.l(c, a, [d]) } : function () { return k.l(c, a, Array.prototype.slice.call(arguments)) }); return d[a] }, V: function () { throw "You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"; }, t: function (a) { var c = la; la = la + a | 0; la = la + 15 & -16; return c }, u: function (a) { var c = ma; ma = ma + a | 0; ma = ma + 15 & -16; return c },
        F: function (a) { var c = m[na >> 2]; a = (c + a + 15 | 0) & -16; m[na >> 2] = a; if (a = a >= oa) qa(), a = !0; return a ? (m[na >> 2] = c, 0) : c }, p: function (a, c) { return Math.ceil(a / (c ? c : 16)) * (c ? c : 16) }, $: function (a, c, d) { return d ? +(a >>> 0) + 4294967296 * +(c >>> 0) : +(a >>> 0) + 4294967296 * +(c | 0) }, i: 1024, j: 4, P: 0
    }; k.addFunction = k.v; k.removeFunction = k.L; var ra = 0; function assert(a, c) { a || sa("Assertion failed: " + c) }
    function ta(a) { var c; c = "i32"; "*" === c.charAt(c.length - 1) && (c = "i32"); switch (c) { case "i1": return ua[a >> 0]; case "i8": return ua[a >> 0]; case "i16": return va[a >> 1]; case "i32": return m[a >> 2]; case "i64": return m[a >> 2]; case "float": return wa[a >> 2]; case "double": return xa[a >> 3]; default: sa("invalid type for setValue: " + c) }return null }
    function ya(a, c, d) {
        var e, f, g; "number" === typeof a ? (f = !0, g = a) : (f = !1, g = a.length); var h = "string" === typeof c ? c : null, l; 4 == d ? l = e : l = ["function" === typeof za ? za : k.u, k.t, k.u, k.F][void 0 === d ? 2 : d](Math.max(g, h ? 1 : c.length)); if (f) { e = l; assert(0 == (l & 3)); for (a = l + (g & -4); e < a; e += 4)m[e >> 2] = 0; for (a = l + g; e < a;)ua[e++ >> 0] = 0; return l } if ("i8" === h) return a.subarray || a.slice ? Aa.set(a, l) : Aa.set(new Uint8Array(a), l), l; e = 0; for (var n, v; e < g;) {
            var x = a[e]; "function" === typeof x && (x = k.X(x)); d = h || c[e]; if (0 === d) e++; else {
                "i64" == d && (d = "i32");
                f = l + e; var F = d, F = F || "i8"; "*" === F.charAt(F.length - 1) && (F = "i32"); switch (F) {
                    case "i1": ua[f >> 0] = x; break; case "i8": ua[f >> 0] = x; break; case "i16": va[f >> 1] = x; break; case "i32": m[f >> 2] = x; break; case "i64": tempI64 = [x >>> 0, (tempDouble = x, 1 <= +Ba(tempDouble) ? 0 < tempDouble ? (Ca(+Da(tempDouble / 4294967296), 4294967295) | 0) >>> 0 : ~~+Ea((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)]; m[f >> 2] = tempI64[0]; m[f + 4 >> 2] = tempI64[1]; break; case "float": wa[f >> 2] = x; break; case "double": xa[f >> 3] = x; break; default: sa("invalid type for setValue: " +
                        F)
                }v !== d && (n = k.s(d), v = d); e += n
            }
        } return l
    } function Fa(a) { var c; if (0 === c || !a) return ""; for (var d = 0, e, f = 0; ;) { e = Aa[a + f >> 0]; d |= e; if (0 == e && !c) break; f++; if (c && f == c) break } c || (c = f); e = ""; if (128 > d) { for (; 0 < c;)d = String.fromCharCode.apply(String, Aa.subarray(a, a + Math.min(c, 1024))), e = e ? e + d : d, a += 1024, c -= 1024; return e } return b.UTF8ToString(a) } var Ga = "undefined" !== typeof TextDecoder ? new TextDecoder("utf8") : void 0;
    function Ha(a, c, d, e) {
        if (0 < e) {
            e = d + e - 1; for (var f = 0; f < a.length; ++f) {
                var g = a.charCodeAt(f); 55296 <= g && 57343 >= g && (g = 65536 + ((g & 1023) << 10) | a.charCodeAt(++f) & 1023); if (127 >= g) { if (d >= e) break; c[d++] = g } else {
                    if (2047 >= g) { if (d + 1 >= e) break; c[d++] = 192 | g >> 6 } else {
                        if (65535 >= g) { if (d + 2 >= e) break; c[d++] = 224 | g >> 12 } else { if (2097151 >= g) { if (d + 3 >= e) break; c[d++] = 240 | g >> 18 } else { if (67108863 >= g) { if (d + 4 >= e) break; c[d++] = 248 | g >> 24 } else { if (d + 5 >= e) break; c[d++] = 252 | g >> 30; c[d++] = 128 | g >> 24 & 63 } c[d++] = 128 | g >> 18 & 63 } c[d++] = 128 | g >> 12 & 63 } c[d++] =
                            128 | g >> 6 & 63
                    } c[d++] = 128 | g & 63
                }
            } c[d] = 0
        }
    } function Ia(a) { for (var c = 0, d = 0; d < a.length; ++d) { var e = a.charCodeAt(d); 55296 <= e && 57343 >= e && (e = 65536 + ((e & 1023) << 10) | a.charCodeAt(++d) & 1023); 127 >= e ? ++c : c = 2047 >= e ? c + 2 : 65535 >= e ? c + 3 : 2097151 >= e ? c + 4 : 67108863 >= e ? c + 5 : c + 6 } return c } "undefined" !== typeof TextDecoder && new TextDecoder("utf-16le");
    function Ja(a) { return a.replace(/__Z[\w\d_]+/g, function (a) { var d; a: { var e = b.___cxa_demangle || b.__cxa_demangle; if (e) try { var f = a.substr(1), g = Ia(f) + 1, h = za(g); Ha(f, Aa, h, g); var l = za(4), n = e(h, 0, 0, l); if (0 === ta(l) && n) { d = Fa(n); break a } } catch (v) { } finally { h && Ka(h), l && Ka(l), n && Ka(n) } else k.f("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"); d = a } return a === d ? a : a + " [" + d + "]" }) }
    function La() { var a; a: { a = Error(); if (!a.stack) { try { throw Error(0); } catch (c) { a = c } if (!a.stack) { a = "(no stack trace available)"; break a } } a = a.stack.toString() } b.extraStackTrace && (a += "\n" + b.extraStackTrace()); return Ja(a) } var buffer, ua, Aa, va, Ma, m, Oa, wa, xa;
    function Pa() { b.HEAP8 = ua = new Int8Array(buffer); b.HEAP16 = va = new Int16Array(buffer); b.HEAP32 = m = new Int32Array(buffer); b.HEAPU8 = Aa = new Uint8Array(buffer); b.HEAPU16 = Ma = new Uint16Array(buffer); b.HEAPU32 = Oa = new Uint32Array(buffer); b.HEAPF32 = wa = new Float32Array(buffer); b.HEAPF64 = xa = new Float64Array(buffer) } var Qa, ma, Ra, la, Sa, Ta, na; Qa = ma = Ra = la = Sa = Ta = na = 0;
    function qa() { sa("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value " + oa + ", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ") } var Ua = b.TOTAL_STACK || 5242880, oa = b.TOTAL_MEMORY || 67108864; oa < Ua && b.h("TOTAL_MEMORY should be larger than TOTAL_STACK, was " + oa + "! (TOTAL_STACK=" + Ua + ")");
    b.buffer ? buffer = b.buffer : "object" === typeof WebAssembly && "function" === typeof WebAssembly.Memory ? (b.wasmMemory = new WebAssembly.Memory({ initial: oa / 65536, maximum: oa / 65536 }), buffer = b.wasmMemory.buffer) : buffer = new ArrayBuffer(oa); Pa(); m[0] = 1668509029; va[1] = 25459; if (115 !== Aa[2] || 99 !== Aa[3]) throw "Runtime error: expected the system to be little-endian!"; b.HEAP = void 0; b.buffer = buffer; b.HEAP8 = ua; b.HEAP16 = va; b.HEAP32 = m; b.HEAPU8 = Aa; b.HEAPU16 = Ma; b.HEAPU32 = Oa; b.HEAPF32 = wa; b.HEAPF64 = xa;
    function Va(a) { for (; 0 < a.length;) { var c = a.shift(); if ("function" == typeof c) c(); else { var d = c.G; "number" === typeof d ? void 0 === c.k ? b.dynCall_v(d) : b.dynCall_vi(d, c.k) : d(void 0 === c.k ? null : c.k) } } } var Wa = [], Xa = [], Ya = [], Za = [], $a = [], ab = !1; function bb() { var a = b.preRun.shift(); Wa.unshift(a) } function cb(a) { var c = Array(Ia(a) + 1); Ha(a, c, 0, c.length); return c } Math.imul && -5 === Math.imul(4294967295, 5) || (Math.imul = function (a, c) { var d = a & 65535, e = c & 65535; return d * e + ((a >>> 16) * e + d * (c >>> 16) << 16) | 0 }); Math.Y = Math.imul;
    if (!Math.fround) { var eb = new Float32Array(1); Math.fround = function (a) { eb[0] = a; return eb[0] } } Math.T = Math.fround; Math.clz32 || (Math.clz32 = function (a) { a = a >>> 0; for (var c = 0; 32 > c; c++)if (a & 1 << 31 - c) return c; return 32 }); Math.R = Math.clz32; Math.trunc || (Math.trunc = function (a) { return 0 > a ? Math.ceil(a) : Math.floor(a) }); Math.trunc = Math.trunc; var Ba = Math.abs, Ea = Math.ceil, Da = Math.floor, fb = Math.pow, Ca = Math.min, gb = 0, hb = null, ib = null; function jb() { gb++; b.monitorRunDependencies && b.monitorRunDependencies(gb) }
    function kb() { gb--; b.monitorRunDependencies && b.monitorRunDependencies(gb); if (0 == gb && (null !== hb && (clearInterval(hb), hb = null), ib)) { var a = ib; ib = null; a() } } b.preloadedImages = {}; b.preloadedAudios = {}; var lb = null;
    (function (a) {
        function c(a, c) { var d = F; if (0 > a.indexOf(".")) d = (d || {})[a]; else var e = a.split("."), d = (d || {})[e[0]], d = (d || {})[e[1]]; c && (d = (d || {})[c]); void 0 === d && sa("bad lookupImport to (" + a + ")." + c); return d } function d(c) {
            var d = a.buffer; c.byteLength < d.byteLength && a.printErr("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here"); var d = new Int8Array(d), e = new Int8Array(c); lb || d.set(e.subarray(a.STATIC_BASE, a.STATIC_BASE + a.STATIC_BUMP), a.STATIC_BASE); e.set(d);
            b.buffer = buffer = c; Pa()
        } function e() { var c; if (a.wasmBinary) c = a.wasmBinary, c = new Uint8Array(c); else if (a.readBinary) c = a.readBinary(v); else throw "on the web, we need the wasm binary to be preloaded and set on Module['wasmBinary']. emcc.py will do that for you when generating HTML (but not JS)"; return c } function f() { return a.wasmBinary || "function" !== typeof fetch ? new Promise(function (a) { a(e()) }) : fetch(v).then(function (a) { if (!a.ok) throw "failed to load wasm binary file at '" + v + "'"; return a.arrayBuffer() }) }
        function g(c, d, e) { if ("function" !== typeof a.asm || a.asm === wb) a.asmPreload ? a.asm = a.asmPreload : eval(a.read(x)); return "function" !== typeof a.asm ? (a.printErr("asm evalling did not set the module properly"), !1) : a.asm(c, d, e) } function h(c, e) {
            function g(c) { Na = c.exports; Na.memory && d(Na.memory); a.asm = Na; a.usingWasm = !0; kb() } if ("object" !== typeof WebAssembly) return a.printErr("no native wasm support detected"), !1; if (!(a.wasmMemory instanceof WebAssembly.Memory)) return a.printErr("no native wasm Memory in use"), !1;
            e.memory = a.wasmMemory; F.global = { NaN: NaN, Infinity: Infinity }; F["global.Math"] = c.Math; F.env = e; jb(); if (a.instantiateWasm) try { return a.instantiateWasm(F, g) } catch (h) { return a.printErr("Module.instantiateWasm callback failed with error: " + h), !1 } f().then(function (a) { return WebAssembly.instantiate(a, F) }).then(function (a) { g(a.instance) }).catch(function (c) { a.printErr("failed to asynchronously prepare wasm: " + c); a.quit(1, c) }); return {}
        } var l = a.wasmJSMethod || "native-wasm"; a.wasmJSMethod = l; var n = a.wasmTextFile ||
            "ammo.wasm.wast", v = a.wasmBinaryFile || "/ammo.wasm.wasm", x = a.asmjsCodeFile || "ammo.wasm.temp.asm.js"; "function" === typeof a.locateFile && (n = a.locateFile(n), v = a.locateFile(v), x = a.locateFile(x)); var F = {
                global: null, env: null, asm2wasm: { "f64-rem": function (a, c) { return a % c }, "f64-to-int": function (a) { return a | 0 }, "i32s-div": function (a, c) { return (a | 0) / (c | 0) | 0 }, "i32u-div": function (a, c) { return (a >>> 0) / (c >>> 0) >>> 0 }, "i32s-rem": function (a, c) { return (a | 0) % (c | 0) | 0 }, "i32u-rem": function (a, c) { return (a >>> 0) % (c >>> 0) >>> 0 }, "debugger": function () { debugger } },
                parent: a
            }, Na = null; a.asmPreload = a.asm; a.reallocBuffer = function (c) { var d = a.usingWasm ? 65536 : 16777216; 0 < c % d && (c += d - c % d); var d = a.buffer, e = d.byteLength; if (a.usingWasm) try { return -1 !== a.wasmMemory.grow((c - e) / 65536) ? a.buffer = a.wasmMemory.buffer : null } catch (f) { return null } else return Na.__growWasmMemory((c - e) / 65536), a.buffer !== d ? a.buffer : null }; a.asm = function (f, v, wb) {
                if (!v.table) {
                    var vb = a.wasmTableSize; void 0 === vb && (vb = 1024); var Mb = a.wasmMaxTableSize; v.table = "object" === typeof WebAssembly && "function" === typeof WebAssembly.Table ?
                        void 0 !== Mb ? new WebAssembly.Table({ initial: vb, maximum: Mb, element: "anyfunc" }) : new WebAssembly.Table({ initial: vb, element: "anyfunc" }) : Array(vb); a.wasmTable = v.table
                } v.memoryBase || (v.memoryBase = a.STATIC_BASE); v.tableBase || (v.tableBase = 0); for (var V, vb = l.split(","), Mb = 0; Mb < vb.length; Mb++)if (V = vb[Mb], "native-wasm" === V) { if (V = h(f, v)) break } else if ("asmjs" === V) { if (V = g(f, v, wb)) break } else if ("interpret-asm2wasm" === V || "interpret-s-expr" === V || "interpret-binary" === V) {
                    var db = f, pa = v, Nb = wb; if ("function" !== typeof WasmJS) a.printErr("WasmJS not detected - polyfill not bundled?"),
                        V = !1; else {
                        var ia = WasmJS({}); ia.outside = a; ia.info = F; ia.lookupImport = c; assert(Nb === a.buffer); F.global = db; F.env = pa; assert(Nb === a.buffer); pa.memory = Nb; assert(pa.memory instanceof ArrayBuffer); ia.providedTotalMemory = a.buffer.byteLength; db = void 0; db = "interpret-binary" === V ? e() : a.read("interpret-asm2wasm" == V ? x : n); pa = void 0; if ("interpret-asm2wasm" == V) pa = ia._malloc(db.length + 1), ia.writeAsciiToMemory(db, pa), ia._load_asm2wasm(pa); else if ("interpret-s-expr" === V) pa = ia._malloc(db.length + 1), ia.writeAsciiToMemory(db,
                            pa), ia._load_s_expr2wasm(pa); else if ("interpret-binary" === V) pa = ia._malloc(db.length), ia.HEAPU8.set(db, pa), ia._load_binary2wasm(pa, db.length); else throw "what? " + V; ia._free(pa); ia._instantiate(pa); a.newBuffer && (d(a.newBuffer), a.newBuffer = null); V = Na = ia.asmExports
                    } if (V) break
                } else throw "bad method: " + V; if (!V) throw "no binaryen method succeeded. consider enabling more options, like interpreting, if you want that: https://github.com/kripken/emscripten/wiki/WebAssembly#binaryen-methods"; return V
            }; var wb = a.asm
    })(b);
    var mb = [function (a, c, d, e, f, g, h, l) { a = b.getCache(b.ConcreteContactResultCallback)[a]; if (!a.hasOwnProperty("addSingleResult")) throw "a JSImplementation must implement all functions, you forgot ConcreteContactResultCallback::addSingleResult."; return a.addSingleResult(c, d, e, f, g, h, l) }]; Qa = k.i; ma = Qa + 27712; Xa.push({ G: function () { nb() } }); lb = 0 <= b.wasmJSMethod.indexOf("asmjs") || 0 <= b.wasmJSMethod.indexOf("interpret-asm2wasm") ? "ammo.wasm.js.mem" : null; b.STATIC_BASE = Qa; b.STATIC_BUMP = 27712; var ob = ma; ma += 16;
    b._memset = pb; function qb() { return !!qb.d } var rb = 0, sb = [], tb = {}; function ub(a, c) { ub.d || (ub.d = {}); a in ub.d || (b.dynCall_v(c), ub.d[a] = 1) } b._memcpy = xb; var yb = 0; function zb() { yb += 4; return m[yb - 4 >> 2] } var Ab = {}; b._llvm_bswap_i16 = Bb; var Cb = {}; b._sbrk = Db; b._memmove = Eb; var Fb = 1;
    function Gb() { var a = rb; if (!a) return (k.e(0), 0) | 0; var c = tb[a], d = c.type; if (!d) return (k.e(0), a) | 0; var e = Array.prototype.slice.call(arguments); b.___cxa_is_pointer_type(d); Gb.buffer || (Gb.buffer = za(4)); m[Gb.buffer >> 2] = a; for (var a = Gb.buffer, f = 0; f < e.length; f++)if (e[f] && b.___cxa_can_catch(e[f], d, a)) return a = m[a >> 2], c.A = a, (k.e(e[f]), a) | 0; a = m[a >> 2]; return (k.e(d), a) | 0 } b._llvm_bswap_i32 = Hb;
    function Ib(a, c) {
        yb = c; try {
            var d = zb(), e = zb(), f = zb(), g = 0; Ib.buffer || (Ib.d = [null, [], []], Ib.q = function (a, c) {
                var d = Ib.d[a]; assert(d); if (0 === c || 10 === c) {
                    var e = 1 === a ? b.print : b.printErr, f; a: {
                        for (var g = f = 0; d[g];)++g; if (16 < g - f && d.subarray && Ga) f = Ga.decode(d.subarray(f, g)); else for (var h, l, n, v, x, Nb, g = ""; ;) {
                            h = d[f++]; if (!h) { f = g; break a } h & 128 ? (l = d[f++] & 63, 192 == (h & 224) ? g += String.fromCharCode((h & 31) << 6 | l) : (n = d[f++] & 63, 224 == (h & 240) ? h = (h & 15) << 12 | l << 6 | n : (v = d[f++] & 63, 240 == (h & 248) ? h = (h & 7) << 18 | l << 12 | n << 6 | v : (x = d[f++] & 63,
                                248 == (h & 252) ? h = (h & 3) << 24 | l << 18 | n << 12 | v << 6 | x : (Nb = d[f++] & 63, h = (h & 1) << 30 | l << 24 | n << 18 | v << 12 | x << 6 | Nb))), 65536 > h ? g += String.fromCharCode(h) : (h -= 65536, g += String.fromCharCode(55296 | h >> 10, 56320 | h & 1023)))) : g += String.fromCharCode(h)
                        }
                    } e(f); d.length = 0
                } else d.push(c)
            }); for (var h = 0; h < f; h++) { for (var l = m[e + 8 * h >> 2], n = m[e + (8 * h + 4) >> 2], v = 0; v < n; v++)Ib.q(d, Aa[l + v]); g += n } return g
        } catch (x) { return "undefined" !== typeof FS && x instanceof FS.o || sa(x), -x.r }
    }
    Za.push(function () { var a = b._fflush; a && a(0); if (a = Ib.q) { var c = Ib.d; c[1].length && a(1, 10); c[2].length && a(2, 10) } }); na = ya(1, "i32", 2); Ra = la = k.p(ma); Sa = Ra + Ua; Ta = k.p(Sa); m[na >> 2] = Ta; b.wasmTableSize = 1393; b.wasmMaxTableSize = 1393; b.B = { Math: Math, Int8Array: Int8Array, Int16Array: Int16Array, Int32Array: Int32Array, Uint8Array: Uint8Array, Uint16Array: Uint16Array, Uint32Array: Uint32Array, Float32Array: Float32Array, Float64Array: Float64Array, NaN: NaN, Infinity: Infinity };
    b.C = {
        abort: sa, assert: assert, enlargeMemory: function () { qa() }, getTotalMemory: function () { return oa }, abortOnCannotGrowMemory: qa, invoke_viiiii: function (a, c, d, e, f, g) { try { b.dynCall_viiiii(a, c, d, e, f, g) } catch (h) { if ("number" !== typeof h && "longjmp" !== h) throw h; b.setThrew(1, 0) } }, invoke_viiiifffffifi: function (a, c, d, e, f, g, h, l, n, v, x, F, Na) { try { b.dynCall_viiiifffffifi(a, c, d, e, f, g, h, l, n, v, x, F, Na) } catch (wb) { if ("number" !== typeof wb && "longjmp" !== wb) throw wb; b.setThrew(1, 0) } }, invoke_vif: function (a, c, d) {
            try {
                b.dynCall_vif(a,
                    c, d)
            } catch (e) { if ("number" !== typeof e && "longjmp" !== e) throw e; b.setThrew(1, 0) }
        }, invoke_viifii: function (a, c, d, e, f, g) { try { b.dynCall_viifii(a, c, d, e, f, g) } catch (h) { if ("number" !== typeof h && "longjmp" !== h) throw h; b.setThrew(1, 0) } }, invoke_vi: function (a, c) { try { b.dynCall_vi(a, c) } catch (d) { if ("number" !== typeof d && "longjmp" !== d) throw d; b.setThrew(1, 0) } }, invoke_vii: function (a, c, d) { try { b.dynCall_vii(a, c, d) } catch (e) { if ("number" !== typeof e && "longjmp" !== e) throw e; b.setThrew(1, 0) } }, invoke_iiiiiiiiiii: function (a, c, d, e,
            f, g, h, l, n, v, x) { try { return b.dynCall_iiiiiiiiiii(a, c, d, e, f, g, h, l, n, v, x) } catch (F) { if ("number" !== typeof F && "longjmp" !== F) throw F; b.setThrew(1, 0) } }, invoke_viiiif: function (a, c, d, e, f, g) { try { b.dynCall_viiiif(a, c, d, e, f, g) } catch (h) { if ("number" !== typeof h && "longjmp" !== h) throw h; b.setThrew(1, 0) } }, invoke_ii: function (a, c) { try { return b.dynCall_ii(a, c) } catch (d) { if ("number" !== typeof d && "longjmp" !== d) throw d; b.setThrew(1, 0) } }, invoke_viifi: function (a, c, d, e, f) {
                try { b.dynCall_viifi(a, c, d, e, f) } catch (g) {
                    if ("number" !== typeof g &&
                        "longjmp" !== g) throw g; b.setThrew(1, 0)
                }
            }, invoke_viiiiiiiii: function (a, c, d, e, f, g, h, l, n, v) { try { b.dynCall_viiiiiiiii(a, c, d, e, f, g, h, l, n, v) } catch (x) { if ("number" !== typeof x && "longjmp" !== x) throw x; b.setThrew(1, 0) } }, invoke_viiif: function (a, c, d, e, f) { try { b.dynCall_viiif(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, invoke_viffiii: function (a, c, d, e, f, g, h) { try { b.dynCall_viffiii(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_iiiii: function (a,
                c, d, e, f) { try { return b.dynCall_iiiii(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, invoke_viiifii: function (a, c, d, e, f, g, h) { try { b.dynCall_viiifii(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_fiifii: function (a, c, d, e, f, g) { try { return b.dynCall_fiifii(a, c, d, e, f, g) } catch (h) { if ("number" !== typeof h && "longjmp" !== h) throw h; b.setThrew(1, 0) } }, invoke_fiiiiiiiii: function (a, c, d, e, f, g, h, l, n, v) {
                    try {
                        return b.dynCall_fiiiiiiiii(a,
                            c, d, e, f, g, h, l, n, v)
                    } catch (x) { if ("number" !== typeof x && "longjmp" !== x) throw x; b.setThrew(1, 0) }
                }, invoke_iiii: function (a, c, d, e) { try { return b.dynCall_iiii(a, c, d, e) } catch (f) { if ("number" !== typeof f && "longjmp" !== f) throw f; b.setThrew(1, 0) } }, invoke_fif: function (a, c, d) { try { return b.dynCall_fif(a, c, d) } catch (e) { if ("number" !== typeof e && "longjmp" !== e) throw e; b.setThrew(1, 0) } }, invoke_viff: function (a, c, d, e) { try { b.dynCall_viff(a, c, d, e) } catch (f) { if ("number" !== typeof f && "longjmp" !== f) throw f; b.setThrew(1, 0) } }, invoke_vifi: function (a,
                    c, d, e) { try { b.dynCall_vifi(a, c, d, e) } catch (f) { if ("number" !== typeof f && "longjmp" !== f) throw f; b.setThrew(1, 0) } }, invoke_viiiiif: function (a, c, d, e, f, g, h) { try { b.dynCall_viiiiif(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_viiiiii: function (a, c, d, e, f, g, h) { try { b.dynCall_viiiiii(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_iiif: function (a, c, d, e) {
                        try { return b.dynCall_iiif(a, c, d, e) } catch (f) {
                            if ("number" !== typeof f &&
                                "longjmp" !== f) throw f; b.setThrew(1, 0)
                        }
                    }, invoke_fiii: function (a, c, d, e) { try { return b.dynCall_fiii(a, c, d, e) } catch (f) { if ("number" !== typeof f && "longjmp" !== f) throw f; b.setThrew(1, 0) } }, invoke_iiiiiii: function (a, c, d, e, f, g, h) { try { return b.dynCall_iiiiiii(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_fiiiiiiiiii: function (a, c, d, e, f, g, h, l, n, v, x) {
                        try { return b.dynCall_fiiiiiiiiii(a, c, d, e, f, g, h, l, n, v, x) } catch (F) {
                            if ("number" !== typeof F && "longjmp" !== F) throw F; b.setThrew(1,
                                0)
                        }
                    }, invoke_fiiiii: function (a, c, d, e, f, g) { try { return b.dynCall_fiiiii(a, c, d, e, f, g) } catch (h) { if ("number" !== typeof h && "longjmp" !== h) throw h; b.setThrew(1, 0) } }, invoke_viiiiiii: function (a, c, d, e, f, g, h, l) { try { b.dynCall_viiiiiii(a, c, d, e, f, g, h, l) } catch (n) { if ("number" !== typeof n && "longjmp" !== n) throw n; b.setThrew(1, 0) } }, invoke_vifii: function (a, c, d, e, f) { try { b.dynCall_vifii(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, invoke_fi: function (a, c) {
                        try { return b.dynCall_fi(a, c) } catch (d) {
                            if ("number" !==
                                typeof d && "longjmp" !== d) throw d; b.setThrew(1, 0)
                        }
                    }, invoke_viiiiiiiiii: function (a, c, d, e, f, g, h, l, n, v, x) { try { b.dynCall_viiiiiiiiii(a, c, d, e, f, g, h, l, n, v, x) } catch (F) { if ("number" !== typeof F && "longjmp" !== F) throw F; b.setThrew(1, 0) } }, invoke_iii: function (a, c, d) { try { return b.dynCall_iii(a, c, d) } catch (e) { if ("number" !== typeof e && "longjmp" !== e) throw e; b.setThrew(1, 0) } }, invoke_fiiiiiiii: function (a, c, d, e, f, g, h, l, n) {
                        try { return b.dynCall_fiiiiiiii(a, c, d, e, f, g, h, l, n) } catch (v) {
                            if ("number" !== typeof v && "longjmp" !== v) throw v;
                            b.setThrew(1, 0)
                        }
                    }, invoke_iifif: function (a, c, d, e, f) { try { return b.dynCall_iifif(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, invoke_viiiiffffiif: function (a, c, d, e, f, g, h, l, n, v, x, F) { try { b.dynCall_viiiiffffiif(a, c, d, e, f, g, h, l, n, v, x, F) } catch (Na) { if ("number" !== typeof Na && "longjmp" !== Na) throw Na; b.setThrew(1, 0) } }, invoke_fiiii: function (a, c, d, e, f) { try { return b.dynCall_fiiii(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, invoke_iiiiiiiiii: function (a,
                        c, d, e, f, g, h, l, n, v) { try { return b.dynCall_iiiiiiiiii(a, c, d, e, f, g, h, l, n, v) } catch (x) { if ("number" !== typeof x && "longjmp" !== x) throw x; b.setThrew(1, 0) } }, invoke_viii: function (a, c, d, e) { try { b.dynCall_viii(a, c, d, e) } catch (f) { if ("number" !== typeof f && "longjmp" !== f) throw f; b.setThrew(1, 0) } }, invoke_v: function (a) { try { b.dynCall_v(a) } catch (c) { if ("number" !== typeof c && "longjmp" !== c) throw c; b.setThrew(1, 0) } }, invoke_viif: function (a, c, d, e) {
                            try { b.dynCall_viif(a, c, d, e) } catch (f) {
                                if ("number" !== typeof f && "longjmp" !== f) throw f;
                                b.setThrew(1, 0)
                            }
                        }, invoke_fiiifii: function (a, c, d, e, f, g, h) { try { return b.dynCall_fiiifii(a, c, d, e, f, g, h) } catch (l) { if ("number" !== typeof l && "longjmp" !== l) throw l; b.setThrew(1, 0) } }, invoke_viiii: function (a, c, d, e, f) { try { b.dynCall_viiii(a, c, d, e, f) } catch (g) { if ("number" !== typeof g && "longjmp" !== g) throw g; b.setThrew(1, 0) } }, _pthread_getspecific: function (a) { return Cb[a] || 0 }, ___cxa_begin_catch: function (a) {
                            var c = tb[a]; c && !c.D && (c.D = !0, qb.d--); c && (c.da = !1); sb.push(a); a: {
                                if (a && !tb[a]) for (var d in tb) if (tb[d].A === a) {
                                    c =
                                        d; break a
                                } c = a
                            } c && tb[c].ba++; return a
                        }, _pthread_setspecific: function (a, c) { if (!(a in Cb)) return 22; Cb[a] = c; return 0 }, _pthread_key_create: function (a) { if (0 == a) return 22; m[a >> 2] = Fb; Cb[Fb] = 0; Fb++; return 0 }, _abort: function () { b.abort() }, ___setErrNo: function (a) { b.___errno_location && (m[b.___errno_location() >> 2] = a); return a }, ___syscall6: function (a, c) { yb = c; try { var d = Ab.I(); FS.close(d); return 0 } catch (e) { return "undefined" !== typeof FS && e instanceof FS.o || sa(e), -e.r } }, _llvm_trap: function () { sa("trap!") }, _llvm_pow_f32: fb,
        _gettimeofday: function (a) { var c = Date.now(); m[a >> 2] = c / 1E3 | 0; m[a + 4 >> 2] = c % 1E3 * 1E3 | 0; return 0 }, _pthread_once: ub, _emscripten_memcpy_big: function (a, c, d) { Aa.set(Aa.subarray(c, c + d), a); return a }, ___gxx_personality_v0: function () { }, ___syscall140: function (a, c) { yb = c; try { var d = Ab.I(); zb(); var e = zb(), f = zb(), g = zb(); FS.Z(d, e, g); m[f >> 2] = d.position; d.K && 0 === e && 0 === g && (d.K = null); return 0 } catch (h) { return "undefined" !== typeof FS && h instanceof FS.o || sa(h), -h.r } }, ___resumeException: function (a) {
            rb || (rb = a); throw a + " - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.";
        }, _emscripten_asm_const_diiiiiiii: function (a, c, d, e, f, g, h, l, n) { return mb[a](c, d, e, f, g, h, l, n) }, ___cxa_find_matching_catch: Gb, ___cxa_pure_virtual: function () { ra = !0; throw "Pure virtual function called!"; }, ___syscall146: Ib, __ZSt18uncaught_exceptionv: qb, DYNAMICTOP_PTR: na, tempDoublePtr: ob, ABORT: ra, STACKTOP: la, STACK_MAX: Sa
    }; var Jb = b.asm(b.B, b.C, buffer); b.asm = Jb;
    var Kb = b._emscripten_bind_btCylinderShape___destroy___0 = function () { return b.asm._emscripten_bind_btCylinderShape___destroy___0.apply(null, arguments) }, Lb = b._emscripten_bind_btGeneric6DofConstraint_enableFeedback_1 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_enableFeedback_1.apply(null, arguments) }, Ob = b._emscripten_bind_btGhostObject___destroy___0 = function () { return b.asm._emscripten_bind_btGhostObject___destroy___0.apply(null, arguments) }, Pb = b._emscripten_bind_Config_get_kSRHR_CL_0 =
        function () { return b.asm._emscripten_bind_Config_get_kSRHR_CL_0.apply(null, arguments) }, Qb = b._emscripten_bind_btPoint2PointConstraint_set_m_setting_1 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_set_m_setting_1.apply(null, arguments) }, Rb = b._emscripten_bind_btQuaternion_dot_1 = function () { return b.asm._emscripten_bind_btQuaternion_dot_1.apply(null, arguments) }, Sb = b._emscripten_bind_btDispatcherInfo_set_m_useContinuous_1 = function () {
            return b.asm._emscripten_bind_btDispatcherInfo_set_m_useContinuous_1.apply(null,
                arguments)
        }, Tb = b._emscripten_bind_btCollisionObject_isActive_0 = function () { return b.asm._emscripten_bind_btCollisionObject_isActive_0.apply(null, arguments) }, Ub = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelsDampingRelaxation_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelsDampingRelaxation_1.apply(null, arguments) }, Vb = b._emscripten_bind_btVehicleTuning_set_m_frictionSlip_1 = function () {
            return b.asm._emscripten_bind_btVehicleTuning_set_m_frictionSlip_1.apply(null,
                arguments)
        }, Wb = b._emscripten_bind_btDiscreteDynamicsWorld_btDiscreteDynamicsWorld_4 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_btDiscreteDynamicsWorld_4.apply(null, arguments) }, Xb = b._emscripten_bind_btCapsuleShapeX_getMargin_0 = function () { return b.asm._emscripten_bind_btCapsuleShapeX_getMargin_0.apply(null, arguments) }, Yb = b._emscripten_bind_Node_set_m_n_1 = function () { return b.asm._emscripten_bind_Node_set_m_n_1.apply(null, arguments) }, Zb = b._emscripten_bind_btCompoundShape_getMargin_0 =
            function () { return b.asm._emscripten_bind_btCompoundShape_getMargin_0.apply(null, arguments) }, $b = b._emscripten_bind_RaycastInfo_set_m_wheelDirectionWS_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_wheelDirectionWS_1.apply(null, arguments) }, ac = b._emscripten_bind_btVehicleRaycasterResult_get_m_hitNormalInWorld_0 = function () { return b.asm._emscripten_bind_btVehicleRaycasterResult_get_m_hitNormalInWorld_0.apply(null, arguments) }, bc = b._emscripten_bind_btRigidBody_setUserPointer_1 = function () {
                return b.asm._emscripten_bind_btRigidBody_setUserPointer_1.apply(null,
                    arguments)
            }, cc = b._emscripten_bind_ClosestRayResultCallback_get_m_hitPointWorld_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_hitPointWorld_0.apply(null, arguments) }, dc = b._emscripten_bind_btTypedConstraint_setBreakingImpulseThreshold_1 = function () { return b.asm._emscripten_bind_btTypedConstraint_setBreakingImpulseThreshold_1.apply(null, arguments) }, ec = b._emscripten_bind_btQuaternion_setX_1 = function () { return b.asm._emscripten_bind_btQuaternion_setX_1.apply(null, arguments) }, fc =
            b._emscripten_bind_btCylinderShapeZ_getMargin_0 = function () { return b.asm._emscripten_bind_btCylinderShapeZ_getMargin_0.apply(null, arguments) }, gc = b._emscripten_bind_btDispatcherInfo_get_m_timeOfImpact_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_timeOfImpact_0.apply(null, arguments) }, hc = b._emscripten_bind_btQuaternion_setZ_1 = function () { return b.asm._emscripten_bind_btQuaternion_setZ_1.apply(null, arguments) }, ic = b._emscripten_bind_btCollisionObject_getUserIndex_0 = function () {
                return b.asm._emscripten_bind_btCollisionObject_getUserIndex_0.apply(null,
                    arguments)
            }, jc = b._emscripten_bind_btDispatcherInfo_get_m_allowedCcdPenetration_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_allowedCcdPenetration_0.apply(null, arguments) }, kc = b._emscripten_bind_LocalConvexResult_get_m_hitNormalLocal_0 = function () { return b.asm._emscripten_bind_LocalConvexResult_get_m_hitNormalLocal_0.apply(null, arguments) }, lc = b._emscripten_bind_btSoftBodyWorldInfo_set_water_density_1 = function () {
                return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_water_density_1.apply(null,
                    arguments)
            }, mc = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_restitution_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_restitution_0.apply(null, arguments) }, nc = b._emscripten_bind_btKinematicCharacterController_setMaxSlope_1 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_setMaxSlope_1.apply(null, arguments) }, oc = b._emscripten_bind_btQuadWord_z_0 = function () { return b.asm._emscripten_bind_btQuadWord_z_0.apply(null, arguments) }, pc = b._emscripten_bind_btSoftBody_setCcdMotionThreshold_1 =
                function () { return b.asm._emscripten_bind_btSoftBody_setCcdMotionThreshold_1.apply(null, arguments) }, qc = b._emscripten_bind_Material___destroy___0 = function () { return b.asm._emscripten_bind_Material___destroy___0.apply(null, arguments) }, rc = b._emscripten_bind_btHingeConstraint_btHingeConstraint_2 = function () { return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_2.apply(null, arguments) }, sc = b._emscripten_bind_btSoftBody_rotate_1 = function () {
                    return b.asm._emscripten_bind_btSoftBody_rotate_1.apply(null,
                        arguments)
                }, tc = b._emscripten_bind_btWheelInfo_get_m_suspensionRestLength1_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_suspensionRestLength1_0.apply(null, arguments) }, uc = b._emscripten_bind_btWheelInfo_get_m_suspensionStiffness_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_suspensionStiffness_0.apply(null, arguments) }, vc = b._emscripten_bind_btVector4_setY_1 = function () { return b.asm._emscripten_bind_btVector4_setY_1.apply(null, arguments) }, wc = b._emscripten_enum_PHY_ScalarType_PHY_UCHAR =
                    function () { return b.asm._emscripten_enum_PHY_ScalarType_PHY_UCHAR.apply(null, arguments) }, xc = b._emscripten_bind_btQuaternion_setW_1 = function () { return b.asm._emscripten_bind_btQuaternion_setW_1.apply(null, arguments) }, yc = b._emscripten_bind_btSoftRigidDynamicsWorld___destroy___0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld___destroy___0.apply(null, arguments) }, zc = b._emscripten_bind_btSoftRigidDynamicsWorld_removeConstraint_1 = function () {
                        return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_removeConstraint_1.apply(null,
                            arguments)
                    }, Ac = b._emscripten_bind_RaycastInfo_get_m_wheelAxleWS_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_wheelAxleWS_0.apply(null, arguments) }, Bc = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_angularDamping_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_angularDamping_0.apply(null, arguments) }, Cc = b._emscripten_bind_btCollisionDispatcher___destroy___0 = function () { return b.asm._emscripten_bind_btCollisionDispatcher___destroy___0.apply(null, arguments) },
        Dc = b._emscripten_bind_btRigidBody_applyCentralImpulse_1 = function () { return b.asm._emscripten_bind_btRigidBody_applyCentralImpulse_1.apply(null, arguments) }, Ec = b._emscripten_bind_btConvexHullShape_getMargin_0 = function () { return b.asm._emscripten_bind_btConvexHullShape_getMargin_0.apply(null, arguments) }, Fc = b._emscripten_bind_btDefaultMotionState_getWorldTransform_1 = function () { return b.asm._emscripten_bind_btDefaultMotionState_getWorldTransform_1.apply(null, arguments) }, Gc = b._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_1 =
            function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_1.apply(null, arguments) }, Hc = b._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_3 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_3.apply(null, arguments) }, Ic = b._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_2 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_stepSimulation_2.apply(null, arguments) }, Jc = b._emscripten_bind_btSoftRigidDynamicsWorld_addAction_1 = function () {
                return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addAction_1.apply(null,
                    arguments)
            }, Kc = b._emscripten_bind_btDynamicsWorld_rayTest_3 = function () { return b.asm._emscripten_bind_btDynamicsWorld_rayTest_3.apply(null, arguments) }, Lc = b._emscripten_bind_Config_set_kSR_SPLT_CL_1 = function () { return b.asm._emscripten_bind_Config_set_kSR_SPLT_CL_1.apply(null, arguments) }, Mc = b._emscripten_bind_btQuadWord_x_0 = function () { return b.asm._emscripten_bind_btQuadWord_x_0.apply(null, arguments) }, Nc = b._emscripten_bind_Config_get_diterations_0 = function () {
                return b.asm._emscripten_bind_Config_get_diterations_0.apply(null,
                    arguments)
            }, Oc = b._emscripten_bind_btCollisionObject_isKinematicObject_0 = function () { return b.asm._emscripten_bind_btCollisionObject_isKinematicObject_0.apply(null, arguments) }, Pc = b._emscripten_bind_btSoftRigidDynamicsWorld_removeSoftBody_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_removeSoftBody_1.apply(null, arguments) }, Qc = b._emscripten_bind_btSphereShape___destroy___0 = function () { return b.asm._emscripten_bind_btSphereShape___destroy___0.apply(null, arguments) }, Rc = b._emscripten_bind_btGeneric6DofSpringConstraint_setLinearUpperLimit_1 =
                function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setLinearUpperLimit_1.apply(null, arguments) }, Sc = b._emscripten_bind_btQuaternion_getAngleShortestPath_0 = function () { return b.asm._emscripten_bind_btQuaternion_getAngleShortestPath_0.apply(null, arguments) }, Tc = b._emscripten_bind_ClosestConvexResultCallback_set_m_hitNormalWorld_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_hitNormalWorld_1.apply(null, arguments) }, Uc = b._emscripten_bind_btSoftBody_isKinematicObject_0 =
                    function () { return b.asm._emscripten_bind_btSoftBody_isKinematicObject_0.apply(null, arguments) }, Vc = b._emscripten_bind_btRigidBody_getCenterOfMassTransform_0 = function () { return b.asm._emscripten_bind_btRigidBody_getCenterOfMassTransform_0.apply(null, arguments) }, Wc = b._emscripten_bind_btTransform_setIdentity_0 = function () { return b.asm._emscripten_bind_btTransform_setIdentity_0.apply(null, arguments) }, Xc = b._emscripten_bind_btGhostObject_isKinematicObject_0 = function () {
                        return b.asm._emscripten_bind_btGhostObject_isKinematicObject_0.apply(null,
                            arguments)
                    }, Yc = b._emscripten_bind_btGeneric6DofSpringConstraint_btGeneric6DofSpringConstraint_5 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_btGeneric6DofSpringConstraint_5.apply(null, arguments) }, Zc = b._emscripten_bind_btWheelInfoConstructionInfo___destroy___0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo___destroy___0.apply(null, arguments) }, $c = b._emscripten_bind_btCapsuleShape___destroy___0 = function () {
                        return b.asm._emscripten_bind_btCapsuleShape___destroy___0.apply(null,
                            arguments)
                    }, ad = b._emscripten_bind_btDefaultCollisionConfiguration_btDefaultCollisionConfiguration_1 = function () { return b.asm._emscripten_bind_btDefaultCollisionConfiguration_btDefaultCollisionConfiguration_1.apply(null, arguments) }, bd = b._emscripten_bind_btCollisionObject_activate_1 = function () { return b.asm._emscripten_bind_btCollisionObject_activate_1.apply(null, arguments) }, cd = b._emscripten_bind_btCollisionObject_activate_0 = function () {
                        return b.asm._emscripten_bind_btCollisionObject_activate_0.apply(null,
                            arguments)
                    }, dd = b._emscripten_bind_btKinematicCharacterController_setUpAxis_1 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_setUpAxis_1.apply(null, arguments) }, ed = b._emscripten_bind_btSoftRigidDynamicsWorld_addConstraint_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addConstraint_1.apply(null, arguments) }, fd = b._emscripten_bind_Config_set_kSSHR_CL_1 = function () { return b.asm._emscripten_bind_Config_set_kSSHR_CL_1.apply(null, arguments) }, gd = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_maxSuspensionForce_0 =
                        function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_maxSuspensionForce_0.apply(null, arguments) }, hd = b._emscripten_bind_btDispatcherInfo_set_m_timeOfImpact_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_timeOfImpact_1.apply(null, arguments) }, id = b._emscripten_bind_btCollisionDispatcher_btCollisionDispatcher_1 = function () { return b.asm._emscripten_bind_btCollisionDispatcher_btCollisionDispatcher_1.apply(null, arguments) }, jd = b._emscripten_bind_btVector3_setX_1 = function () {
                            return b.asm._emscripten_bind_btVector3_setX_1.apply(null,
                                arguments)
                        }, kd = b._emscripten_bind_btCollisionConfiguration___destroy___0 = function () { return b.asm._emscripten_bind_btCollisionConfiguration___destroy___0.apply(null, arguments) }, ld = b._emscripten_bind_btCapsuleShapeZ_setMargin_1 = function () { return b.asm._emscripten_bind_btCapsuleShapeZ_setMargin_1.apply(null, arguments) }, md = b._emscripten_bind_btHingeConstraint_enableFeedback_1 = function () { return b.asm._emscripten_bind_btHingeConstraint_enableFeedback_1.apply(null, arguments) }, nd = b._emscripten_bind_btActionInterface_updateAction_2 =
                            function () { return b.asm._emscripten_bind_btActionInterface_updateAction_2.apply(null, arguments) }; b.stackAlloc = function () { return b.asm.stackAlloc.apply(null, arguments) };
    var od = b._emscripten_bind_btHeightfieldTerrainShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape_setLocalScaling_1.apply(null, arguments) }, pd = b._emscripten_bind_btManifoldPoint_set_m_positionWorldOnB_1 = function () { return b.asm._emscripten_bind_btManifoldPoint_set_m_positionWorldOnB_1.apply(null, arguments) }, qd = b._emscripten_bind_btRaycastVehicle_updateSuspension_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_updateSuspension_1.apply(null, arguments) },
        rd = b._emscripten_bind_btManifoldPoint_set_m_localPointB_1 = function () { return b.asm._emscripten_bind_btManifoldPoint_set_m_localPointB_1.apply(null, arguments) }, sd = b._emscripten_bind_btVector3_setZ_1 = function () { return b.asm._emscripten_bind_btVector3_setZ_1.apply(null, arguments) }, td = b._emscripten_bind_btKinematicCharacterController_setUseGhostSweepTest_1 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_setUseGhostSweepTest_1.apply(null, arguments) }, ud = b._emscripten_bind_btQuaternion_setValue_4 =
            function () { return b.asm._emscripten_bind_btQuaternion_setValue_4.apply(null, arguments) }, vd = b._emscripten_bind_btDispatcherInfo_set_m_dispatchFunc_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_dispatchFunc_1.apply(null, arguments) }, wd = b._emscripten_bind_btQuaternion_setRotation_2 = function () { return b.asm._emscripten_bind_btQuaternion_setRotation_2.apply(null, arguments) }, xd = b._emscripten_bind_btMotionState_setWorldTransform_1 = function () {
                return b.asm._emscripten_bind_btMotionState_setWorldTransform_1.apply(null,
                    arguments)
            }, yd = b._emscripten_bind_LocalShapeInfo___destroy___0 = function () { return b.asm._emscripten_bind_LocalShapeInfo___destroy___0.apply(null, arguments) }, zd = b._emscripten_bind_btSoftBody_appendAnchor_4 = function () { return b.asm._emscripten_bind_btSoftBody_appendAnchor_4.apply(null, arguments) }, Ad = b._emscripten_bind_btPoint2PointConstraint_get_m_setting_0 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_get_m_setting_0.apply(null, arguments) }, Bd = b._emscripten_bind_btQuadWord_setY_1 = function () {
                return b.asm._emscripten_bind_btQuadWord_setY_1.apply(null,
                    arguments)
            }, Cd = b._emscripten_bind_btRigidBody_isKinematicObject_0 = function () { return b.asm._emscripten_bind_btRigidBody_isKinematicObject_0.apply(null, arguments) }, Dd = b._emscripten_bind_ContactResultCallback_addSingleResult_7 = function () { return b.asm._emscripten_bind_ContactResultCallback_addSingleResult_7.apply(null, arguments) }, Ed = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_restitution_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_restitution_1.apply(null, arguments) },
        Fd = b._emscripten_bind_btVector4_rotate_2 = function () { return b.asm._emscripten_bind_btVector4_rotate_2.apply(null, arguments) }, Gd = b._emscripten_bind_btDefaultMotionState_get_m_graphicsWorldTrans_0 = function () { return b.asm._emscripten_bind_btDefaultMotionState_get_m_graphicsWorldTrans_0.apply(null, arguments) }, Hd = b._emscripten_bind_btSliderConstraint_btSliderConstraint_5 = function () { return b.asm._emscripten_bind_btSliderConstraint_btSliderConstraint_5.apply(null, arguments) }, Id = b._emscripten_bind_btConeTwistConstraint_setDamping_1 =
            function () { return b.asm._emscripten_bind_btConeTwistConstraint_setDamping_1.apply(null, arguments) }, Jd = b._emscripten_bind_btPairCachingGhostObject_btPairCachingGhostObject_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_btPairCachingGhostObject_0.apply(null, arguments) }, Kd = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_suspensionRestLength_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_suspensionRestLength_0.apply(null, arguments) }, Ld = b._emscripten_bind_btDiscreteDynamicsWorld_getSolverInfo_0 =
                function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getSolverInfo_0.apply(null, arguments) }, Md = b._emscripten_bind_btCylinderShape_setMargin_1 = function () { return b.asm._emscripten_bind_btCylinderShape_setMargin_1.apply(null, arguments) }, Nd = b._emscripten_bind_btRaycastVehicle_rayCast_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_rayCast_1.apply(null, arguments) }, Od = b._emscripten_bind_btCollisionWorld___destroy___0 = function () {
                    return b.asm._emscripten_bind_btCollisionWorld___destroy___0.apply(null,
                        arguments)
                }, Pd = b._emscripten_bind_btSoftBodyWorldInfo_get_m_broadphase_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_m_broadphase_0.apply(null, arguments) }, Qd = b._emscripten_bind_LocalConvexResult_get_m_hitPointLocal_0 = function () { return b.asm._emscripten_bind_LocalConvexResult_get_m_hitPointLocal_0.apply(null, arguments) }, Rd = b._emscripten_bind_btBoxShape_btBoxShape_1 = function () { return b.asm._emscripten_bind_btBoxShape_btBoxShape_1.apply(null, arguments) }, Sd = b._emscripten_bind_btPersistentManifold_getBody1_0 =
                    function () { return b.asm._emscripten_bind_btPersistentManifold_getBody1_0.apply(null, arguments) }, Td = b._emscripten_bind_ClosestRayResultCallback_set_m_collisionObject_1 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_collisionObject_1.apply(null, arguments) }, Ud = b._emscripten_bind_RaycastInfo_set_m_isInContact_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_isInContact_1.apply(null, arguments) }, Vd = b._emscripten_bind_btKinematicCharacterController_setGravity_1 = function () {
                        return b.asm._emscripten_bind_btKinematicCharacterController_setGravity_1.apply(null,
                            arguments)
                    }, Wd = b._emscripten_bind_btGeneric6DofConstraint_btGeneric6DofConstraint_5 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_btGeneric6DofConstraint_5.apply(null, arguments) }, Xd = b._emscripten_bind_btGeneric6DofConstraint_btGeneric6DofConstraint_3 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_btGeneric6DofConstraint_3.apply(null, arguments) }, Yd = b._emscripten_bind_LocalShapeInfo_get_m_shapePart_0 = function () {
                        return b.asm._emscripten_bind_LocalShapeInfo_get_m_shapePart_0.apply(null,
                            arguments)
                    }, Zd = b._emscripten_bind_btSoftRigidDynamicsWorld_removeAction_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_removeAction_1.apply(null, arguments) }, $d = b._emscripten_bind_btWheelInfo_get_m_rollInfluence_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_rollInfluence_0.apply(null, arguments) }, ae = b._emscripten_bind_btRigidBody_activate_0 = function () { return b.asm._emscripten_bind_btRigidBody_activate_0.apply(null, arguments) }, be = b._emscripten_bind_btVector4_setValue_4 =
                        function () { return b.asm._emscripten_bind_btVector4_setValue_4.apply(null, arguments) }, ce = b._emscripten_bind_btBvhTriangleMeshShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btBvhTriangleMeshShape_setLocalScaling_1.apply(null, arguments) }, de = b._emscripten_bind_tNodeArray_size_0 = function () { return b.asm._emscripten_bind_tNodeArray_size_0.apply(null, arguments) }, ee = b._emscripten_bind_btPoint2PointConstraint_setBreakingImpulseThreshold_1 = function () {
                            return b.asm._emscripten_bind_btPoint2PointConstraint_setBreakingImpulseThreshold_1.apply(null,
                                arguments)
                        }, fe = b._emscripten_bind_btDynamicsWorld_getDispatchInfo_0 = function () { return b.asm._emscripten_bind_btDynamicsWorld_getDispatchInfo_0.apply(null, arguments) }, ge = b._emscripten_bind_btCompoundShape_removeChildShapeByIndex_1 = function () { return b.asm._emscripten_bind_btCompoundShape_removeChildShapeByIndex_1.apply(null, arguments) }, he = b._emscripten_bind_btSoftBody_appendFace_4 = function () { return b.asm._emscripten_bind_btSoftBody_appendFace_4.apply(null, arguments) }, ie = b._emscripten_bind_btConvexTriangleMeshShape_btConvexTriangleMeshShape_2 =
                            function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape_btConvexTriangleMeshShape_2.apply(null, arguments) }, je = b._emscripten_bind_btConvexTriangleMeshShape_btConvexTriangleMeshShape_1 = function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape_btConvexTriangleMeshShape_1.apply(null, arguments) }, ke = b._emscripten_bind_ClosestConvexResultCallback_set_m_hitPointWorld_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_hitPointWorld_1.apply(null, arguments) }, le =
            b._emscripten_bind_RayResultCallback_set_m_collisionFilterMask_1 = function () { return b.asm._emscripten_bind_RayResultCallback_set_m_collisionFilterMask_1.apply(null, arguments) }, me = b._emscripten_bind_btBoxShape_getMargin_0 = function () { return b.asm._emscripten_bind_btBoxShape_getMargin_0.apply(null, arguments) }, ne = b._emscripten_bind_btPairCachingGhostObject___destroy___0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject___destroy___0.apply(null, arguments) }, oe = b._emscripten_bind_btPairCachingGhostObject_setUserPointer_1 =
                function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setUserPointer_1.apply(null, arguments) }, pe = b._emscripten_bind_btDynamicsWorld_addCollisionObject_3 = function () { return b.asm._emscripten_bind_btDynamicsWorld_addCollisionObject_3.apply(null, arguments) }, qe = b._emscripten_bind_btPairCachingGhostObject_activate_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_activate_0.apply(null, arguments) }, re = b._emscripten_bind_btPairCachingGhostObject_activate_1 = function () {
                    return b.asm._emscripten_bind_btPairCachingGhostObject_activate_1.apply(null,
                        arguments)
                }, se = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_suspensionStiffness_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_suspensionStiffness_1.apply(null, arguments) }, te = b._emscripten_bind_btContactSolverInfo_get_m_splitImpulsePenetrationThreshold_0 = function () { return b.asm._emscripten_bind_btContactSolverInfo_get_m_splitImpulsePenetrationThreshold_0.apply(null, arguments) }, ue = b._emscripten_bind_btSoftBody_setUserPointer_1 = function () {
                    return b.asm._emscripten_bind_btSoftBody_setUserPointer_1.apply(null,
                        arguments)
                }, ve = b._emscripten_bind_btSoftBody_setMass_2 = function () { return b.asm._emscripten_bind_btSoftBody_setMass_2.apply(null, arguments) }, we = b._emscripten_bind_Config_get_kCHR_0 = function () { return b.asm._emscripten_bind_Config_get_kCHR_0.apply(null, arguments) }, xe = b._emscripten_bind_btPairCachingGhostObject_forceActivationState_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_forceActivationState_1.apply(null, arguments) }, ye = b._emscripten_bind_btDefaultMotionState___destroy___0 =
                    function () { return b.asm._emscripten_bind_btDefaultMotionState___destroy___0.apply(null, arguments) }, ze = b._emscripten_bind_btDispatcherInfo_get_m_stepCount_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_stepCount_0.apply(null, arguments) }, Ae = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_angularDamping_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_angularDamping_1.apply(null, arguments) }, Be = b._emscripten_bind_btQuadWord_setW_1 = function () {
                        return b.asm._emscripten_bind_btQuadWord_setW_1.apply(null,
                            arguments)
                    }, Ce = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_friction_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_friction_0.apply(null, arguments) }, De = b._emscripten_bind_btCapsuleShapeX_btCapsuleShapeX_2 = function () { return b.asm._emscripten_bind_btCapsuleShapeX_btCapsuleShapeX_2.apply(null, arguments) }, Ee = b._emscripten_bind_LocalShapeInfo_set_m_shapePart_1 = function () { return b.asm._emscripten_bind_LocalShapeInfo_set_m_shapePart_1.apply(null, arguments) }, Fe = b._emscripten_bind_btRigidBody_setLinearFactor_1 =
                        function () { return b.asm._emscripten_bind_btRigidBody_setLinearFactor_1.apply(null, arguments) }, Ge = b._emscripten_bind_btCompoundShape_getChildShape_1 = function () { return b.asm._emscripten_bind_btCompoundShape_getChildShape_1.apply(null, arguments) }, He = b._emscripten_bind_btDispatcherInfo_set_m_useConvexConservativeDistanceUtil_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_useConvexConservativeDistanceUtil_1.apply(null, arguments) }, Ie = b._emscripten_bind_btSoftRigidDynamicsWorld_setGravity_1 =
                            function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_setGravity_1.apply(null, arguments) }, Je = b._emscripten_bind_btRaycastVehicle_getUpAxis_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getUpAxis_0.apply(null, arguments) }, Ke = b._emscripten_bind_btRaycastVehicle_getCurrentSpeedKmHour_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getCurrentSpeedKmHour_0.apply(null, arguments) }, Le = b._emscripten_bind_btWheelInfo_get_m_engineForce_0 = function () {
                                return b.asm._emscripten_bind_btWheelInfo_get_m_engineForce_0.apply(null,
                                    arguments)
                            }, Me = b._emscripten_bind_Config_get_kSR_SPLT_CL_0 = function () { return b.asm._emscripten_bind_Config_get_kSR_SPLT_CL_0.apply(null, arguments) }, Ne = b._emscripten_bind_btRaycastVehicle_setSteeringValue_2 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setSteeringValue_2.apply(null, arguments) }, Oe = b._emscripten_bind_btPoint2PointConstraint___destroy___0 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint___destroy___0.apply(null, arguments) }, Pe = b._emscripten_bind_btSoftBody_getUserPointer_0 =
                                function () { return b.asm._emscripten_bind_btSoftBody_getUserPointer_0.apply(null, arguments) }, Qe = b._emscripten_bind_btCollisionShape_setMargin_1 = function () { return b.asm._emscripten_bind_btCollisionShape_setMargin_1.apply(null, arguments) }, Re = b._emscripten_bind_btGeneric6DofConstraint_setAngularUpperLimit_1 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_setAngularUpperLimit_1.apply(null, arguments) }, Se = b._emscripten_bind_btDiscreteDynamicsWorld_addConstraint_2 = function () {
                                    return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addConstraint_2.apply(null,
                                        arguments)
                                }, Te = b._emscripten_bind_btDiscreteDynamicsWorld_addConstraint_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addConstraint_1.apply(null, arguments) }, Ue = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_angularSleepingThreshold_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_angularSleepingThreshold_1.apply(null, arguments) }, Ve = b._emscripten_bind_Config_get_kVCF_0 = function () { return b.asm._emscripten_bind_Config_get_kVCF_0.apply(null, arguments) },
        We = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_suspensionStiffness_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_suspensionStiffness_0.apply(null, arguments) }, Xe = b._emscripten_bind_btRaycastVehicle_getRightAxis_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getRightAxis_0.apply(null, arguments) }, Ye = b._emscripten_bind_btContactSolverInfo_set_m_numIterations_1 = function () {
            return b.asm._emscripten_bind_btContactSolverInfo_set_m_numIterations_1.apply(null,
                arguments)
        }, za = b._malloc = function () { return b.asm._malloc.apply(null, arguments) }, Ze = b._emscripten_bind_btDispatcherInfo_get_m_useEpa_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_useEpa_0.apply(null, arguments) }, $e = b._emscripten_bind_btTransform_btTransform_2 = function () { return b.asm._emscripten_bind_btTransform_btTransform_2.apply(null, arguments) }, af = b._emscripten_bind_btTransform_btTransform_0 = function () { return b.asm._emscripten_bind_btTransform_btTransform_0.apply(null, arguments) },
        bf = b._emscripten_bind_btPairCachingGhostObject_getUserIndex_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getUserIndex_0.apply(null, arguments) }, cf = b._emscripten_bind_Config_set_kVC_1 = function () { return b.asm._emscripten_bind_Config_set_kVC_1.apply(null, arguments) }, df = b._emscripten_bind_btSoftRigidDynamicsWorld_addSoftBody_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addSoftBody_3.apply(null, arguments) }, ef = b._emscripten_bind_btVector3_op_sub_1 = function () {
            return b.asm._emscripten_bind_btVector3_op_sub_1.apply(null,
                arguments)
        }, ff = b._emscripten_bind_btWheelInfo_set_m_wheelsRadius_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_wheelsRadius_1.apply(null, arguments) }, gf = b._emscripten_bind_btQuaternion_length_0 = function () { return b.asm._emscripten_bind_btQuaternion_length_0.apply(null, arguments) }, hf = b._emscripten_bind_btDispatcherInfo_set_m_enableSPU_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_enableSPU_1.apply(null, arguments) }, jf = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelsDampingCompression_0 =
            function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelsDampingCompression_0.apply(null, arguments) }, kf = b._emscripten_bind_btRaycastVehicle_setCoordinateSystem_3 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setCoordinateSystem_3.apply(null, arguments) }, lf = b._emscripten_bind_btSoftBody_appendNode_2 = function () { return b.asm._emscripten_bind_btSoftBody_appendNode_2.apply(null, arguments) }, mf = b._emscripten_bind_btCollisionObject_setActivationState_1 = function () {
                return b.asm._emscripten_bind_btCollisionObject_setActivationState_1.apply(null,
                    arguments)
            }, nf = b._emscripten_bind_btQuaternion_angle_1 = function () { return b.asm._emscripten_bind_btQuaternion_angle_1.apply(null, arguments) }, of = b._emscripten_bind_btPersistentManifold___destroy___0 = function () { return b.asm._emscripten_bind_btPersistentManifold___destroy___0.apply(null, arguments) }, pf = b._emscripten_bind_btConstraintSetting_get_m_impulseClamp_0 = function () { return b.asm._emscripten_bind_btConstraintSetting_get_m_impulseClamp_0.apply(null, arguments) }, qf = b._emscripten_bind_btCylinderShapeZ___destroy___0 =
                function () { return b.asm._emscripten_bind_btCylinderShapeZ___destroy___0.apply(null, arguments) }, rf = b._emscripten_bind_btMatrix3x3___destroy___0 = function () { return b.asm._emscripten_bind_btMatrix3x3___destroy___0.apply(null, arguments) }; b.setTempRet0 = function () { return b.asm.setTempRet0.apply(null, arguments) };
    var sf = b._emscripten_bind_btQuaternion_angleShortestPath_1 = function () { return b.asm._emscripten_bind_btQuaternion_angleShortestPath_1.apply(null, arguments) }, tf = b._emscripten_bind_Config_set_kKHR_1 = function () { return b.asm._emscripten_bind_Config_set_kKHR_1.apply(null, arguments) }, uf = b._emscripten_bind_ConvexResultCallback_hasHit_0 = function () { return b.asm._emscripten_bind_ConvexResultCallback_hasHit_0.apply(null, arguments) }, vf = b._emscripten_bind_btCollisionShape_calculateLocalInertia_2 = function () {
        return b.asm._emscripten_bind_btCollisionShape_calculateLocalInertia_2.apply(null,
            arguments)
    }, wf = b._emscripten_bind_btGeneric6DofSpringConstraint_setBreakingImpulseThreshold_1 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setBreakingImpulseThreshold_1.apply(null, arguments) }, xf = b._emscripten_bind_Config_set_kPR_1 = function () { return b.asm._emscripten_bind_Config_set_kPR_1.apply(null, arguments) }, yf = b._emscripten_bind_btCollisionWorld_convexSweepTest_5 = function () { return b.asm._emscripten_bind_btCollisionWorld_convexSweepTest_5.apply(null, arguments) }, zf = b._emscripten_bind_btSoftBody_set_m_materials_1 =
        function () { return b.asm._emscripten_bind_btSoftBody_set_m_materials_1.apply(null, arguments) }, Af = b._emscripten_bind_ClosestRayResultCallback_set_m_hitPointWorld_1 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_hitPointWorld_1.apply(null, arguments) }, Bf = b._emscripten_bind_btVehicleRaycasterResult___destroy___0 = function () { return b.asm._emscripten_bind_btVehicleRaycasterResult___destroy___0.apply(null, arguments) }, Cf = b._emscripten_bind_btCapsuleShapeX_calculateLocalInertia_2 = function () {
            return b.asm._emscripten_bind_btCapsuleShapeX_calculateLocalInertia_2.apply(null,
                arguments)
        }, Df = b._emscripten_bind_btConstraintSetting_set_m_damping_1 = function () { return b.asm._emscripten_bind_btConstraintSetting_set_m_damping_1.apply(null, arguments) }, Ef = b._emscripten_bind_btWheelInfo_set_m_bIsFrontWheel_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_bIsFrontWheel_1.apply(null, arguments) }, Ff = b._emscripten_bind_btRigidBody_setCcdMotionThreshold_1 = function () { return b.asm._emscripten_bind_btRigidBody_setCcdMotionThreshold_1.apply(null, arguments) }, Gf = b._emscripten_bind_btConvexHullShape_setMargin_1 =
            function () { return b.asm._emscripten_bind_btConvexHullShape_setMargin_1.apply(null, arguments) }, Hf = b._emscripten_bind_btRigidBody_applyForce_2 = function () { return b.asm._emscripten_bind_btRigidBody_applyForce_2.apply(null, arguments) }, If = b._emscripten_bind_btConeShapeZ_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConeShapeZ_calculateLocalInertia_2.apply(null, arguments) }, Jf = b._emscripten_bind_btConstraintSetting_set_m_tau_1 = function () {
                return b.asm._emscripten_bind_btConstraintSetting_set_m_tau_1.apply(null,
                    arguments)
            }, Kf = b._emscripten_bind_btConvexHullShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConvexHullShape_calculateLocalInertia_2.apply(null, arguments) }, Lf = b._emscripten_bind_btQuaternion_op_div_1 = function () { return b.asm._emscripten_bind_btQuaternion_op_div_1.apply(null, arguments) }, Mf = b._emscripten_bind_RaycastInfo_get_m_contactPointWS_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_contactPointWS_0.apply(null, arguments) }, Nf = b._emscripten_bind_btSoftBody_setCollisionFlags_1 =
                function () { return b.asm._emscripten_bind_btSoftBody_setCollisionFlags_1.apply(null, arguments) }, Of = b._emscripten_bind_btSphereShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btSphereShape_calculateLocalInertia_2.apply(null, arguments) }, Pf = b._emscripten_bind_Config_set_maxvolume_1 = function () { return b.asm._emscripten_bind_Config_set_maxvolume_1.apply(null, arguments) }, Qf = b._emscripten_bind_btSoftRigidDynamicsWorld_getSolverInfo_0 = function () {
                    return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getSolverInfo_0.apply(null,
                        arguments)
                }, Rf = b._emscripten_bind_btCollisionDispatcher_getManifoldByIndexInternal_1 = function () { return b.asm._emscripten_bind_btCollisionDispatcher_getManifoldByIndexInternal_1.apply(null, arguments) }, Sf = b._emscripten_bind_btSoftBody_setTotalMass_2 = function () { return b.asm._emscripten_bind_btSoftBody_setTotalMass_2.apply(null, arguments) }, Tf = b._emscripten_bind_ClosestRayResultCallback_get_m_rayToWorld_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_rayToWorld_0.apply(null, arguments) },
        Uf = b._emscripten_bind_btGhostObject_setFriction_1 = function () { return b.asm._emscripten_bind_btGhostObject_setFriction_1.apply(null, arguments) }, Vf = b._emscripten_bind_btCollisionWorld_rayTest_3 = function () { return b.asm._emscripten_bind_btCollisionWorld_rayTest_3.apply(null, arguments) }; b.stackRestore = function () { return b.asm.stackRestore.apply(null, arguments) };
    var Wf = b._emscripten_bind_btRigidBody_setCcdSweptSphereRadius_1 = function () { return b.asm._emscripten_bind_btRigidBody_setCcdSweptSphereRadius_1.apply(null, arguments) }, Xf = b._emscripten_bind_btCylinderShapeZ_setMargin_1 = function () { return b.asm._emscripten_bind_btCylinderShapeZ_setMargin_1.apply(null, arguments) }, Yf = b._emscripten_bind_btRigidBody_setFriction_1 = function () { return b.asm._emscripten_bind_btRigidBody_setFriction_1.apply(null, arguments) }, Zf = b._emscripten_bind_LocalConvexResult_set_m_hitPointLocal_1 =
        function () { return b.asm._emscripten_bind_LocalConvexResult_set_m_hitPointLocal_1.apply(null, arguments) }, $f = b._emscripten_bind_btGhostObject_setWorldTransform_1 = function () { return b.asm._emscripten_bind_btGhostObject_setWorldTransform_1.apply(null, arguments) }, ag = b._emscripten_bind_tMaterialArray_size_0 = function () { return b.asm._emscripten_bind_tMaterialArray_size_0.apply(null, arguments) }, bg = b._emscripten_bind_RaycastInfo_set_m_hardPointWS_1 = function () {
            return b.asm._emscripten_bind_RaycastInfo_set_m_hardPointWS_1.apply(null,
                arguments)
        }, cg = b._emscripten_bind_btManifoldPoint_getAppliedImpulse_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_getAppliedImpulse_0.apply(null, arguments) }, dg = b._emscripten_bind_btDiscreteDynamicsWorld_removeRigidBody_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_removeRigidBody_1.apply(null, arguments) }, eg = b._emscripten_bind_btConvexHullShape___destroy___0 = function () { return b.asm._emscripten_bind_btConvexHullShape___destroy___0.apply(null, arguments) }, fg = b._emscripten_bind_btDiscreteDynamicsWorld_getBroadphase_0 =
            function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getBroadphase_0.apply(null, arguments) }, gg = b._emscripten_bind_btDiscreteDynamicsWorld_addAction_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addAction_1.apply(null, arguments) }, hg = b._emscripten_bind_btVector4_setX_1 = function () { return b.asm._emscripten_bind_btVector4_setX_1.apply(null, arguments) }, ig = b._emscripten_bind_btKinematicCharacterController_jump_0 = function () {
                return b.asm._emscripten_bind_btKinematicCharacterController_jump_0.apply(null,
                    arguments)
            }, jg = b._emscripten_bind_btCollisionObject_getUserPointer_0 = function () { return b.asm._emscripten_bind_btCollisionObject_getUserPointer_0.apply(null, arguments) }, kg = b._emscripten_bind_btWheelInfo_set_m_raycastInfo_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_raycastInfo_1.apply(null, arguments) }, lg = b._emscripten_bind_btCollisionWorld_contactTest_2 = function () { return b.asm._emscripten_bind_btCollisionWorld_contactTest_2.apply(null, arguments) }, mg = b._emscripten_bind_btConeTwistConstraint_setMaxMotorImpulseNormalized_1 =
                function () { return b.asm._emscripten_bind_btConeTwistConstraint_setMaxMotorImpulseNormalized_1.apply(null, arguments) }, ng = b._emscripten_bind_btConvexTriangleMeshShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape_setLocalScaling_1.apply(null, arguments) }, og = b._emscripten_bind_btRigidBody_upcast_1 = function () { return b.asm._emscripten_bind_btRigidBody_upcast_1.apply(null, arguments) }, pg = b._emscripten_bind_btTransform_setOrigin_1 = function () {
                    return b.asm._emscripten_bind_btTransform_setOrigin_1.apply(null,
                        arguments)
                }, qg = b._emscripten_bind_btVector4_setZ_1 = function () { return b.asm._emscripten_bind_btVector4_setZ_1.apply(null, arguments) }, rg = b._emscripten_bind_btQuadWord_y_0 = function () { return b.asm._emscripten_bind_btQuadWord_y_0.apply(null, arguments) }, sg = b._emscripten_bind_btTransform_getBasis_0 = function () { return b.asm._emscripten_bind_btTransform_getBasis_0.apply(null, arguments) }, tg = b._emscripten_bind_btPairCachingGhostObject_setFriction_1 = function () {
                    return b.asm._emscripten_bind_btPairCachingGhostObject_setFriction_1.apply(null,
                        arguments)
                }, ug = b._emscripten_bind_btSoftBody_setRollingFriction_1 = function () { return b.asm._emscripten_bind_btSoftBody_setRollingFriction_1.apply(null, arguments) }, vg = b._emscripten_bind_Config_set_kSRHR_CL_1 = function () { return b.asm._emscripten_bind_Config_set_kSRHR_CL_1.apply(null, arguments) }, wg = b._emscripten_bind_btCollisionDispatcher_getNumManifolds_0 = function () { return b.asm._emscripten_bind_btCollisionDispatcher_getNumManifolds_0.apply(null, arguments) }, xg = b._emscripten_bind_btVehicleRaycaster___destroy___0 =
                    function () { return b.asm._emscripten_bind_btVehicleRaycaster___destroy___0.apply(null, arguments) }, yg = b._emscripten_bind_ClosestRayResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback___destroy___0.apply(null, arguments) }, zg = b._emscripten_bind_ClosestConvexResultCallback_get_m_convexFromWorld_0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_convexFromWorld_0.apply(null, arguments) }, Ag = b._emscripten_bind_btCylinderShapeX_setMargin_1 =
                        function () { return b.asm._emscripten_bind_btCylinderShapeX_setMargin_1.apply(null, arguments) }, Bg = b._emscripten_bind_btQuadWord_w_0 = function () { return b.asm._emscripten_bind_btQuadWord_w_0.apply(null, arguments) }, Cg = b._emscripten_bind_Node___destroy___0 = function () { return b.asm._emscripten_bind_Node___destroy___0.apply(null, arguments) }, Dg = b._emscripten_bind_btAxisSweep3___destroy___0 = function () { return b.asm._emscripten_bind_btAxisSweep3___destroy___0.apply(null, arguments) }, Eg = b._emscripten_bind_btDiscreteDynamicsWorld_contactTest_2 =
                            function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_contactTest_2.apply(null, arguments) }, Fg = b._emscripten_bind_btBvhTriangleMeshShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btBvhTriangleMeshShape_calculateLocalInertia_2.apply(null, arguments) }, Gg = b._emscripten_bind_btCompoundShape_setMargin_1 = function () { return b.asm._emscripten_bind_btCompoundShape_setMargin_1.apply(null, arguments) }, Hg = b._emscripten_bind_btCompoundShape_getNumChildShapes_0 = function () {
                                return b.asm._emscripten_bind_btCompoundShape_getNumChildShapes_0.apply(null,
                                    arguments)
                            }, Ig = b._emscripten_bind_btSoftBodyWorldInfo_set_m_broadphase_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_m_broadphase_1.apply(null, arguments) }, Jg = b._emscripten_bind_btCapsuleShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btCapsuleShape_setLocalScaling_1.apply(null, arguments) }, Kg = b._emscripten_bind_btGhostObject_btGhostObject_0 = function () { return b.asm._emscripten_bind_btGhostObject_btGhostObject_0.apply(null, arguments) }, Lg = b._emscripten_bind_btConeShape_btConeShape_2 =
                                function () { return b.asm._emscripten_bind_btConeShape_btConeShape_2.apply(null, arguments) }, Mg = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalAngularDampingFactor_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalAngularDampingFactor_1.apply(null, arguments) }, Ng = b._emscripten_bind_btManifoldPoint_set_m_localPointA_1 = function () { return b.asm._emscripten_bind_btManifoldPoint_set_m_localPointA_1.apply(null, arguments) }, Og = b._emscripten_bind_btCapsuleShapeX_setMargin_1 =
                                    function () { return b.asm._emscripten_bind_btCapsuleShapeX_setMargin_1.apply(null, arguments) }, Pg = b._emscripten_bind_Config_set_kMT_1 = function () { return b.asm._emscripten_bind_Config_set_kMT_1.apply(null, arguments) }, Qg = b._emscripten_bind_btVector3_dot_1 = function () { return b.asm._emscripten_bind_btVector3_dot_1.apply(null, arguments) }, Rg = b._emscripten_bind_btGhostObject_getUserPointer_0 = function () { return b.asm._emscripten_bind_btGhostObject_getUserPointer_0.apply(null, arguments) }, Sg = b._emscripten_bind_btVector4_op_add_1 =
                                        function () { return b.asm._emscripten_bind_btVector4_op_add_1.apply(null, arguments) }, Tg = b._emscripten_bind_btWheelInfo___destroy___0 = function () { return b.asm._emscripten_bind_btWheelInfo___destroy___0.apply(null, arguments) }, Ug = b._emscripten_bind_btSoftRigidDynamicsWorld_getSoftBodyArray_0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getSoftBodyArray_0.apply(null, arguments) }, Vg = b._emscripten_bind_btHingeConstraint_btHingeConstraint_4 = function () {
                                            return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_4.apply(null,
                                                arguments)
                                        }, Wg = b._emscripten_bind_btTransform_setRotation_1 = function () { return b.asm._emscripten_bind_btTransform_setRotation_1.apply(null, arguments) }, Xg = b._emscripten_bind_Config_set_kSHR_1 = function () { return b.asm._emscripten_bind_Config_set_kSHR_1.apply(null, arguments) }, Yg = b._emscripten_bind_btPoint2PointConstraint_enableFeedback_1 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_enableFeedback_1.apply(null, arguments) }, Zg = b._emscripten_bind_ClosestRayResultCallback_set_m_collisionFilterGroup_1 =
                                            function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_collisionFilterGroup_1.apply(null, arguments) }, $g = b._emscripten_bind_btAxisSweep3_btAxisSweep3_2 = function () { return b.asm._emscripten_bind_btAxisSweep3_btAxisSweep3_2.apply(null, arguments) }, ah = b._emscripten_bind_btAxisSweep3_btAxisSweep3_3 = function () { return b.asm._emscripten_bind_btAxisSweep3_btAxisSweep3_3.apply(null, arguments) }, bh = b._emscripten_bind_btDynamicsWorld___destroy___0 = function () {
                                                return b.asm._emscripten_bind_btDynamicsWorld___destroy___0.apply(null,
                                                    arguments)
                                            }, ch = b._emscripten_bind_btVector3_setY_1 = function () { return b.asm._emscripten_bind_btVector3_setY_1.apply(null, arguments) }, dh = b._emscripten_bind_btAxisSweep3_btAxisSweep3_4 = function () { return b.asm._emscripten_bind_btAxisSweep3_btAxisSweep3_4.apply(null, arguments) }, eh = b._emscripten_bind_btAxisSweep3_btAxisSweep3_5 = function () { return b.asm._emscripten_bind_btAxisSweep3_btAxisSweep3_5.apply(null, arguments) }, fh = b._emscripten_bind_btQuadWord_setX_1 = function () {
                                                return b.asm._emscripten_bind_btQuadWord_setX_1.apply(null,
                                                    arguments)
                                            }, gh = b._emscripten_bind_tMaterialArray___destroy___0 = function () { return b.asm._emscripten_bind_tMaterialArray___destroy___0.apply(null, arguments) }, hh = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalLinearDampingThresholdSqr_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalLinearDampingThresholdSqr_1.apply(null, arguments) }, ih = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalLinearDampingThresholdSqr_0 = function () {
                                                return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalLinearDampingThresholdSqr_0.apply(null,
                                                    arguments)
                                            }, jh = b._emscripten_bind_Config_set_piterations_1 = function () { return b.asm._emscripten_bind_Config_set_piterations_1.apply(null, arguments) }, kh = b._emscripten_bind_btOverlappingPairCache___destroy___0 = function () { return b.asm._emscripten_bind_btOverlappingPairCache___destroy___0.apply(null, arguments) }, lh = b._emscripten_bind_btRigidBody_setUserIndex_1 = function () { return b.asm._emscripten_bind_btRigidBody_setUserIndex_1.apply(null, arguments) }, mh = b._emscripten_bind_Material_get_m_kAST_0 = function () {
                                                return b.asm._emscripten_bind_Material_get_m_kAST_0.apply(null,
                                                    arguments)
                                            }, nh = b._emscripten_bind_btConstraintSetting___destroy___0 = function () { return b.asm._emscripten_bind_btConstraintSetting___destroy___0.apply(null, arguments) }, oh = b._emscripten_bind_btWheelInfo_btWheelInfo_1 = function () { return b.asm._emscripten_bind_btWheelInfo_btWheelInfo_1.apply(null, arguments) }, ph = b._emscripten_bind_RayResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_RayResultCallback___destroy___0.apply(null, arguments) }, qh = b._emscripten_bind_RaycastInfo_get_m_contactNormalWS_0 =
                                                function () { return b.asm._emscripten_bind_RaycastInfo_get_m_contactNormalWS_0.apply(null, arguments) }, rh = b._emscripten_bind_btSoftBodyWorldInfo_get_water_density_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_water_density_0.apply(null, arguments) }, sh = b._emscripten_bind_btPersistentManifold_getBody0_0 = function () { return b.asm._emscripten_bind_btPersistentManifold_getBody0_0.apply(null, arguments) }, th = b._emscripten_bind_btConeShapeX_btConeShapeX_2 = function () {
                                                    return b.asm._emscripten_bind_btConeShapeX_btConeShapeX_2.apply(null,
                                                        arguments)
                                                }, uh = b._emscripten_bind_btSoftBody_setCcdSweptSphereRadius_1 = function () { return b.asm._emscripten_bind_btSoftBody_setCcdSweptSphereRadius_1.apply(null, arguments) }, vh = b._emscripten_bind_btConeTwistConstraint_enableFeedback_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_enableFeedback_1.apply(null, arguments) }, wh = b._emscripten_bind_btRaycastVehicle_setPitchControl_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setPitchControl_1.apply(null, arguments) }, xh = b._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration_btSoftBodyRigidBodyCollisionConfiguration_0 =
                                                    function () { return b.asm._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration_btSoftBodyRigidBodyCollisionConfiguration_0.apply(null, arguments) }, yh = b._emscripten_bind_btCapsuleShapeZ_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btCapsuleShapeZ_setLocalScaling_1.apply(null, arguments) }, zh = b._emscripten_bind_Config_get_piterations_0 = function () { return b.asm._emscripten_bind_Config_get_piterations_0.apply(null, arguments) }, Ah = b._emscripten_bind_btSoftBody_translate_1 = function () {
                                                        return b.asm._emscripten_bind_btSoftBody_translate_1.apply(null,
                                                            arguments)
                                                    }, Bh = b._emscripten_bind_btSliderConstraint_setUpperLinLimit_1 = function () { return b.asm._emscripten_bind_btSliderConstraint_setUpperLinLimit_1.apply(null, arguments) }, Ch = b._emscripten_bind_btConeTwistConstraint_btConeTwistConstraint_2 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_btConeTwistConstraint_2.apply(null, arguments) }, Dh = b._emscripten_bind_btVector3_op_mul_1 = function () { return b.asm._emscripten_bind_btVector3_op_mul_1.apply(null, arguments) }, Eh = b._emscripten_bind_btConcaveShape___destroy___0 =
                                                        function () { return b.asm._emscripten_bind_btConcaveShape___destroy___0.apply(null, arguments) }, Fh = b._emscripten_bind_Config_get_kSK_SPLT_CL_0 = function () { return b.asm._emscripten_bind_Config_get_kSK_SPLT_CL_0.apply(null, arguments) }, Gh = b._emscripten_bind_btConeTwistConstraint_btConeTwistConstraint_4 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_btConeTwistConstraint_4.apply(null, arguments) }, Hh = b._emscripten_bind_btQuaternion_x_0 = function () {
                                                            return b.asm._emscripten_bind_btQuaternion_x_0.apply(null,
                                                                arguments)
                                                        }, Ih = b._emscripten_bind_btSoftRigidDynamicsWorld_btSoftRigidDynamicsWorld_5 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_btSoftRigidDynamicsWorld_5.apply(null, arguments) }, Jh = b._emscripten_bind_btVehicleRaycasterResult_set_m_distFraction_1 = function () { return b.asm._emscripten_bind_btVehicleRaycasterResult_set_m_distFraction_1.apply(null, arguments) }, Kh = b._emscripten_bind_Config_set_timescale_1 = function () { return b.asm._emscripten_bind_Config_set_timescale_1.apply(null, arguments) },
        Lh = b._emscripten_bind_LocalConvexResult_set_m_hitNormalLocal_1 = function () { return b.asm._emscripten_bind_LocalConvexResult_set_m_hitNormalLocal_1.apply(null, arguments) }, Mh = b._emscripten_bind_btConcaveShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btConcaveShape_setLocalScaling_1.apply(null, arguments) }, Nh = b._emscripten_bind_btDiscreteDynamicsWorld_getDispatchInfo_0 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getDispatchInfo_0.apply(null, arguments) }, Oh = b._emscripten_bind_btConeShapeX_setLocalScaling_1 =
            function () { return b.asm._emscripten_bind_btConeShapeX_setLocalScaling_1.apply(null, arguments) }, Ph = b._emscripten_bind_btSoftBody_appendLink_4 = function () { return b.asm._emscripten_bind_btSoftBody_appendLink_4.apply(null, arguments) }, Qh = b._emscripten_bind_btQuaternion_z_0 = function () { return b.asm._emscripten_bind_btQuaternion_z_0.apply(null, arguments) }, Rh = b._emscripten_bind_btConvexHullShape_btConvexHullShape_0 = function () { return b.asm._emscripten_bind_btConvexHullShape_btConvexHullShape_0.apply(null, arguments) },
        Sh = b._emscripten_bind_btWheelInfo_set_m_maxSuspensionForce_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_maxSuspensionForce_1.apply(null, arguments) }, Th = b._emscripten_bind_btConstraintSetting_get_m_damping_0 = function () { return b.asm._emscripten_bind_btConstraintSetting_get_m_damping_0.apply(null, arguments) }, Uh = b._emscripten_bind_btVector4_op_mul_1 = function () { return b.asm._emscripten_bind_btVector4_op_mul_1.apply(null, arguments) }, Vh = b._emscripten_bind_btSoftRigidDynamicsWorld_removeCollisionObject_1 =
            function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_removeCollisionObject_1.apply(null, arguments) }, Wh = b._emscripten_bind_Config_get_kLF_0 = function () { return b.asm._emscripten_bind_Config_get_kLF_0.apply(null, arguments) }, Xh = b._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_3.apply(null, arguments) }, Yh = b._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_2 = function () {
                return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_2.apply(null,
                    arguments)
            }, Zh = b._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addCollisionObject_1.apply(null, arguments) }, $h = b._emscripten_bind_btGhostObject_setContactProcessingThreshold_1 = function () { return b.asm._emscripten_bind_btGhostObject_setContactProcessingThreshold_1.apply(null, arguments) }, ai = b._emscripten_bind_btSoftBodyHelpers_CreateFromConvexHull_4 = function () {
                return b.asm._emscripten_bind_btSoftBodyHelpers_CreateFromConvexHull_4.apply(null,
                    arguments)
            }, bi = b._emscripten_bind_btCollisionWorld_getBroadphase_0 = function () { return b.asm._emscripten_bind_btCollisionWorld_getBroadphase_0.apply(null, arguments) }, ci = b._emscripten_bind_btCylinderShape_btCylinderShape_1 = function () { return b.asm._emscripten_bind_btCylinderShape_btCylinderShape_1.apply(null, arguments) }, di = b._emscripten_bind_btDispatcherInfo_set_m_stepCount_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_stepCount_1.apply(null, arguments) }, ei = b._emscripten_bind_btContactSolverInfo_set_m_splitImpulse_1 =
                function () { return b.asm._emscripten_bind_btContactSolverInfo_set_m_splitImpulse_1.apply(null, arguments) }, fi = b._emscripten_bind_btKinematicCharacterController_updateAction_2 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_updateAction_2.apply(null, arguments) }, gi = b._emscripten_bind_btDefaultMotionState_btDefaultMotionState_2 = function () { return b.asm._emscripten_bind_btDefaultMotionState_btDefaultMotionState_2.apply(null, arguments) }, hi = b._emscripten_bind_Material_set_m_flags_1 =
                    function () { return b.asm._emscripten_bind_Material_set_m_flags_1.apply(null, arguments) }, ii = b._emscripten_bind_btDefaultMotionState_btDefaultMotionState_0 = function () { return b.asm._emscripten_bind_btDefaultMotionState_btDefaultMotionState_0.apply(null, arguments) }, ji = b._emscripten_bind_btDefaultMotionState_btDefaultMotionState_1 = function () { return b.asm._emscripten_bind_btDefaultMotionState_btDefaultMotionState_1.apply(null, arguments) }, ki = b._emscripten_bind_Config_get_viterations_0 = function () {
                        return b.asm._emscripten_bind_Config_get_viterations_0.apply(null,
                            arguments)
                    }, li = b._emscripten_bind_btKinematicCharacterController_canJump_0 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_canJump_0.apply(null, arguments) }, mi = b._emscripten_bind_btSoftBodyArray_at_1 = function () { return b.asm._emscripten_bind_btSoftBodyArray_at_1.apply(null, arguments) }, ni = b._emscripten_bind_btPairCachingGhostObject_setUserIndex_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setUserIndex_1.apply(null, arguments) }, oi = b._emscripten_bind_btRigidBody_isActive_0 =
                        function () { return b.asm._emscripten_bind_btRigidBody_isActive_0.apply(null, arguments) }, pi = b._emscripten_bind_btRaycastVehicle_btRaycastVehicle_3 = function () { return b.asm._emscripten_bind_btRaycastVehicle_btRaycastVehicle_3.apply(null, arguments) }, qi = b._emscripten_bind_btSoftBody_transform_1 = function () { return b.asm._emscripten_bind_btSoftBody_transform_1.apply(null, arguments) }, ri = b._emscripten_bind_btSoftRigidDynamicsWorld_getDispatcher_0 = function () {
                            return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getDispatcher_0.apply(null,
                                arguments)
                        }, si = b._emscripten_bind_btCylinderShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btCylinderShape_setLocalScaling_1.apply(null, arguments) }, ti = b._emscripten_bind_btPairCachingGhostObject_getWorldTransform_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getWorldTransform_0.apply(null, arguments) }, ui = b._emscripten_bind_btCompoundShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btCompoundShape_calculateLocalInertia_2.apply(null, arguments) },
        vi = b._emscripten_bind_btCollisionWorld_getDispatchInfo_0 = function () { return b.asm._emscripten_bind_btCollisionWorld_getDispatchInfo_0.apply(null, arguments) }, wi = b._emscripten_bind_btRigidBody_setCollisionShape_1 = function () { return b.asm._emscripten_bind_btRigidBody_setCollisionShape_1.apply(null, arguments) }, xi = b._emscripten_bind_btSoftBody_appendTetra_5 = function () { return b.asm._emscripten_bind_btSoftBody_appendTetra_5.apply(null, arguments) }, yi = b._emscripten_bind_btConeShapeX___destroy___0 = function () {
            return b.asm._emscripten_bind_btConeShapeX___destroy___0.apply(null,
                arguments)
        }, zi = b._emscripten_bind_btCollisionObject_getCollisionFlags_0 = function () { return b.asm._emscripten_bind_btCollisionObject_getCollisionFlags_0.apply(null, arguments) }, Ai = b._emscripten_bind_btDispatcherInfo_set_m_enableSatConvex_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_enableSatConvex_1.apply(null, arguments) }, Bi = b._emscripten_bind_btConeTwistConstraint_enableMotor_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_enableMotor_1.apply(null, arguments) }, Ci =
            b._emscripten_bind_btWheelInfo_set_m_chassisConnectionPointCS_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_chassisConnectionPointCS_1.apply(null, arguments) }, Di = b._emscripten_bind_btVehicleRaycasterResult_get_m_hitPointInWorld_0 = function () { return b.asm._emscripten_bind_btVehicleRaycasterResult_get_m_hitPointInWorld_0.apply(null, arguments) }, Ei = b._emscripten_bind_btWheelInfo_set_m_wheelsDampingCompression_1 = function () {
                return b.asm._emscripten_bind_btWheelInfo_set_m_wheelsDampingCompression_1.apply(null,
                    arguments)
            }, Fi = b._emscripten_bind_btDefaultCollisionConfiguration_btDefaultCollisionConfiguration_0 = function () { return b.asm._emscripten_bind_btDefaultCollisionConfiguration_btDefaultCollisionConfiguration_0.apply(null, arguments) }, Gi = b._emscripten_bind_btPairCachingGhostObject_setRestitution_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setRestitution_1.apply(null, arguments) }, Hi = b._emscripten_bind_Config_set_kAHR_1 = function () {
                return b.asm._emscripten_bind_Config_set_kAHR_1.apply(null,
                    arguments)
            }, Ii = b._emscripten_bind_btHeightfieldTerrainShape_getMargin_0 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape_getMargin_0.apply(null, arguments) }, Ji = b._emscripten_bind_ConvexResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_ConvexResultCallback___destroy___0.apply(null, arguments) }, Ki = b._emscripten_bind_btSoftRigidDynamicsWorld_rayTest_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_rayTest_3.apply(null, arguments) }, Li = b._emscripten_bind_btQuaternion_getAngle_0 =
                function () { return b.asm._emscripten_bind_btQuaternion_getAngle_0.apply(null, arguments) }, Mi = b._emscripten_bind_btSliderConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btSliderConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, Ni = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalDampingFactor_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalDampingFactor_1.apply(null, arguments) }, Oi = b._emscripten_bind_btKinematicCharacterController_btKinematicCharacterController_3 =
                    function () { return b.asm._emscripten_bind_btKinematicCharacterController_btKinematicCharacterController_3.apply(null, arguments) }, Pi = b._emscripten_bind_btCollisionObject_setContactProcessingThreshold_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setContactProcessingThreshold_1.apply(null, arguments) }, Qi = b._emscripten_bind_btCompoundShape___destroy___0 = function () { return b.asm._emscripten_bind_btCompoundShape___destroy___0.apply(null, arguments) }, Ri = b._emscripten_bind_btHingeConstraint_setMotorTarget_2 =
                        function () { return b.asm._emscripten_bind_btHingeConstraint_setMotorTarget_2.apply(null, arguments) }, Si = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalAngularDampingFactor_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalAngularDampingFactor_0.apply(null, arguments) }, Ti = b._emscripten_bind_LocalConvexResult___destroy___0 = function () { return b.asm._emscripten_bind_LocalConvexResult___destroy___0.apply(null, arguments) }, Ui = b._emscripten_bind_btSequentialImpulseConstraintSolver___destroy___0 =
                            function () { return b.asm._emscripten_bind_btSequentialImpulseConstraintSolver___destroy___0.apply(null, arguments) }; b.setThrew = function () { return b.asm.setThrew.apply(null, arguments) };
    var Vi = b._emscripten_bind_btSoftBodyHelpers_CreateRope_5 = function () { return b.asm._emscripten_bind_btSoftBodyHelpers_CreateRope_5.apply(null, arguments) }, Wi = b._emscripten_bind_btRaycastVehicle___destroy___0 = function () { return b.asm._emscripten_bind_btRaycastVehicle___destroy___0.apply(null, arguments) }, Xi = b._emscripten_bind_btCollisionWorld_addCollisionObject_3 = function () { return b.asm._emscripten_bind_btCollisionWorld_addCollisionObject_3.apply(null, arguments) }, Yi = b._emscripten_bind_btRigidBody_getCollisionFlags_0 =
        function () { return b.asm._emscripten_bind_btRigidBody_getCollisionFlags_0.apply(null, arguments) }, Zi = b._emscripten_bind_btCollisionShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btCollisionShape_setLocalScaling_1.apply(null, arguments) }, $i = b._emscripten_bind_ClosestConvexResultCallback_get_m_closestHitFraction_0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_closestHitFraction_0.apply(null, arguments) }, aj = b._emscripten_bind_LocalConvexResult_get_m_hitCollisionObject_0 =
            function () { return b.asm._emscripten_bind_LocalConvexResult_get_m_hitCollisionObject_0.apply(null, arguments) }, bj = b._emscripten_bind_btMatrix3x3_setEulerZYX_3 = function () { return b.asm._emscripten_bind_btMatrix3x3_setEulerZYX_3.apply(null, arguments) }, cj = b._emscripten_bind_btSoftBody_getTotalMass_0 = function () { return b.asm._emscripten_bind_btSoftBody_getTotalMass_0.apply(null, arguments) }, dj = b._emscripten_bind_btDispatcherInfo_get_m_convexConservativeDistanceThreshold_0 = function () {
                return b.asm._emscripten_bind_btDispatcherInfo_get_m_convexConservativeDistanceThreshold_0.apply(null,
                    arguments)
            }, ej = b._emscripten_bind_btRigidBody_getUserPointer_0 = function () { return b.asm._emscripten_bind_btRigidBody_getUserPointer_0.apply(null, arguments) }, fj = b._emscripten_bind_Config_get_kSHR_0 = function () { return b.asm._emscripten_bind_Config_get_kSHR_0.apply(null, arguments) }, gj = b._emscripten_bind_btHeightfieldTerrainShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape_calculateLocalInertia_2.apply(null, arguments) }, hj = b._emscripten_bind_btRigidBody_setMotionState_1 =
                function () { return b.asm._emscripten_bind_btRigidBody_setMotionState_1.apply(null, arguments) }, ij = b._emscripten_bind_RayResultCallback_get_m_collisionFilterMask_0 = function () { return b.asm._emscripten_bind_RayResultCallback_get_m_collisionFilterMask_0.apply(null, arguments) }, jj = b._emscripten_bind_btCollisionWorld_getDispatcher_0 = function () { return b.asm._emscripten_bind_btCollisionWorld_getDispatcher_0.apply(null, arguments) }, kj = b._emscripten_bind_btVector4_dot_1 = function () {
                    return b.asm._emscripten_bind_btVector4_dot_1.apply(null,
                        arguments)
                }, lj = b._emscripten_bind_btSoftBody_forceActivationState_1 = function () { return b.asm._emscripten_bind_btSoftBody_forceActivationState_1.apply(null, arguments) }, mj = b._emscripten_bind_btCollisionObject_setRollingFriction_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setRollingFriction_1.apply(null, arguments) }, nj = b._emscripten_bind_Config_set_kSK_SPLT_CL_1 = function () { return b.asm._emscripten_bind_Config_set_kSK_SPLT_CL_1.apply(null, arguments) }, oj = b._emscripten_bind_RayResultCallback_set_m_collisionFilterGroup_1 =
                    function () { return b.asm._emscripten_bind_RayResultCallback_set_m_collisionFilterGroup_1.apply(null, arguments) }, pj = b._emscripten_bind_btVehicleRaycaster_castRay_3 = function () { return b.asm._emscripten_bind_btVehicleRaycaster_castRay_3.apply(null, arguments) }, qj = b._emscripten_bind_btCylinderShapeX_getMargin_0 = function () { return b.asm._emscripten_bind_btCylinderShapeX_getMargin_0.apply(null, arguments) }, rj = b._emscripten_bind_btRigidBody_setDamping_2 = function () {
                        return b.asm._emscripten_bind_btRigidBody_setDamping_2.apply(null,
                            arguments)
                    }, sj = b._emscripten_bind_btDynamicsWorld_getDispatcher_0 = function () { return b.asm._emscripten_bind_btDynamicsWorld_getDispatcher_0.apply(null, arguments) }, tj = b._emscripten_bind_btGhostObject_setCollisionFlags_1 = function () { return b.asm._emscripten_bind_btGhostObject_setCollisionFlags_1.apply(null, arguments) }, uj = b._emscripten_bind_btMatrix3x3_getRotation_1 = function () { return b.asm._emscripten_bind_btMatrix3x3_getRotation_1.apply(null, arguments) }, vj = b._emscripten_bind_btWheelInfo_set_m_engineForce_1 =
                        function () { return b.asm._emscripten_bind_btWheelInfo_set_m_engineForce_1.apply(null, arguments) }, wj = b._emscripten_bind_btConeTwistConstraint_setMaxMotorImpulse_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_setMaxMotorImpulse_1.apply(null, arguments) }, xj = b._emscripten_bind_btPersistentManifold_getNumContacts_0 = function () { return b.asm._emscripten_bind_btPersistentManifold_getNumContacts_0.apply(null, arguments) }, yj = b._emscripten_bind_btCylinderShapeX_setLocalScaling_1 = function () {
                            return b.asm._emscripten_bind_btCylinderShapeX_setLocalScaling_1.apply(null,
                                arguments)
                        }, zj = b._emscripten_bind_btDbvtBroadphase_btDbvtBroadphase_0 = function () { return b.asm._emscripten_bind_btDbvtBroadphase_btDbvtBroadphase_0.apply(null, arguments) }, Aj = b._emscripten_bind_btSoftBodyHelpers_btSoftBodyHelpers_0 = function () { return b.asm._emscripten_bind_btSoftBodyHelpers_btSoftBodyHelpers_0.apply(null, arguments) }, Bj = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalDamping_0 = function () {
                            return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalDamping_0.apply(null,
                                arguments)
                        }, Cj = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_bIsFrontWheel_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_bIsFrontWheel_0.apply(null, arguments) }, Dj = b._emscripten_bind_btOverlappingPairCallback___destroy___0 = function () { return b.asm._emscripten_bind_btOverlappingPairCallback___destroy___0.apply(null, arguments) }, Ej = b._emscripten_bind_btWheelInfo_get_m_suspensionRelativeVelocity_0 = function () {
                            return b.asm._emscripten_bind_btWheelInfo_get_m_suspensionRelativeVelocity_0.apply(null,
                                arguments)
                        }, Fj = b._emscripten_bind_btManifoldPoint_get_m_positionWorldOnB_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_get_m_positionWorldOnB_0.apply(null, arguments) }, Gj = b._emscripten_bind_tNodeArray___destroy___0 = function () { return b.asm._emscripten_bind_tNodeArray___destroy___0.apply(null, arguments) }, Hj = b._emscripten_bind_btPairCachingGhostObject_setCcdSweptSphereRadius_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setCcdSweptSphereRadius_1.apply(null, arguments) },
        Ij = b._emscripten_bind_btHingeConstraint_enableAngularMotor_3 = function () { return b.asm._emscripten_bind_btHingeConstraint_enableAngularMotor_3.apply(null, arguments) }, Jj = b._emscripten_bind_btRigidBody_setContactProcessingThreshold_1 = function () { return b.asm._emscripten_bind_btRigidBody_setContactProcessingThreshold_1.apply(null, arguments) }, Kj = b._emscripten_bind_btRigidBody_getLinearVelocity_0 = function () { return b.asm._emscripten_bind_btRigidBody_getLinearVelocity_0.apply(null, arguments) }, Lj = b._emscripten_bind_btRigidBody_applyImpulse_2 =
            function () { return b.asm._emscripten_bind_btRigidBody_applyImpulse_2.apply(null, arguments) }, Mj = b._emscripten_bind_btConcaveShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConcaveShape_calculateLocalInertia_2.apply(null, arguments) }, Nj = b._emscripten_bind_RaycastInfo_get_m_groundObject_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_groundObject_0.apply(null, arguments) }, Oj = b._emscripten_bind_btRigidBody_setWorldTransform_1 = function () {
                return b.asm._emscripten_bind_btRigidBody_setWorldTransform_1.apply(null,
                    arguments)
            }, Pj = b._emscripten_bind_LocalConvexResult_set_m_localShapeInfo_1 = function () { return b.asm._emscripten_bind_LocalConvexResult_set_m_localShapeInfo_1.apply(null, arguments) }, Qj = b._emscripten_bind_btRigidBody_setAngularVelocity_1 = function () { return b.asm._emscripten_bind_btRigidBody_setAngularVelocity_1.apply(null, arguments) }, Rj = b._emscripten_bind_btGeneric6DofSpringConstraint_btGeneric6DofSpringConstraint_3 = function () {
                return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_btGeneric6DofSpringConstraint_3.apply(null,
                    arguments)
            }, Sj = b._emscripten_bind_Config_get_kDP_0 = function () { return b.asm._emscripten_bind_Config_get_kDP_0.apply(null, arguments) }, Tj = b._emscripten_bind_btConvexShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btConvexShape_setLocalScaling_1.apply(null, arguments) }, Uj = b._emscripten_bind_Config_get_collisions_0 = function () { return b.asm._emscripten_bind_Config_get_collisions_0.apply(null, arguments) }, Vj = b._emscripten_bind_btTriangleMeshShape_calculateLocalInertia_2 = function () {
                return b.asm._emscripten_bind_btTriangleMeshShape_calculateLocalInertia_2.apply(null,
                    arguments)
            }; b.stackSave = function () { return b.asm.stackSave.apply(null, arguments) };
    var Wj = b._emscripten_bind_btRaycastVehicle_setUserConstraintId_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setUserConstraintId_1.apply(null, arguments) }, Ka = b._free = function () { return b.asm._free.apply(null, arguments) }, Xj = b._emscripten_bind_btPairCachingGhostObject_setContactProcessingThreshold_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setContactProcessingThreshold_1.apply(null, arguments) }, Yj = b._emscripten_bind_btGeneric6DofConstraint_setLinearUpperLimit_1 = function () {
        return b.asm._emscripten_bind_btGeneric6DofConstraint_setLinearUpperLimit_1.apply(null,
            arguments)
    }, Zj = b._emscripten_bind_ClosestRayResultCallback_get_m_collisionFilterMask_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_collisionFilterMask_0.apply(null, arguments) }, ak = b._emscripten_bind_RayResultCallback_hasHit_0 = function () { return b.asm._emscripten_bind_RayResultCallback_hasHit_0.apply(null, arguments) }, bk = b._emscripten_bind_btRigidBody_applyLocalTorque_1 = function () { return b.asm._emscripten_bind_btRigidBody_applyLocalTorque_1.apply(null, arguments) }, ck = b._emscripten_bind_Config___destroy___0 =
        function () { return b.asm._emscripten_bind_Config___destroy___0.apply(null, arguments) }, dk = b._emscripten_bind_btVehicleTuning_set_m_maxSuspensionForce_1 = function () { return b.asm._emscripten_bind_btVehicleTuning_set_m_maxSuspensionForce_1.apply(null, arguments) }, ek = b._emscripten_bind_btVehicleTuning_get_m_suspensionDamping_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_suspensionDamping_0.apply(null, arguments) }, fk = b._emscripten_bind_btRaycastVehicle_getWheelTransformWS_1 = function () {
            return b.asm._emscripten_bind_btRaycastVehicle_getWheelTransformWS_1.apply(null,
                arguments)
        }, gk = b._emscripten_bind_btQuaternion_normalize_0 = function () { return b.asm._emscripten_bind_btQuaternion_normalize_0.apply(null, arguments) }, hk = b._emscripten_bind_btQuaternion___destroy___0 = function () { return b.asm._emscripten_bind_btQuaternion___destroy___0.apply(null, arguments) }, ik = b._emscripten_bind_btWheelInfo_get_m_frictionSlip_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_frictionSlip_0.apply(null, arguments) }, jk = b._emscripten_bind_btConeShapeZ_setLocalScaling_1 = function () {
            return b.asm._emscripten_bind_btConeShapeZ_setLocalScaling_1.apply(null,
                arguments)
        }, kk = b._emscripten_bind_btSoftBodyWorldInfo_get_m_dispatcher_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_m_dispatcher_0.apply(null, arguments) }, lk = b._emscripten_bind_btGeneric6DofSpringConstraint___destroy___0 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint___destroy___0.apply(null, arguments) }, mk = b._emscripten_bind_btRaycastVehicle_getNumWheels_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getNumWheels_0.apply(null, arguments) }, nk =
            b._emscripten_bind_btVehicleTuning_set_m_maxSuspensionTravelCm_1 = function () { return b.asm._emscripten_bind_btVehicleTuning_set_m_maxSuspensionTravelCm_1.apply(null, arguments) }, ok = b._emscripten_bind_Material_set_m_kAST_1 = function () { return b.asm._emscripten_bind_Material_set_m_kAST_1.apply(null, arguments) }, pk = b._emscripten_bind_btGhostObject_setRollingFriction_1 = function () { return b.asm._emscripten_bind_btGhostObject_setRollingFriction_1.apply(null, arguments) }, qk = b._emscripten_bind_btCylinderShapeZ_btCylinderShapeZ_1 =
                function () { return b.asm._emscripten_bind_btCylinderShapeZ_btCylinderShapeZ_1.apply(null, arguments) }, rk = b._emscripten_bind_btSoftBodyArray___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBodyArray___destroy___0.apply(null, arguments) }, sk = b._emscripten_bind_btCompoundShape_btCompoundShape_0 = function () { return b.asm._emscripten_bind_btCompoundShape_btCompoundShape_0.apply(null, arguments) }, tk = b._emscripten_bind_btCompoundShape_btCompoundShape_1 = function () {
                    return b.asm._emscripten_bind_btCompoundShape_btCompoundShape_1.apply(null,
                        arguments)
                }, uk = b._emscripten_bind_btOverlappingPairCache_setInternalGhostPairCallback_1 = function () { return b.asm._emscripten_bind_btOverlappingPairCache_setInternalGhostPairCallback_1.apply(null, arguments) }, vk = b._emscripten_bind_btStaticPlaneShape_btStaticPlaneShape_2 = function () { return b.asm._emscripten_bind_btStaticPlaneShape_btStaticPlaneShape_2.apply(null, arguments) }, nb = b.__GLOBAL__sub_I_btQuickprof_cpp = function () { return b.asm.__GLOBAL__sub_I_btQuickprof_cpp.apply(null, arguments) }, wk = b._emscripten_bind_btDispatcherInfo_set_m_convexConservativeDistanceThreshold_1 =
                    function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_convexConservativeDistanceThreshold_1.apply(null, arguments) }, xk = b._emscripten_bind_btSoftBody_checkLink_2 = function () { return b.asm._emscripten_bind_btSoftBody_checkLink_2.apply(null, arguments) }, yk = b._emscripten_bind_btSoftBody_getCollisionShape_0 = function () { return b.asm._emscripten_bind_btSoftBody_getCollisionShape_0.apply(null, arguments) }, zk = b._emscripten_bind_Config_get_kDG_0 = function () {
                        return b.asm._emscripten_bind_Config_get_kDG_0.apply(null,
                            arguments)
                    }, Ak = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_linearDamping_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_linearDamping_1.apply(null, arguments) }, Bk = b._emscripten_bind_btDefaultVehicleRaycaster___destroy___0 = function () { return b.asm._emscripten_bind_btDefaultVehicleRaycaster___destroy___0.apply(null, arguments) }, Ck = b._emscripten_bind_btPairCachingGhostObject_setAnisotropicFriction_2 = function () {
                        return b.asm._emscripten_bind_btPairCachingGhostObject_setAnisotropicFriction_2.apply(null,
                            arguments)
                    }, Dk = b._emscripten_bind_Node_get_m_x_0 = function () { return b.asm._emscripten_bind_Node_get_m_x_0.apply(null, arguments) }, Ek = b._emscripten_bind_btCollisionObject_getWorldTransform_0 = function () { return b.asm._emscripten_bind_btCollisionObject_getWorldTransform_0.apply(null, arguments) }, Fk = b._emscripten_bind_ClosestRayResultCallback_hasHit_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_hasHit_0.apply(null, arguments) }, Gk = b._emscripten_bind_btCompoundShape_addChildShape_2 = function () {
                        return b.asm._emscripten_bind_btCompoundShape_addChildShape_2.apply(null,
                            arguments)
                    }, Hk = b._emscripten_bind_btDispatcher___destroy___0 = function () { return b.asm._emscripten_bind_btDispatcher___destroy___0.apply(null, arguments) }, Ik = b._emscripten_bind_btVehicleTuning_get_m_suspensionCompression_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_suspensionCompression_0.apply(null, arguments) }, Bb = b._llvm_bswap_i16 = function () { return b.asm._llvm_bswap_i16.apply(null, arguments) }, Jk = b._emscripten_bind_btDiscreteDynamicsWorld___destroy___0 = function () {
                        return b.asm._emscripten_bind_btDiscreteDynamicsWorld___destroy___0.apply(null,
                            arguments)
                    }, Kk = b._emscripten_bind_btConvexShape___destroy___0 = function () { return b.asm._emscripten_bind_btConvexShape___destroy___0.apply(null, arguments) }, Lk = b._emscripten_bind_btRaycastVehicle_updateWheelTransformsWS_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_updateWheelTransformsWS_1.apply(null, arguments) }, Mk = b._emscripten_bind_btWheelInfo_set_m_brake_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_brake_1.apply(null, arguments) }, Eb = b._memmove = function () {
                        return b.asm._memmove.apply(null,
                            arguments)
                    }, Nk = b._emscripten_bind_btWheelInfo_set_m_worldTransform_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_worldTransform_1.apply(null, arguments) }, Ok = b._emscripten_bind_btCapsuleShapeX_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btCapsuleShapeX_setLocalScaling_1.apply(null, arguments) }, Pk = b._emscripten_bind_btPairCachingGhostObject_getCollisionShape_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getCollisionShape_0.apply(null, arguments) }, Qk =
            b._emscripten_bind_btSoftBody_getCollisionFlags_0 = function () { return b.asm._emscripten_bind_btSoftBody_getCollisionFlags_0.apply(null, arguments) }, Rk = b._emscripten_bind_btRaycastVehicle_getChassisWorldTransform_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getChassisWorldTransform_0.apply(null, arguments) }, Sk = b._emscripten_bind_btCollisionObject_setRestitution_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setRestitution_1.apply(null, arguments) }, Tk = b._emscripten_bind_btRigidBody_applyCentralForce_1 =
                function () { return b.asm._emscripten_bind_btRigidBody_applyCentralForce_1.apply(null, arguments) }, Uk = b._emscripten_bind_btSoftBodyWorldInfo_set_m_gravity_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_m_gravity_1.apply(null, arguments) }, Vk = b._emscripten_bind_LocalConvexResult_get_m_hitFraction_0 = function () { return b.asm._emscripten_bind_LocalConvexResult_get_m_hitFraction_0.apply(null, arguments) }, Wk = b._emscripten_bind_btHingeConstraint_setBreakingImpulseThreshold_1 = function () {
                    return b.asm._emscripten_bind_btHingeConstraint_setBreakingImpulseThreshold_1.apply(null,
                        arguments)
                }, Xk = b._emscripten_bind_btQuaternion_w_0 = function () { return b.asm._emscripten_bind_btQuaternion_w_0.apply(null, arguments) }, Yk = b._emscripten_bind_ConvexResultCallback_get_m_collisionFilterGroup_0 = function () { return b.asm._emscripten_bind_ConvexResultCallback_get_m_collisionFilterGroup_0.apply(null, arguments) }, Zk = b._emscripten_bind_btTransform_getRotation_0 = function () { return b.asm._emscripten_bind_btTransform_getRotation_0.apply(null, arguments) }, $k = b._emscripten_bind_Config_set_kSKHR_CL_1 = function () {
                    return b.asm._emscripten_bind_Config_set_kSKHR_CL_1.apply(null,
                        arguments)
                }, al = b._emscripten_bind_btHingeConstraint_btHingeConstraint_6 = function () { return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_6.apply(null, arguments) }, bl = b._emscripten_bind_btHingeConstraint_btHingeConstraint_7 = function () { return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_7.apply(null, arguments) }, cl = b._emscripten_bind_btCapsuleShapeZ_getMargin_0 = function () { return b.asm._emscripten_bind_btCapsuleShapeZ_getMargin_0.apply(null, arguments) }, dl = b._emscripten_bind_btHingeConstraint_btHingeConstraint_5 =
                    function () { return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_5.apply(null, arguments) }, el = b._emscripten_bind_btSoftBodyWorldInfo_get_m_maxDisplacement_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_m_maxDisplacement_0.apply(null, arguments) }, fl = b._emscripten_bind_btHingeConstraint_btHingeConstraint_3 = function () { return b.asm._emscripten_bind_btHingeConstraint_btHingeConstraint_3.apply(null, arguments) }, gl = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalAngularDampingThresholdSqr_1 =
                        function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalAngularDampingThresholdSqr_1.apply(null, arguments) }, hl = b._emscripten_bind_btSoftBody_setWorldTransform_1 = function () { return b.asm._emscripten_bind_btSoftBody_setWorldTransform_1.apply(null, arguments) }, il = b._emscripten_bind_btBoxShape_setMargin_1 = function () { return b.asm._emscripten_bind_btBoxShape_setMargin_1.apply(null, arguments) }, jl = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_maxSuspensionTravelCm_1 = function () {
                            return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_maxSuspensionTravelCm_1.apply(null,
                                arguments)
                        }, kl = b._emscripten_bind_ClosestConvexResultCallback_get_m_hitNormalWorld_0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_hitNormalWorld_0.apply(null, arguments) }, ll = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_chassisConnectionCS_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_chassisConnectionCS_0.apply(null, arguments) }, ml = b._emscripten_bind_btTypedConstraint___destroy___0 = function () {
                            return b.asm._emscripten_bind_btTypedConstraint___destroy___0.apply(null,
                                arguments)
                        }, nl = b._emscripten_bind_btCylinderShapeX_btCylinderShapeX_1 = function () { return b.asm._emscripten_bind_btCylinderShapeX_btCylinderShapeX_1.apply(null, arguments) }, ol = b._emscripten_bind_btGeneric6DofSpringConstraint_setAngularUpperLimit_1 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setAngularUpperLimit_1.apply(null, arguments) }, pl = b._emscripten_bind_btDiscreteDynamicsWorld_addRigidBody_3 = function () {
                            return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addRigidBody_3.apply(null,
                                arguments)
                        }, ql = b._emscripten_bind_btDiscreteDynamicsWorld_addRigidBody_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addRigidBody_1.apply(null, arguments) }, rl = b._emscripten_bind_Config_set_collisions_1 = function () { return b.asm._emscripten_bind_Config_set_collisions_1.apply(null, arguments) }, sl = b._emscripten_bind_btQuaternion_btQuaternion_4 = function () { return b.asm._emscripten_bind_btQuaternion_btQuaternion_4.apply(null, arguments) }, tl = b._emscripten_bind_btSoftRigidDynamicsWorld_getBroadphase_0 =
                            function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getBroadphase_0.apply(null, arguments) }, ul = b._emscripten_bind_btWheelInfo_set_m_rotation_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_rotation_1.apply(null, arguments) }, vl = b._emscripten_bind_btSphereShape_btSphereShape_1 = function () { return b.asm._emscripten_bind_btSphereShape_btSphereShape_1.apply(null, arguments) }, wl = b._emscripten_bind_btWheelInfo_get_m_wheelsSuspensionForce_0 = function () {
                                return b.asm._emscripten_bind_btWheelInfo_get_m_wheelsSuspensionForce_0.apply(null,
                                    arguments)
                            }, xl = b._emscripten_bind_btQuaternion_y_0 = function () { return b.asm._emscripten_bind_btQuaternion_y_0.apply(null, arguments) }, yl = b._emscripten_bind_btCollisionWorld_addCollisionObject_1 = function () { return b.asm._emscripten_bind_btCollisionWorld_addCollisionObject_1.apply(null, arguments) }, zl = b._emscripten_bind_btCollisionWorld_addCollisionObject_2 = function () { return b.asm._emscripten_bind_btCollisionWorld_addCollisionObject_2.apply(null, arguments) }, Al = b._emscripten_bind_btCompoundShape_setLocalScaling_1 =
                                function () { return b.asm._emscripten_bind_btCompoundShape_setLocalScaling_1.apply(null, arguments) }, Bl = b._emscripten_bind_ClosestConvexResultCallback_set_m_collisionFilterGroup_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_collisionFilterGroup_1.apply(null, arguments) }, Cl = b._emscripten_bind_btConeTwistConstraint_setBreakingImpulseThreshold_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_setBreakingImpulseThreshold_1.apply(null, arguments) }, Dl = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_chassisConnectionCS_1 =
                                    function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_chassisConnectionCS_1.apply(null, arguments) }, El = b._emscripten_bind_btSoftBodyHelpers_CreateEllipsoid_4 = function () { return b.asm._emscripten_bind_btSoftBodyHelpers_CreateEllipsoid_4.apply(null, arguments) }, Fl = b._emscripten_bind_RaycastInfo_get_m_isInContact_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_isInContact_0.apply(null, arguments) }, Gl = b._emscripten_bind_btWheelInfo_get_m_skidInfo_0 = function () {
                                        return b.asm._emscripten_bind_btWheelInfo_get_m_skidInfo_0.apply(null,
                                            arguments)
                                    }, Hl = b._emscripten_bind_btHeightfieldTerrainShape_setMargin_1 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape_setMargin_1.apply(null, arguments) }, Il = b._emscripten_bind_ClosestConvexResultCallback_get_m_collisionFilterGroup_0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_collisionFilterGroup_0.apply(null, arguments) }, Jl = b._emscripten_bind_btCapsuleShape_setMargin_1 = function () {
                                        return b.asm._emscripten_bind_btCapsuleShape_setMargin_1.apply(null,
                                            arguments)
                                    }, Kl = b._emscripten_bind_btDefaultVehicleRaycaster_btDefaultVehicleRaycaster_1 = function () { return b.asm._emscripten_bind_btDefaultVehicleRaycaster_btDefaultVehicleRaycaster_1.apply(null, arguments) }, Ll = b._emscripten_bind_btDynamicsWorld_contactTest_2 = function () { return b.asm._emscripten_bind_btDynamicsWorld_contactTest_2.apply(null, arguments) }, Ml = b._emscripten_bind_btCollisionObject_setUserPointer_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setUserPointer_1.apply(null, arguments) },
        Nl = b._emscripten_bind_btSequentialImpulseConstraintSolver_btSequentialImpulseConstraintSolver_0 = function () { return b.asm._emscripten_bind_btSequentialImpulseConstraintSolver_btSequentialImpulseConstraintSolver_0.apply(null, arguments) }, Ol = b._emscripten_bind_btActionInterface___destroy___0 = function () { return b.asm._emscripten_bind_btActionInterface___destroy___0.apply(null, arguments) }, Pl = b._emscripten_bind_btSoftBody_generateClusters_2 = function () {
            return b.asm._emscripten_bind_btSoftBody_generateClusters_2.apply(null,
                arguments)
        }, Ql = b._emscripten_bind_btDefaultMotionState_setWorldTransform_1 = function () { return b.asm._emscripten_bind_btDefaultMotionState_setWorldTransform_1.apply(null, arguments) }, Rl = b._emscripten_bind_btSoftBody_generateClusters_1 = function () { return b.asm._emscripten_bind_btSoftBody_generateClusters_1.apply(null, arguments) }, Sl = b._emscripten_bind_RayResultCallback_get_m_collisionObject_0 = function () { return b.asm._emscripten_bind_RayResultCallback_get_m_collisionObject_0.apply(null, arguments) }, Tl = b._emscripten_bind_btPoint2PointConstraint_getPivotInA_0 =
            function () { return b.asm._emscripten_bind_btPoint2PointConstraint_getPivotInA_0.apply(null, arguments) }, Ul = b._emscripten_bind_Config_get_kAHR_0 = function () { return b.asm._emscripten_bind_Config_get_kAHR_0.apply(null, arguments) }, Vl = b._emscripten_bind_btGeneric6DofSpringConstraint_setStiffness_2 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setStiffness_2.apply(null, arguments) }, Wl = b._emscripten_bind_btCylinderShape_calculateLocalInertia_2 = function () {
                return b.asm._emscripten_bind_btCylinderShape_calculateLocalInertia_2.apply(null,
                    arguments)
            }, Xl = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelRadius_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelRadius_1.apply(null, arguments) }, Yl = b._emscripten_bind_ClosestConvexResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback___destroy___0.apply(null, arguments) }, Zl = b._emscripten_bind_btQuaternion_normalized_0 = function () { return b.asm._emscripten_bind_btQuaternion_normalized_0.apply(null, arguments) },
        $l = b._emscripten_bind_btDynamicsWorld_addCollisionObject_1 = function () { return b.asm._emscripten_bind_btDynamicsWorld_addCollisionObject_1.apply(null, arguments) }, am = b._emscripten_bind_ClosestConvexResultCallback_get_m_collisionFilterMask_0 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_collisionFilterMask_0.apply(null, arguments) }; b.___cxa_can_catch = function () { return b.asm.___cxa_can_catch.apply(null, arguments) };
    var bm = b._emscripten_bind_btDynamicsWorld_addCollisionObject_2 = function () { return b.asm._emscripten_bind_btDynamicsWorld_addCollisionObject_2.apply(null, arguments) }, cm = b._emscripten_bind_btDiscreteDynamicsWorld_getDispatcher_0 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getDispatcher_0.apply(null, arguments) }, dm = b._emscripten_bind_btCollisionObject_setFriction_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setFriction_1.apply(null, arguments) }, em = b._emscripten_bind_btGeneric6DofSpringConstraint_enableFeedback_1 =
        function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_enableFeedback_1.apply(null, arguments) }, fm = b._emscripten_bind_btVector3_rotate_2 = function () { return b.asm._emscripten_bind_btVector3_rotate_2.apply(null, arguments) }, gm = b._emscripten_bind_btHeightfieldTerrainShape___destroy___0 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape___destroy___0.apply(null, arguments) }, hm = b._emscripten_bind_btWheelInfo_get_m_maxSuspensionTravelCm_0 = function () {
            return b.asm._emscripten_bind_btWheelInfo_get_m_maxSuspensionTravelCm_0.apply(null,
                arguments)
        }, im = b._emscripten_bind_Config_get_kVC_0 = function () { return b.asm._emscripten_bind_Config_get_kVC_0.apply(null, arguments) }, jm = b._emscripten_bind_btVehicleRaycasterResult_set_m_hitPointInWorld_1 = function () { return b.asm._emscripten_bind_btVehicleRaycasterResult_set_m_hitPointInWorld_1.apply(null, arguments) }, km = b._emscripten_bind_btQuaternion_op_mulq_1 = function () { return b.asm._emscripten_bind_btQuaternion_op_mulq_1.apply(null, arguments) }, lm = b._emscripten_bind_btPairCachingGhostObject_setActivationState_1 =
            function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setActivationState_1.apply(null, arguments) }, mm = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelDirectionCS_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelDirectionCS_1.apply(null, arguments) }, nm = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelAxleCS_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelAxleCS_1.apply(null, arguments) }, om = b._emscripten_bind_Material_get_m_kVST_0 =
                function () { return b.asm._emscripten_bind_Material_get_m_kVST_0.apply(null, arguments) }, pm = b._emscripten_bind_Config_set_kVCF_1 = function () { return b.asm._emscripten_bind_Config_set_kVCF_1.apply(null, arguments) }, qm = b._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_3.apply(null, arguments) }, rm = b._emscripten_bind_btGhostObject_getUserIndex_0 = function () {
                    return b.asm._emscripten_bind_btGhostObject_getUserIndex_0.apply(null,
                        arguments)
                }, sm = b._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_1.apply(null, arguments) }, tm = b._emscripten_bind_btWheelInfo_set_m_deltaRotation_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_deltaRotation_1.apply(null, arguments) }, um = b._emscripten_bind_btVector3___destroy___0 = function () { return b.asm._emscripten_bind_btVector3___destroy___0.apply(null, arguments) }, wm = b._emscripten_bind_RaycastInfo___destroy___0 =
                    function () { return b.asm._emscripten_bind_RaycastInfo___destroy___0.apply(null, arguments) }, xm = b._emscripten_bind_btRigidBody_setAngularFactor_1 = function () { return b.asm._emscripten_bind_btRigidBody_setAngularFactor_1.apply(null, arguments) }, ym = b._emscripten_bind_btCylinderShapeZ_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btCylinderShapeZ_calculateLocalInertia_2.apply(null, arguments) }, zm = b._emscripten_bind_btConeShapeZ_btConeShapeZ_2 = function () {
                        return b.asm._emscripten_bind_btConeShapeZ_btConeShapeZ_2.apply(null,
                            arguments)
                    }, Am = b._emscripten_bind_LocalShapeInfo_set_m_triangleIndex_1 = function () { return b.asm._emscripten_bind_LocalShapeInfo_set_m_triangleIndex_1.apply(null, arguments) }, Bm = b._emscripten_bind_btMotionState_getWorldTransform_1 = function () { return b.asm._emscripten_bind_btMotionState_getWorldTransform_1.apply(null, arguments) }, Cm = b._emscripten_bind_btDynamicsWorld_getSolverInfo_0 = function () { return b.asm._emscripten_bind_btDynamicsWorld_getSolverInfo_0.apply(null, arguments) }, Dm = b._emscripten_bind_btVehicleRaycasterResult_set_m_hitNormalInWorld_1 =
                        function () { return b.asm._emscripten_bind_btVehicleRaycasterResult_set_m_hitNormalInWorld_1.apply(null, arguments) }, Em = b._emscripten_bind_btSoftRigidDynamicsWorld_convexSweepTest_5 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_convexSweepTest_5.apply(null, arguments) }, Fm = b._emscripten_bind_Config_get_kMT_0 = function () { return b.asm._emscripten_bind_Config_get_kMT_0.apply(null, arguments) }, Gm = b._emscripten_bind_btDynamicsWorld_getBroadphase_0 = function () {
                            return b.asm._emscripten_bind_btDynamicsWorld_getBroadphase_0.apply(null,
                                arguments)
                        }, Hm = b._emscripten_bind_btSphereShape_getMargin_0 = function () { return b.asm._emscripten_bind_btSphereShape_getMargin_0.apply(null, arguments) }, Im = b._emscripten_bind_Config_get_timescale_0 = function () { return b.asm._emscripten_bind_Config_get_timescale_0.apply(null, arguments) }, Jm = b._emscripten_bind_btVector3_x_0 = function () { return b.asm._emscripten_bind_btVector3_x_0.apply(null, arguments) }; b.___cxa_is_pointer_type = function () { return b.asm.___cxa_is_pointer_type.apply(null, arguments) };
    var Km = b._emscripten_bind_btConvexTriangleMeshShape___destroy___0 = function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape___destroy___0.apply(null, arguments) }, Lm = b._emscripten_bind_btCollisionObject_getCollisionShape_0 = function () { return b.asm._emscripten_bind_btCollisionObject_getCollisionShape_0.apply(null, arguments) }, Mm = b._emscripten_bind_btRigidBodyConstructionInfo_btRigidBodyConstructionInfo_4 = function () {
        return b.asm._emscripten_bind_btRigidBodyConstructionInfo_btRigidBodyConstructionInfo_4.apply(null,
            arguments)
    }, Nm = b._emscripten_bind_btManifoldPoint___destroy___0 = function () { return b.asm._emscripten_bind_btManifoldPoint___destroy___0.apply(null, arguments) }, Om = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_rollingFriction_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_rollingFriction_1.apply(null, arguments) }, Pm = b._emscripten_bind_btVector4_length_0 = function () { return b.asm._emscripten_bind_btVector4_length_0.apply(null, arguments) }, Qm = b._emscripten_bind_btGhostObject_setUserIndex_1 =
        function () { return b.asm._emscripten_bind_btGhostObject_setUserIndex_1.apply(null, arguments) }, Rm = b._emscripten_bind_btWheelInfo_getSuspensionRestLength_0 = function () { return b.asm._emscripten_bind_btWheelInfo_getSuspensionRestLength_0.apply(null, arguments) }, Sm = b._emscripten_bind_btDefaultMotionState_set_m_graphicsWorldTrans_1 = function () { return b.asm._emscripten_bind_btDefaultMotionState_set_m_graphicsWorldTrans_1.apply(null, arguments) }, Tm = b._emscripten_bind_btGhostObject_setRestitution_1 = function () {
            return b.asm._emscripten_bind_btGhostObject_setRestitution_1.apply(null,
                arguments)
        }, Um = b._emscripten_bind_btConeTwistConstraint_setAngularOnly_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_setAngularOnly_1.apply(null, arguments) }, Vm = b._emscripten_bind_btQuadWord_setZ_1 = function () { return b.asm._emscripten_bind_btQuadWord_setZ_1.apply(null, arguments) }, Wm = b._emscripten_bind_btDefaultCollisionConfiguration___destroy___0 = function () { return b.asm._emscripten_bind_btDefaultCollisionConfiguration___destroy___0.apply(null, arguments) }, Xm = b._emscripten_bind_btRigidBody_setMassProps_2 =
            function () { return b.asm._emscripten_bind_btRigidBody_setMassProps_2.apply(null, arguments) }; b.getTempRet0 = function () { return b.asm.getTempRet0.apply(null, arguments) };
    var Ym = b._emscripten_bind_btVector3_setValue_3 = function () { return b.asm._emscripten_bind_btVector3_setValue_3.apply(null, arguments) }, Zm = b._emscripten_bind_btPairCachingGhostObject_setCcdMotionThreshold_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setCcdMotionThreshold_1.apply(null, arguments) }, $m = b._emscripten_bind_RaycastInfo_get_m_suspensionLength_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_suspensionLength_0.apply(null, arguments) }, an = b._emscripten_bind_btGhostObject_getCollisionFlags_0 =
        function () { return b.asm._emscripten_bind_btGhostObject_getCollisionFlags_0.apply(null, arguments) }, bn = b._emscripten_bind_btCapsuleShapeX___destroy___0 = function () { return b.asm._emscripten_bind_btCapsuleShapeX___destroy___0.apply(null, arguments) }, cn = b._emscripten_bind_btWheelInfo_get_m_wheelDirectionCS_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_wheelDirectionCS_0.apply(null, arguments) }, dn = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelsDampingCompression_1 = function () {
            return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_wheelsDampingCompression_1.apply(null,
                arguments)
        }, en = b._emscripten_bind_Material_get_m_flags_0 = function () { return b.asm._emscripten_bind_Material_get_m_flags_0.apply(null, arguments) }, fn = b._emscripten_bind_btQuaternion_getAxis_0 = function () { return b.asm._emscripten_bind_btQuaternion_getAxis_0.apply(null, arguments) }, gn = b._emscripten_bind_btRaycastVehicle_getUserConstraintId_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getUserConstraintId_0.apply(null, arguments) }, hn = b._emscripten_bind_btRaycastVehicle_updateAction_2 = function () {
            return b.asm._emscripten_bind_btRaycastVehicle_updateAction_2.apply(null,
                arguments)
        }, jn = b._emscripten_bind_btHingeConstraint_setLimit_4 = function () { return b.asm._emscripten_bind_btHingeConstraint_setLimit_4.apply(null, arguments) }, kn = b._emscripten_bind_btHingeConstraint_setLimit_5 = function () { return b.asm._emscripten_bind_btHingeConstraint_setLimit_5.apply(null, arguments) }, ln = b._emscripten_bind_btSoftBodyWorldInfo_btSoftBodyWorldInfo_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_btSoftBodyWorldInfo_0.apply(null, arguments) }, mn = b._emscripten_bind_Config_set_kDG_1 =
            function () { return b.asm._emscripten_bind_Config_set_kDG_1.apply(null, arguments) }, nn = b._emscripten_bind_btWheelInfo_set_m_maxSuspensionTravelCm_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_maxSuspensionTravelCm_1.apply(null, arguments) }, on = b._emscripten_bind_btWheelInfo_set_m_wheelsSuspensionForce_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_wheelsSuspensionForce_1.apply(null, arguments) }, pn = b._emscripten_bind_btSoftBody_scale_1 = function () {
                return b.asm._emscripten_bind_btSoftBody_scale_1.apply(null,
                    arguments)
            }, qn = b._emscripten_bind_Config_get_citerations_0 = function () { return b.asm._emscripten_bind_Config_get_citerations_0.apply(null, arguments) }, rn = b._emscripten_bind_btTypedConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btTypedConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, sn = b._emscripten_bind_btGhostObject_getCollisionShape_0 = function () { return b.asm._emscripten_bind_btGhostObject_getCollisionShape_0.apply(null, arguments) }, tn = b._emscripten_bind_btCollisionObject_setAnisotropicFriction_2 =
                function () { return b.asm._emscripten_bind_btCollisionObject_setAnisotropicFriction_2.apply(null, arguments) }, un = b._emscripten_bind_btBoxShape___destroy___0 = function () { return b.asm._emscripten_bind_btBoxShape___destroy___0.apply(null, arguments) }, vn = b._emscripten_bind_btWheelInfo_get_m_bIsFrontWheel_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_bIsFrontWheel_0.apply(null, arguments) }, wn = b._emscripten_bind_btPersistentManifold_getContactPoint_1 = function () {
                    return b.asm._emscripten_bind_btPersistentManifold_getContactPoint_1.apply(null,
                        arguments)
                }, xn = b._emscripten_bind_btGeneric6DofSpringConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, yn = b._emscripten_bind_ConvexResultCallback_set_m_collisionFilterGroup_1 = function () { return b.asm._emscripten_bind_ConvexResultCallback_set_m_collisionFilterGroup_1.apply(null, arguments) }, zn = b._emscripten_bind_RaycastInfo_set_m_groundObject_1 = function () {
                    return b.asm._emscripten_bind_RaycastInfo_set_m_groundObject_1.apply(null,
                        arguments)
                }, An = b._emscripten_bind_btGhostObject_activate_1 = function () { return b.asm._emscripten_bind_btGhostObject_activate_1.apply(null, arguments) }, Bn = b._emscripten_bind_btRaycastVehicle_getForwardAxis_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getForwardAxis_0.apply(null, arguments) }, Cn = b._emscripten_bind_btManifoldPoint_getPositionWorldOnB_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_getPositionWorldOnB_0.apply(null, arguments) }, Dn = b._emscripten_bind_btManifoldPoint_get_m_positionWorldOnA_0 =
                    function () { return b.asm._emscripten_bind_btManifoldPoint_get_m_positionWorldOnA_0.apply(null, arguments) }, En = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalDamping_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_additionalDamping_1.apply(null, arguments) }, Fn = b._emscripten_bind_btDefaultSoftBodySolver_btDefaultSoftBodySolver_0 = function () { return b.asm._emscripten_bind_btDefaultSoftBodySolver_btDefaultSoftBodySolver_0.apply(null, arguments) }, Gn = b._emscripten_bind_btSphereShape_setMargin_1 =
                        function () { return b.asm._emscripten_bind_btSphereShape_setMargin_1.apply(null, arguments) }, Hn = b._emscripten_bind_btSoftBody_get_m_cfg_0 = function () { return b.asm._emscripten_bind_btSoftBody_get_m_cfg_0.apply(null, arguments) }, In = b._emscripten_bind_btCollisionObject_setUserIndex_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setUserIndex_1.apply(null, arguments) }, Jn = b._emscripten_bind_btContactSolverInfo_set_m_splitImpulsePenetrationThreshold_1 = function () {
                            return b.asm._emscripten_bind_btContactSolverInfo_set_m_splitImpulsePenetrationThreshold_1.apply(null,
                                arguments)
                        }, Kn = b._emscripten_bind_btSliderConstraint_setUpperAngLimit_1 = function () { return b.asm._emscripten_bind_btSliderConstraint_setUpperAngLimit_1.apply(null, arguments) }, Ln = b._emscripten_bind_btDynamicsWorld_contactPairTest_3 = function () { return b.asm._emscripten_bind_btDynamicsWorld_contactPairTest_3.apply(null, arguments) }, Mn = b._emscripten_bind_btCollisionWorld_getPairCache_0 = function () { return b.asm._emscripten_bind_btCollisionWorld_getPairCache_0.apply(null, arguments) }, Nn = b._emscripten_bind_btConeTwistConstraint_setMotorTarget_1 =
                            function () { return b.asm._emscripten_bind_btConeTwistConstraint_setMotorTarget_1.apply(null, arguments) }, On = b._emscripten_bind_ClosestConvexResultCallback_set_m_convexFromWorld_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_convexFromWorld_1.apply(null, arguments) }, Pn = b._emscripten_bind_btWheelInfo_set_m_rollInfluence_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_rollInfluence_1.apply(null, arguments) }, Qn = b._emscripten_bind_btGhostObject_setCcdMotionThreshold_1 =
                                function () { return b.asm._emscripten_bind_btGhostObject_setCcdMotionThreshold_1.apply(null, arguments) }, Rn = b._emscripten_bind_btGeneric6DofConstraint_setBreakingImpulseThreshold_1 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_setBreakingImpulseThreshold_1.apply(null, arguments) }, Sn = b._emscripten_enum_PHY_ScalarType_PHY_INTEGER = function () { return b.asm._emscripten_enum_PHY_ScalarType_PHY_INTEGER.apply(null, arguments) }, Tn = b._emscripten_bind_btSoftBodyHelpers_CreatePatchUV_10 = function () {
                                    return b.asm._emscripten_bind_btSoftBodyHelpers_CreatePatchUV_10.apply(null,
                                        arguments)
                                }, Un = b._emscripten_bind_btGhostObject_forceActivationState_1 = function () { return b.asm._emscripten_bind_btGhostObject_forceActivationState_1.apply(null, arguments) }, Vn = b._emscripten_bind_btGhostPairCallback_btGhostPairCallback_0 = function () { return b.asm._emscripten_bind_btGhostPairCallback_btGhostPairCallback_0.apply(null, arguments) }, Wn = b._emscripten_bind_btSoftBodyHelpers_CreateFromTriMesh_5 = function () { return b.asm._emscripten_bind_btSoftBodyHelpers_CreateFromTriMesh_5.apply(null, arguments) },
        Xn = b._emscripten_bind_btVector4_y_0 = function () { return b.asm._emscripten_bind_btVector4_y_0.apply(null, arguments) }, Yn = b._emscripten_bind_VoidPtr___destroy___0 = function () { return b.asm._emscripten_bind_VoidPtr___destroy___0.apply(null, arguments) }; b.establishStackSpace = function () { return b.asm.establishStackSpace.apply(null, arguments) };
    var Zn = b._emscripten_bind_RaycastInfo_set_m_contactNormalWS_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_contactNormalWS_1.apply(null, arguments) }, $n = b._emscripten_bind_btSliderConstraint_setLowerAngLimit_1 = function () { return b.asm._emscripten_bind_btSliderConstraint_setLowerAngLimit_1.apply(null, arguments) }, ao = b._emscripten_bind_ClosestRayResultCallback_get_m_collisionObject_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_collisionObject_0.apply(null, arguments) },
        bo = b._emscripten_bind_RaycastInfo_set_m_contactPointWS_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_contactPointWS_1.apply(null, arguments) }, co = b._emscripten_bind_ClosestConvexResultCallback_ClosestConvexResultCallback_2 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_ClosestConvexResultCallback_2.apply(null, arguments) }, eo = b._emscripten_bind_ClosestRayResultCallback_get_m_rayFromWorld_0 = function () {
            return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_rayFromWorld_0.apply(null,
                arguments)
        }, fo = b._emscripten_bind_btSoftBody_setContactProcessingThreshold_1 = function () { return b.asm._emscripten_bind_btSoftBody_setContactProcessingThreshold_1.apply(null, arguments) }, go = b._emscripten_bind_btPairCachingGhostObject_getNumOverlappingObjects_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getNumOverlappingObjects_0.apply(null, arguments) }, ho = b._emscripten_bind_btSliderConstraint_enableFeedback_1 = function () {
            return b.asm._emscripten_bind_btSliderConstraint_enableFeedback_1.apply(null,
                arguments)
        }, io = b._emscripten_bind_RayResultCallback_get_m_collisionFilterGroup_0 = function () { return b.asm._emscripten_bind_RayResultCallback_get_m_collisionFilterGroup_0.apply(null, arguments) }, jo = b._emscripten_enum_PHY_ScalarType_PHY_DOUBLE = function () { return b.asm._emscripten_enum_PHY_ScalarType_PHY_DOUBLE.apply(null, arguments) }, ko = b._emscripten_bind_btConstraintSetting_get_m_tau_0 = function () { return b.asm._emscripten_bind_btConstraintSetting_get_m_tau_0.apply(null, arguments) }, lo = b._emscripten_bind_btConeShape_setLocalScaling_1 =
            function () { return b.asm._emscripten_bind_btConeShape_setLocalScaling_1.apply(null, arguments) }, mo = b._emscripten_bind_btCollisionObject_setCollisionShape_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setCollisionShape_1.apply(null, arguments) }, no = b._emscripten_bind_btCollisionShape___destroy___0 = function () { return b.asm._emscripten_bind_btCollisionShape___destroy___0.apply(null, arguments) }, oo = b._emscripten_bind_btMatrix3x3_getRow_1 = function () {
                return b.asm._emscripten_bind_btMatrix3x3_getRow_1.apply(null,
                    arguments)
            }, po = b._emscripten_bind_ConvexResultCallback_get_m_closestHitFraction_0 = function () { return b.asm._emscripten_bind_ConvexResultCallback_get_m_closestHitFraction_0.apply(null, arguments) }, qo = b._emscripten_bind_btSoftRigidDynamicsWorld_getPairCache_0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getPairCache_0.apply(null, arguments) }, ro = b._emscripten_bind_btDispatcherInfo_get_m_dispatchFunc_0 = function () {
                return b.asm._emscripten_bind_btDispatcherInfo_get_m_dispatchFunc_0.apply(null,
                    arguments)
            }, so = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_rollingFriction_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_rollingFriction_0.apply(null, arguments) }, to = b._emscripten_bind_btSoftBody_getUserIndex_0 = function () { return b.asm._emscripten_bind_btSoftBody_getUserIndex_0.apply(null, arguments) }, uo = b._emscripten_bind_btPairCachingGhostObject_setCollisionShape_1 = function () {
                return b.asm._emscripten_bind_btPairCachingGhostObject_setCollisionShape_1.apply(null,
                    arguments)
            }, vo = b._emscripten_bind_btKinematicCharacterController_warp_1 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_warp_1.apply(null, arguments) }, wo = b._emscripten_bind_btContactSolverInfo___destroy___0 = function () { return b.asm._emscripten_bind_btContactSolverInfo___destroy___0.apply(null, arguments) }, xo = b._emscripten_bind_btSoftBody_getWorldTransform_0 = function () { return b.asm._emscripten_bind_btSoftBody_getWorldTransform_0.apply(null, arguments) }, yo = b._emscripten_bind_btTriangleMesh___destroy___0 =
                function () { return b.asm._emscripten_bind_btTriangleMesh___destroy___0.apply(null, arguments) }, zo = b._emscripten_bind_btKinematicCharacterController_preStep_1 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_preStep_1.apply(null, arguments) }, Ao = b._emscripten_bind_btRaycastVehicle_applyEngineForce_2 = function () { return b.asm._emscripten_bind_btRaycastVehicle_applyEngineForce_2.apply(null, arguments) }, Bo = b._emscripten_bind_btBoxShape_calculateLocalInertia_2 = function () {
                    return b.asm._emscripten_bind_btBoxShape_calculateLocalInertia_2.apply(null,
                        arguments)
                }, Co = b._emscripten_bind_btRaycastVehicle_setBrake_2 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setBrake_2.apply(null, arguments) }, Do = b._emscripten_bind_ConcreteContactResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_ConcreteContactResultCallback___destroy___0.apply(null, arguments) }, Eo = b._emscripten_bind_RaycastInfo_set_m_wheelAxleWS_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_wheelAxleWS_1.apply(null, arguments) }, Fo = b._emscripten_bind_btRaycastVehicle_updateVehicle_1 =
                    function () { return b.asm._emscripten_bind_btRaycastVehicle_updateVehicle_1.apply(null, arguments) }, Go = b._emscripten_bind_btCollisionObject___destroy___0 = function () { return b.asm._emscripten_bind_btCollisionObject___destroy___0.apply(null, arguments) }, Ho = b._emscripten_bind_btVehicleTuning_set_m_suspensionDamping_1 = function () { return b.asm._emscripten_bind_btVehicleTuning_set_m_suspensionDamping_1.apply(null, arguments) }, Io = b._emscripten_bind_btConvexTriangleMeshShape_setMargin_1 = function () {
                        return b.asm._emscripten_bind_btConvexTriangleMeshShape_setMargin_1.apply(null,
                            arguments)
                    }, Jo = b._emscripten_bind_btTriangleMeshShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btTriangleMeshShape_setLocalScaling_1.apply(null, arguments) }, Ko = b._emscripten_bind_Config_get_kSSHR_CL_0 = function () { return b.asm._emscripten_bind_Config_get_kSSHR_CL_0.apply(null, arguments) }, Lo = b._emscripten_bind_btConeTwistConstraint_setMotorTargetInConstraintSpace_1 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_setMotorTargetInConstraintSpace_1.apply(null, arguments) },
        Mo = b._emscripten_bind_btQuaternion_op_mul_1 = function () { return b.asm._emscripten_bind_btQuaternion_op_mul_1.apply(null, arguments) }, No = b._emscripten_bind_btDispatcherInfo_set_m_timeStep_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_timeStep_1.apply(null, arguments) }, Oo = b._emscripten_bind_btVector3_btVector3_3 = function () { return b.asm._emscripten_bind_btVector3_btVector3_3.apply(null, arguments) }, Po = b._emscripten_bind_btVector3_btVector3_0 = function () {
            return b.asm._emscripten_bind_btVector3_btVector3_0.apply(null,
                arguments)
        }, Qo = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_friction_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_friction_1.apply(null, arguments) }, Ro = b._emscripten_bind_btDiscreteDynamicsWorld_getGravity_0 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getGravity_0.apply(null, arguments) }, So = b._emscripten_bind_btVector3_z_0 = function () { return b.asm._emscripten_bind_btVector3_z_0.apply(null, arguments) }, To = b._emscripten_bind_ClosestConvexResultCallback_get_m_hitPointWorld_0 =
            function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_hitPointWorld_0.apply(null, arguments) }, Uo = b._emscripten_bind_btCollisionShape_getMargin_0 = function () { return b.asm._emscripten_bind_btCollisionShape_getMargin_0.apply(null, arguments) }, Vo = b._emscripten_bind_btSoftBodyWorldInfo_set_water_offset_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_water_offset_1.apply(null, arguments) }, Wo = b._emscripten_bind_btBroadphaseInterface___destroy___0 = function () {
                return b.asm._emscripten_bind_btBroadphaseInterface___destroy___0.apply(null,
                    arguments)
            }, Xo = b._emscripten_bind_btWheelInfo_updateWheel_2 = function () { return b.asm._emscripten_bind_btWheelInfo_updateWheel_2.apply(null, arguments) }, Yo = b._emscripten_bind_ConcreteContactResultCallback_addSingleResult_7 = function () { return b.asm._emscripten_bind_ConcreteContactResultCallback_addSingleResult_7.apply(null, arguments) }, Zo = b._emscripten_bind_RaycastInfo_get_m_hardPointWS_0 = function () { return b.asm._emscripten_bind_RaycastInfo_get_m_hardPointWS_0.apply(null, arguments) }, $o = b._emscripten_bind_btConeTwistConstraint___destroy___0 =
                function () { return b.asm._emscripten_bind_btConeTwistConstraint___destroy___0.apply(null, arguments) }, ap = b._emscripten_bind_btQuadWord___destroy___0 = function () { return b.asm._emscripten_bind_btQuadWord___destroy___0.apply(null, arguments) }, bp = b._emscripten_bind_btSoftRigidDynamicsWorld_contactPairTest_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_contactPairTest_3.apply(null, arguments) }, cp = b._emscripten_bind_btQuaternion_setEulerZYX_3 = function () {
                    return b.asm._emscripten_bind_btQuaternion_setEulerZYX_3.apply(null,
                        arguments)
                }, dp = b._emscripten_bind_ClosestRayResultCallback_set_m_rayFromWorld_1 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_rayFromWorld_1.apply(null, arguments) }, ep = b._emscripten_bind_btGeneric6DofSpringConstraint_setDamping_2 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setDamping_2.apply(null, arguments) }, fp = b._emscripten_bind_RaycastInfo_get_m_wheelDirectionWS_0 = function () {
                    return b.asm._emscripten_bind_RaycastInfo_get_m_wheelDirectionWS_0.apply(null,
                        arguments)
                }, gp = b._emscripten_bind_btRigidBody_setCenterOfMassTransform_1 = function () { return b.asm._emscripten_bind_btRigidBody_setCenterOfMassTransform_1.apply(null, arguments) }, hp = b._emscripten_bind_btSoftBody_setUserIndex_1 = function () { return b.asm._emscripten_bind_btSoftBody_setUserIndex_1.apply(null, arguments) }, ip = b._emscripten_bind_btWheelInfo_get_m_chassisConnectionPointCS_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_chassisConnectionPointCS_0.apply(null, arguments) }, jp = b._emscripten_bind_btSoftBody_setCollisionShape_1 =
                    function () { return b.asm._emscripten_bind_btSoftBody_setCollisionShape_1.apply(null, arguments) }, kp = b._emscripten_bind_btGhostObject_setAnisotropicFriction_2 = function () { return b.asm._emscripten_bind_btGhostObject_setAnisotropicFriction_2.apply(null, arguments) }, lp = b._emscripten_bind_btConstraintSolver___destroy___0 = function () { return b.asm._emscripten_bind_btConstraintSolver___destroy___0.apply(null, arguments) }, mp = b._emscripten_bind_btSoftBody_isActive_0 = function () {
                        return b.asm._emscripten_bind_btSoftBody_isActive_0.apply(null,
                            arguments)
                    }, np = b._emscripten_bind_btCapsuleShape_btCapsuleShape_2 = function () { return b.asm._emscripten_bind_btCapsuleShape_btCapsuleShape_2.apply(null, arguments) }, op = b._emscripten_bind_btTypedConstraint_enableFeedback_1 = function () { return b.asm._emscripten_bind_btTypedConstraint_enableFeedback_1.apply(null, arguments) }, pp = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_frictionSlip_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_frictionSlip_0.apply(null, arguments) },
        qp = b._emscripten_bind_btGhostObject_activate_0 = function () { return b.asm._emscripten_bind_btGhostObject_activate_0.apply(null, arguments) }, rp = b._emscripten_bind_btConstraintSetting_btConstraintSetting_0 = function () { return b.asm._emscripten_bind_btConstraintSetting_btConstraintSetting_0.apply(null, arguments) }, sp = b._emscripten_bind_btWheelInfo_set_m_clippedInvContactDotSuspension_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_clippedInvContactDotSuspension_1.apply(null, arguments) }, tp = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalDampingFactor_0 =
            function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalDampingFactor_0.apply(null, arguments) }, up = b._emscripten_bind_btRigidBody_setAnisotropicFriction_2 = function () { return b.asm._emscripten_bind_btRigidBody_setAnisotropicFriction_2.apply(null, arguments) }, vp = b._emscripten_bind_btSoftBody_btSoftBody_4 = function () { return b.asm._emscripten_bind_btSoftBody_btSoftBody_4.apply(null, arguments) }, wp = b._emscripten_bind_btSoftBody_activate_0 = function () {
                return b.asm._emscripten_bind_btSoftBody_activate_0.apply(null,
                    arguments)
            }, xp = b._emscripten_bind_btRigidBodyConstructionInfo_btRigidBodyConstructionInfo_3 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_btRigidBodyConstructionInfo_3.apply(null, arguments) }, yp = b._emscripten_bind_ConvexResultCallback_set_m_closestHitFraction_1 = function () { return b.asm._emscripten_bind_ConvexResultCallback_set_m_closestHitFraction_1.apply(null, arguments) }, zp = b._emscripten_bind_btGeneric6DofSpringConstraint_enableSpring_2 = function () {
                return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_enableSpring_2.apply(null,
                    arguments)
            }, Ap = b._emscripten_bind_btPersistentManifold_btPersistentManifold_0 = function () { return b.asm._emscripten_bind_btPersistentManifold_btPersistentManifold_0.apply(null, arguments) }, Bp = b._emscripten_bind_ConvexResultCallback_get_m_collisionFilterMask_0 = function () { return b.asm._emscripten_bind_ConvexResultCallback_get_m_collisionFilterMask_0.apply(null, arguments) }, Cp = b._emscripten_bind_ClosestRayResultCallback_ClosestRayResultCallback_2 = function () {
                return b.asm._emscripten_bind_ClosestRayResultCallback_ClosestRayResultCallback_2.apply(null,
                    arguments)
            }, Dp = b._emscripten_bind_btVector4___destroy___0 = function () { return b.asm._emscripten_bind_btVector4___destroy___0.apply(null, arguments) }, Ep = b._emscripten_bind_btPairCachingGhostObject_isKinematicObject_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_isKinematicObject_0.apply(null, arguments) }, Fp = b._emscripten_bind_ClosestRayResultCallback_set_m_collisionFilterMask_1 = function () {
                return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_collisionFilterMask_1.apply(null,
                    arguments)
            }, Gp = b._emscripten_bind_tNodeArray_at_1 = function () { return b.asm._emscripten_bind_tNodeArray_at_1.apply(null, arguments) }, Hp = b._emscripten_bind_btStaticPlaneShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btStaticPlaneShape_calculateLocalInertia_2.apply(null, arguments) }, Ip = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalAngularDampingThresholdSqr_0 = function () {
                return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_additionalAngularDampingThresholdSqr_0.apply(null,
                    arguments)
            }, Jp = b._emscripten_bind_btCollisionObject_setCcdMotionThreshold_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setCcdMotionThreshold_1.apply(null, arguments) }, Kp = b._emscripten_bind_btKinematicCharacterController_btKinematicCharacterController_4 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_btKinematicCharacterController_4.apply(null, arguments) }, Lp = b._emscripten_bind_btSoftBody_set_m_cfg_1 = function () {
                return b.asm._emscripten_bind_btSoftBody_set_m_cfg_1.apply(null,
                    arguments)
            }, Mp = b._emscripten_bind_btWheelInfo_get_m_brake_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_brake_0.apply(null, arguments) }, Np = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_angularSleepingThreshold_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_angularSleepingThreshold_0.apply(null, arguments) }, Op = b._emscripten_bind_btWheelInfo_get_m_deltaRotation_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_deltaRotation_0.apply(null, arguments) },
        Pp = b._emscripten_bind_btPoint2PointConstraint_getPivotInB_0 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_getPivotInB_0.apply(null, arguments) }, Qp = b._emscripten_bind_btKinematicCharacterController_playerStep_2 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_playerStep_2.apply(null, arguments) }, Rp = b._emscripten_bind_btDispatcherInfo___destroy___0 = function () { return b.asm._emscripten_bind_btDispatcherInfo___destroy___0.apply(null, arguments) }, Sp = b._emscripten_bind_btCapsuleShape_getMargin_0 =
            function () { return b.asm._emscripten_bind_btCapsuleShape_getMargin_0.apply(null, arguments) }, Tp = b._emscripten_bind_btCylinderShape_getMargin_0 = function () { return b.asm._emscripten_bind_btCylinderShape_getMargin_0.apply(null, arguments) }, Up = b._emscripten_bind_btSoftBodyArray_size_0 = function () { return b.asm._emscripten_bind_btSoftBodyArray_size_0.apply(null, arguments) }, Vp = b._emscripten_bind_btStaticPlaneShape_setLocalScaling_1 = function () {
                return b.asm._emscripten_bind_btStaticPlaneShape_setLocalScaling_1.apply(null,
                    arguments)
            }, Wp = b._emscripten_bind_btConvexTriangleMeshShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape_calculateLocalInertia_2.apply(null, arguments) }, Xp = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_bIsFrontWheel_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_bIsFrontWheel_1.apply(null, arguments) }, Yp = b._emscripten_bind_ClosestConvexResultCallback_get_m_convexToWorld_0 = function () {
                return b.asm._emscripten_bind_ClosestConvexResultCallback_get_m_convexToWorld_0.apply(null,
                    arguments)
            }, Zp = b._emscripten_bind_btGhostObject_getWorldTransform_0 = function () { return b.asm._emscripten_bind_btGhostObject_getWorldTransform_0.apply(null, arguments) }, $p = b._emscripten_bind_btDiscreteDynamicsWorld_getPairCache_0 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_getPairCache_0.apply(null, arguments) }, aq = b._emscripten_bind_LocalConvexResult_set_m_hitFraction_1 = function () { return b.asm._emscripten_bind_LocalConvexResult_set_m_hitFraction_1.apply(null, arguments) }, bq = b._emscripten_bind_btCapsuleShapeZ_calculateLocalInertia_2 =
                function () { return b.asm._emscripten_bind_btCapsuleShapeZ_calculateLocalInertia_2.apply(null, arguments) }, cq = b._emscripten_bind_btDispatcherInfo_get_m_timeStep_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_timeStep_0.apply(null, arguments) }, dq = b._emscripten_bind_btHingeConstraint_setAngularOnly_1 = function () { return b.asm._emscripten_bind_btHingeConstraint_setAngularOnly_1.apply(null, arguments) }, eq = b._emscripten_bind_btVehicleTuning_set_m_suspensionCompression_1 = function () {
                    return b.asm._emscripten_bind_btVehicleTuning_set_m_suspensionCompression_1.apply(null,
                        arguments)
                }, fq = b._emscripten_bind_btConstraintSetting_set_m_impulseClamp_1 = function () { return b.asm._emscripten_bind_btConstraintSetting_set_m_impulseClamp_1.apply(null, arguments) }, gq = b._emscripten_bind_btMotionState___destroy___0 = function () { return b.asm._emscripten_bind_btMotionState___destroy___0.apply(null, arguments) }, hq = b._emscripten_bind_btCollisionObject_setCollisionFlags_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setCollisionFlags_1.apply(null, arguments) }, iq = b._emscripten_bind_Config_get_kPR_0 =
                    function () { return b.asm._emscripten_bind_Config_get_kPR_0.apply(null, arguments) }, jq = b._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_1.apply(null, arguments) }, kq = b._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_2 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_2.apply(null, arguments) }, lq = b._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_3 = function () {
                        return b.asm._emscripten_bind_btDiscreteDynamicsWorld_addCollisionObject_3.apply(null,
                            arguments)
                    }, mq = b._emscripten_bind_btWheelInfo_set_m_suspensionStiffness_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_suspensionStiffness_1.apply(null, arguments) }, nq = b._emscripten_bind_RaycastInfo_set_m_suspensionLength_1 = function () { return b.asm._emscripten_bind_RaycastInfo_set_m_suspensionLength_1.apply(null, arguments) }, oq = b._emscripten_bind_btDispatcher_getManifoldByIndexInternal_1 = function () { return b.asm._emscripten_bind_btDispatcher_getManifoldByIndexInternal_1.apply(null, arguments) },
        pq = b._emscripten_bind_btSliderConstraint_setBreakingImpulseThreshold_1 = function () { return b.asm._emscripten_bind_btSliderConstraint_setBreakingImpulseThreshold_1.apply(null, arguments) }, qq = b._emscripten_bind_btSoftBodyWorldInfo___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo___destroy___0.apply(null, arguments) }, rq = b._emscripten_bind_btConvexTriangleMeshShape_getMargin_0 = function () { return b.asm._emscripten_bind_btConvexTriangleMeshShape_getMargin_0.apply(null, arguments) }, sq =
            b._emscripten_bind_btSoftBodySolver___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBodySolver___destroy___0.apply(null, arguments) }, tq = b._emscripten_bind_Node_get_m_n_0 = function () { return b.asm._emscripten_bind_Node_get_m_n_0.apply(null, arguments) }, uq = b._emscripten_bind_btWheelInfo_set_m_steering_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_steering_1.apply(null, arguments) }, vq = b._emscripten_bind_Node_set_m_x_1 = function () {
                return b.asm._emscripten_bind_Node_set_m_x_1.apply(null,
                    arguments)
            }, wq = b._emscripten_bind_btPairCachingGhostObject_setWorldTransform_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setWorldTransform_1.apply(null, arguments) }, xq = b._emscripten_bind_btHingeConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btHingeConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, yq = b._emscripten_bind_btDefaultCollisionConstructionInfo___destroy___0 = function () {
                return b.asm._emscripten_bind_btDefaultCollisionConstructionInfo___destroy___0.apply(null,
                    arguments)
            }, zq = b._emscripten_bind_btConeShape___destroy___0 = function () { return b.asm._emscripten_bind_btConeShape___destroy___0.apply(null, arguments) }, Aq = b._emscripten_bind_btGhostObject_setCcdSweptSphereRadius_1 = function () { return b.asm._emscripten_bind_btGhostObject_setCcdSweptSphereRadius_1.apply(null, arguments) }, Bq = b._emscripten_bind_btPoint2PointConstraint_btPoint2PointConstraint_4 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_btPoint2PointConstraint_4.apply(null, arguments) },
        Cq = b._emscripten_bind_btRaycastVehicle_updateFriction_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_updateFriction_1.apply(null, arguments) }, Dq = b._emscripten_bind_btPoint2PointConstraint_btPoint2PointConstraint_2 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_btPoint2PointConstraint_2.apply(null, arguments) }, Eq = b._emscripten_bind_btKinematicCharacterController_setJumpSpeed_1 = function () {
            return b.asm._emscripten_bind_btKinematicCharacterController_setJumpSpeed_1.apply(null,
                arguments)
        }, Fq = b._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration___destroy___0.apply(null, arguments) }, Gq = b._emscripten_bind_btConeShapeX_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConeShapeX_calculateLocalInertia_2.apply(null, arguments) }, Hq = b._emscripten_enum_PHY_ScalarType_PHY_FIXEDPOINT88 = function () {
            return b.asm._emscripten_enum_PHY_ScalarType_PHY_FIXEDPOINT88.apply(null,
                arguments)
        }, Iq = b._emscripten_bind_btPairCachingGhostObject_getOverlappingObject_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getOverlappingObject_1.apply(null, arguments) }, Jq = b._emscripten_bind_btGhostObject_getNumOverlappingObjects_0 = function () { return b.asm._emscripten_bind_btGhostObject_getNumOverlappingObjects_0.apply(null, arguments) }, Kq = b._emscripten_bind_btRigidBodyConstructionInfo___destroy___0 = function () {
            return b.asm._emscripten_bind_btRigidBodyConstructionInfo___destroy___0.apply(null,
                arguments)
        }, Lq = b._emscripten_bind_btGhostPairCallback___destroy___0 = function () { return b.asm._emscripten_bind_btGhostPairCallback___destroy___0.apply(null, arguments) }, Mq = b._emscripten_bind_btRigidBody_getWorldTransform_0 = function () { return b.asm._emscripten_bind_btRigidBody_getWorldTransform_0.apply(null, arguments) }, Db = b._sbrk = function () { return b.asm._sbrk.apply(null, arguments) }, Nq = b._emscripten_bind_btPoint2PointConstraint_setPivotA_1 = function () {
            return b.asm._emscripten_bind_btPoint2PointConstraint_setPivotA_1.apply(null,
                arguments)
        }, Oq = b._emscripten_bind_ClosestConvexResultCallback_set_m_convexToWorld_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_convexToWorld_1.apply(null, arguments) }, xb = b._memcpy = function () { return b.asm._memcpy.apply(null, arguments) }, Pq = b._emscripten_bind_Config_get_maxvolume_0 = function () { return b.asm._emscripten_bind_Config_get_maxvolume_0.apply(null, arguments) }, Qq = b._emscripten_bind_btCapsuleShape_calculateLocalInertia_2 = function () {
            return b.asm._emscripten_bind_btCapsuleShape_calculateLocalInertia_2.apply(null,
                arguments)
        }, Rq = b._emscripten_bind_btSoftRigidDynamicsWorld_getGravity_0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getGravity_0.apply(null, arguments) }, Sq = b._emscripten_bind_btVector3_y_0 = function () { return b.asm._emscripten_bind_btVector3_y_0.apply(null, arguments) }, Tq = b._emscripten_bind_btDispatcherInfo_set_m_useEpa_1 = function () { return b.asm._emscripten_bind_btDispatcherInfo_set_m_useEpa_1.apply(null, arguments) }, Uq = b._emscripten_bind_btVehicleTuning_get_m_maxSuspensionForce_0 =
            function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_maxSuspensionForce_0.apply(null, arguments) }, Vq = b._emscripten_bind_btBvhTriangleMeshShape_btBvhTriangleMeshShape_2 = function () { return b.asm._emscripten_bind_btBvhTriangleMeshShape_btBvhTriangleMeshShape_2.apply(null, arguments) }, Wq = b._emscripten_bind_btBvhTriangleMeshShape_btBvhTriangleMeshShape_3 = function () { return b.asm._emscripten_bind_btBvhTriangleMeshShape_btBvhTriangleMeshShape_3.apply(null, arguments) }, Xq = b._emscripten_bind_LocalShapeInfo_get_m_triangleIndex_0 =
                function () { return b.asm._emscripten_bind_LocalShapeInfo_get_m_triangleIndex_0.apply(null, arguments) }, Yq = b._emscripten_bind_Config_set_kDF_1 = function () { return b.asm._emscripten_bind_Config_set_kDF_1.apply(null, arguments) }, Zq = b._emscripten_bind_btHeightfieldTerrainShape_btHeightfieldTerrainShape_9 = function () { return b.asm._emscripten_bind_btHeightfieldTerrainShape_btHeightfieldTerrainShape_9.apply(null, arguments) }, $q = b._emscripten_bind_btSoftBody_activate_1 = function () {
                    return b.asm._emscripten_bind_btSoftBody_activate_1.apply(null,
                        arguments)
                }, ar = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_frictionSlip_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_frictionSlip_1.apply(null, arguments) }, br = b._emscripten_bind_btGhostObject_setCollisionShape_1 = function () { return b.asm._emscripten_bind_btGhostObject_setCollisionShape_1.apply(null, arguments) }, cr = b._emscripten_bind_btDispatcherInfo_set_m_allowedCcdPenetration_1 = function () {
                    return b.asm._emscripten_bind_btDispatcherInfo_set_m_allowedCcdPenetration_1.apply(null,
                        arguments)
                }, dr = b._emscripten_bind_btRigidBody_setRollingFriction_1 = function () { return b.asm._emscripten_bind_btRigidBody_setRollingFriction_1.apply(null, arguments) }, er = b._emscripten_bind_btPairCachingGhostObject_setRollingFriction_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setRollingFriction_1.apply(null, arguments) }, fr = b._emscripten_bind_btDiscreteDynamicsWorld_setGravity_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_setGravity_1.apply(null, arguments) };
    b._emscripten_get_global_libc = function () { return b.asm._emscripten_get_global_libc.apply(null, arguments) };
    var gr = b._emscripten_bind_btVehicleTuning_set_m_suspensionStiffness_1 = function () { return b.asm._emscripten_bind_btVehicleTuning_set_m_suspensionStiffness_1.apply(null, arguments) }, hr = b._emscripten_bind_btVector4_z_0 = function () { return b.asm._emscripten_bind_btVector4_z_0.apply(null, arguments) }, ir = b._emscripten_bind_btCollisionObject_forceActivationState_1 = function () { return b.asm._emscripten_bind_btCollisionObject_forceActivationState_1.apply(null, arguments) }, jr = b._emscripten_bind_btKinematicCharacterController_onGround_0 =
        function () { return b.asm._emscripten_bind_btKinematicCharacterController_onGround_0.apply(null, arguments) }, kr = b._emscripten_bind_btRaycastVehicle_getWheelInfo_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getWheelInfo_1.apply(null, arguments) }, lr = b._emscripten_bind_btGeneric6DofConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, mr = b._emscripten_bind_btVector3_length_0 = function () {
            return b.asm._emscripten_bind_btVector3_length_0.apply(null,
                arguments)
        }, nr = b._emscripten_bind_ClosestConvexResultCallback_set_m_collisionFilterMask_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_collisionFilterMask_1.apply(null, arguments) }, or = b._emscripten_bind_btSoftBodyWorldInfo_get_water_normal_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_water_normal_0.apply(null, arguments) }, pr = b._emscripten_bind_btVector3_normalize_0 = function () { return b.asm._emscripten_bind_btVector3_normalize_0.apply(null, arguments) },
        qr = b._emscripten_bind_btConeTwistConstraint_setLimit_2 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_setLimit_2.apply(null, arguments) }, rr = b._emscripten_bind_btSoftBody_setFriction_1 = function () { return b.asm._emscripten_bind_btSoftBody_setFriction_1.apply(null, arguments) }; b.runPostSets = function () { return b.asm.runPostSets.apply(null, arguments) };
    var sr = b._emscripten_bind_btRigidBody_setSleepingThresholds_2 = function () { return b.asm._emscripten_bind_btRigidBody_setSleepingThresholds_2.apply(null, arguments) }, tr = b._emscripten_bind_btSoftBody_upcast_1 = function () { return b.asm._emscripten_bind_btSoftBody_upcast_1.apply(null, arguments) }, ur = b._emscripten_bind_btCollisionObject_setWorldTransform_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setWorldTransform_1.apply(null, arguments) }, vr = b._emscripten_bind_LocalConvexResult_get_m_localShapeInfo_0 =
        function () { return b.asm._emscripten_bind_LocalConvexResult_get_m_localShapeInfo_0.apply(null, arguments) }, wr = b._emscripten_bind_btSoftBodyWorldInfo_set_m_dispatcher_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_m_dispatcher_1.apply(null, arguments) }, xr = b._emscripten_bind_btConvexHullShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btConvexHullShape_setLocalScaling_1.apply(null, arguments) }, yr = b._emscripten_bind_btStridingMeshInterface___destroy___0 = function () {
            return b.asm._emscripten_bind_btStridingMeshInterface___destroy___0.apply(null,
                arguments)
        }, zr = b._emscripten_bind_btSoftBody_setActivationState_1 = function () { return b.asm._emscripten_bind_btSoftBody_setActivationState_1.apply(null, arguments) }, Ar = b._emscripten_bind_btRigidBody_getUserIndex_0 = function () { return b.asm._emscripten_bind_btRigidBody_getUserIndex_0.apply(null, arguments) }, Br = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_linearDamping_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_linearDamping_0.apply(null, arguments) }, Cr = b._emscripten_bind_btSoftBodyHelpers_CreatePatch_9 =
            function () { return b.asm._emscripten_bind_btSoftBodyHelpers_CreatePatch_9.apply(null, arguments) }, Dr = b._emscripten_bind_btDispatcher_getNumManifolds_0 = function () { return b.asm._emscripten_bind_btDispatcher_getNumManifolds_0.apply(null, arguments) }, Er = b._emscripten_bind_btConvexShape_setMargin_1 = function () { return b.asm._emscripten_bind_btConvexShape_setMargin_1.apply(null, arguments) }, Fr = b._emscripten_bind_btSoftBody_get_m_nodes_0 = function () {
                return b.asm._emscripten_bind_btSoftBody_get_m_nodes_0.apply(null,
                    arguments)
            }, Gr = b._emscripten_bind_btSoftBody___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBody___destroy___0.apply(null, arguments) }, Hr = b._emscripten_bind_btRigidBodyConstructionInfo_get_m_linearSleepingThreshold_0 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_get_m_linearSleepingThreshold_0.apply(null, arguments) }, Ir = b._emscripten_bind_btRigidBody_activate_1 = function () { return b.asm._emscripten_bind_btRigidBody_activate_1.apply(null, arguments) }, Jr = b._emscripten_bind_btRaycastVehicle_updateWheelTransform_2 =
                function () { return b.asm._emscripten_bind_btRaycastVehicle_updateWheelTransform_2.apply(null, arguments) }, Kr = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_maxSuspensionForce_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_maxSuspensionForce_1.apply(null, arguments) }, Lr = b._emscripten_bind_btSoftBodyWorldInfo_get_m_gravity_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_m_gravity_0.apply(null, arguments) }, Mr = b._emscripten_bind_Material_set_m_kVST_1 =
                    function () { return b.asm._emscripten_bind_Material_set_m_kVST_1.apply(null, arguments) }, Nr = b._emscripten_bind_btGhostObject_setActivationState_1 = function () { return b.asm._emscripten_bind_btGhostObject_setActivationState_1.apply(null, arguments) }, Or = b._emscripten_bind_Material_set_m_kLST_1 = function () { return b.asm._emscripten_bind_Material_set_m_kLST_1.apply(null, arguments) }, Pr = b._emscripten_bind_btCollisionWorld_contactPairTest_3 = function () {
                        return b.asm._emscripten_bind_btCollisionWorld_contactPairTest_3.apply(null,
                            arguments)
                    }, Qr = b._emscripten_bind_btDispatcherInfo_get_m_useContinuous_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_useContinuous_0.apply(null, arguments) }, Rr = b._emscripten_bind_btHingeConstraint_setMaxMotorImpulse_1 = function () { return b.asm._emscripten_bind_btHingeConstraint_setMaxMotorImpulse_1.apply(null, arguments) }, Sr = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelsDampingRelaxation_0 = function () {
                        return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelsDampingRelaxation_0.apply(null,
                            arguments)
                    }, Tr = b._emscripten_bind_Config_get_kSS_SPLT_CL_0 = function () { return b.asm._emscripten_bind_Config_get_kSS_SPLT_CL_0.apply(null, arguments) }, Ur = b._emscripten_bind_btCylinderShapeX___destroy___0 = function () { return b.asm._emscripten_bind_btCylinderShapeX___destroy___0.apply(null, arguments) }, Vr = b._emscripten_bind_btRigidBodyConstructionInfo_set_m_linearSleepingThreshold_1 = function () { return b.asm._emscripten_bind_btRigidBodyConstructionInfo_set_m_linearSleepingThreshold_1.apply(null, arguments) },
        Wr = b._emscripten_bind_btRigidBody_updateInertiaTensor_0 = function () { return b.asm._emscripten_bind_btRigidBody_updateInertiaTensor_0.apply(null, arguments) }, Xr = b._emscripten_bind_ContactResultCallback___destroy___0 = function () { return b.asm._emscripten_bind_ContactResultCallback___destroy___0.apply(null, arguments) }, Yr = b._emscripten_bind_btDispatcherInfo_get_m_useConvexConservativeDistanceUtil_0 = function () {
            return b.asm._emscripten_bind_btDispatcherInfo_get_m_useConvexConservativeDistanceUtil_0.apply(null,
                arguments)
        }, Zr = b._emscripten_bind_btSoftBody_setAnisotropicFriction_2 = function () { return b.asm._emscripten_bind_btSoftBody_setAnisotropicFriction_2.apply(null, arguments) }, $r = b._emscripten_bind_btPairCachingGhostObject_setCollisionFlags_1 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_setCollisionFlags_1.apply(null, arguments) }, as = b._emscripten_bind_btRigidBody_getMotionState_0 = function () { return b.asm._emscripten_bind_btRigidBody_getMotionState_0.apply(null, arguments) }, bs = b._emscripten_bind_btKinematicCharacterController_getGhostObject_0 =
            function () { return b.asm._emscripten_bind_btKinematicCharacterController_getGhostObject_0.apply(null, arguments) }, cs = b._emscripten_bind_btRigidBody_btRigidBody_1 = function () { return b.asm._emscripten_bind_btRigidBody_btRigidBody_1.apply(null, arguments) }, ds = b._emscripten_bind_btTriangleMeshShape___destroy___0 = function () { return b.asm._emscripten_bind_btTriangleMeshShape___destroy___0.apply(null, arguments) }, es = b._emscripten_bind_btKinematicCharacterController_setWalkDirection_1 = function () {
                return b.asm._emscripten_bind_btKinematicCharacterController_setWalkDirection_1.apply(null,
                    arguments)
            }, gs = b._emscripten_bind_btDynamicsWorld_removeAction_1 = function () { return b.asm._emscripten_bind_btDynamicsWorld_removeAction_1.apply(null, arguments) }, hs = b._emscripten_bind_btRigidBody_applyTorque_1 = function () { return b.asm._emscripten_bind_btRigidBody_applyTorque_1.apply(null, arguments) }, is = b._emscripten_bind_btManifoldPoint_get_m_localPointA_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_get_m_localPointA_0.apply(null, arguments) }, js = b._emscripten_bind_btDefaultCollisionConstructionInfo_btDefaultCollisionConstructionInfo_0 =
                function () { return b.asm._emscripten_bind_btDefaultCollisionConstructionInfo_btDefaultCollisionConstructionInfo_0.apply(null, arguments) }, ks = b._emscripten_bind_btVehicleTuning_get_m_suspensionStiffness_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_suspensionStiffness_0.apply(null, arguments) }, ls = b._emscripten_bind_btManifoldPoint_set_m_normalWorldOnB_1 = function () { return b.asm._emscripten_bind_btManifoldPoint_set_m_normalWorldOnB_1.apply(null, arguments) }, ms = b._emscripten_bind_btGhostObject_setUserPointer_1 =
                    function () { return b.asm._emscripten_bind_btGhostObject_setUserPointer_1.apply(null, arguments) }, ns = b._emscripten_bind_btConvexHullShape_addPoint_2 = function () { return b.asm._emscripten_bind_btConvexHullShape_addPoint_2.apply(null, arguments) }, ps = b._emscripten_bind_btKinematicCharacterController_getGravity_0 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_getGravity_0.apply(null, arguments) }, qs = b._emscripten_enum_PHY_ScalarType_PHY_SHORT = function () {
                        return b.asm._emscripten_enum_PHY_ScalarType_PHY_SHORT.apply(null,
                            arguments)
                    }, rs = b._emscripten_bind_btConeTwistConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btConeTwistConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, ss = b._emscripten_bind_btGeneric6DofConstraint_setAngularLowerLimit_1 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_setAngularLowerLimit_1.apply(null, arguments) }, ts = b._emscripten_bind_btVehicleRaycasterResult_get_m_distFraction_0 = function () {
                        return b.asm._emscripten_bind_btVehicleRaycasterResult_get_m_distFraction_0.apply(null,
                            arguments)
                    }, us = b._emscripten_bind_btQuaternion_op_sub_1 = function () { return b.asm._emscripten_bind_btQuaternion_op_sub_1.apply(null, arguments) }, vs = b._emscripten_bind_btVector4_normalize_0 = function () { return b.asm._emscripten_bind_btVector4_normalize_0.apply(null, arguments) }, xs = b._emscripten_bind_btQuaternion_setY_1 = function () { return b.asm._emscripten_bind_btQuaternion_setY_1.apply(null, arguments) }, ys = b._emscripten_bind_btConeShape_calculateLocalInertia_2 = function () {
                        return b.asm._emscripten_bind_btConeShape_calculateLocalInertia_2.apply(null,
                            arguments)
                    }, zs = b._emscripten_bind_btCylinderShapeX_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btCylinderShapeX_calculateLocalInertia_2.apply(null, arguments) }, As = b._emscripten_bind_ConvexResultCallback_set_m_collisionFilterMask_1 = function () { return b.asm._emscripten_bind_ConvexResultCallback_set_m_collisionFilterMask_1.apply(null, arguments) }, Hb = b._llvm_bswap_i32 = function () { return b.asm._llvm_bswap_i32.apply(null, arguments) }, Bs = b._emscripten_bind_btRaycastVehicle_getForwardVector_0 =
                        function () { return b.asm._emscripten_bind_btRaycastVehicle_getForwardVector_0.apply(null, arguments) }, Cs = b._emscripten_bind_btKinematicCharacterController_setVelocityForTimeInterval_2 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_setVelocityForTimeInterval_2.apply(null, arguments) }, Ds = b._emscripten_bind_btWheelInfo_set_m_suspensionRelativeVelocity_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_suspensionRelativeVelocity_1.apply(null, arguments) }, Es = b._emscripten_bind_btSphereShape_setLocalScaling_1 =
                            function () { return b.asm._emscripten_bind_btSphereShape_setLocalScaling_1.apply(null, arguments) }, Fs = b._emscripten_bind_btRigidBody_applyCentralLocalForce_1 = function () { return b.asm._emscripten_bind_btRigidBody_applyCentralLocalForce_1.apply(null, arguments) }, Gs = b._emscripten_bind_btDiscreteDynamicsWorld_removeAction_1 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_removeAction_1.apply(null, arguments) }, Hs = b._emscripten_bind_btVector4_w_0 = function () {
                                return b.asm._emscripten_bind_btVector4_w_0.apply(null,
                                    arguments)
                            }, Is = b._emscripten_bind_btWheelInfo_get_m_worldTransform_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_worldTransform_0.apply(null, arguments) }, Js = b._emscripten_bind_btManifoldPoint_get_m_normalWorldOnB_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_get_m_normalWorldOnB_0.apply(null, arguments) }, Ks = b._emscripten_bind_btBvhTriangleMeshShape___destroy___0 = function () { return b.asm._emscripten_bind_btBvhTriangleMeshShape___destroy___0.apply(null, arguments) }, Ls = b._emscripten_bind_Config_set_citerations_1 =
                                function () { return b.asm._emscripten_bind_Config_set_citerations_1.apply(null, arguments) }, Ms = b._emscripten_bind_btSoftBody_checkFace_3 = function () { return b.asm._emscripten_bind_btSoftBody_checkFace_3.apply(null, arguments) }, Ns = b._emscripten_bind_Config_get_kSKHR_CL_0 = function () { return b.asm._emscripten_bind_Config_get_kSKHR_CL_0.apply(null, arguments) }, Os = b._emscripten_bind_btDispatcherInfo_get_m_enableSatConvex_0 = function () {
                                    return b.asm._emscripten_bind_btDispatcherInfo_get_m_enableSatConvex_0.apply(null,
                                        arguments)
                                }, Ps = b._emscripten_bind_btDefaultVehicleRaycaster_castRay_3 = function () { return b.asm._emscripten_bind_btDefaultVehicleRaycaster_castRay_3.apply(null, arguments) }, Qs = b._emscripten_bind_LocalConvexResult_LocalConvexResult_5 = function () { return b.asm._emscripten_bind_LocalConvexResult_LocalConvexResult_5.apply(null, arguments) }, Rs = b._emscripten_bind_btContactSolverInfo_get_m_numIterations_0 = function () { return b.asm._emscripten_bind_btContactSolverInfo_get_m_numIterations_0.apply(null, arguments) },
        Ss = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_maxSuspensionTravelCm_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_maxSuspensionTravelCm_0.apply(null, arguments) }, Ts = b._emscripten_bind_ClosestConvexResultCallback_set_m_closestHitFraction_1 = function () { return b.asm._emscripten_bind_ClosestConvexResultCallback_set_m_closestHitFraction_1.apply(null, arguments) }, Us = b._emscripten_bind_btDiscreteDynamicsWorld_removeConstraint_1 = function () {
            return b.asm._emscripten_bind_btDiscreteDynamicsWorld_removeConstraint_1.apply(null,
                arguments)
        }, Vs = b._emscripten_bind_ConcreteContactResultCallback_ConcreteContactResultCallback_0 = function () { return b.asm._emscripten_bind_ConcreteContactResultCallback_ConcreteContactResultCallback_0.apply(null, arguments) }, Ws = b._emscripten_bind_Config_set_diterations_1 = function () { return b.asm._emscripten_bind_Config_set_diterations_1.apply(null, arguments) }, Xs = b._emscripten_bind_btRaycastVehicle_getUserConstraintType_0 = function () {
            return b.asm._emscripten_bind_btRaycastVehicle_getUserConstraintType_0.apply(null,
                arguments)
        }, Ys = b._emscripten_bind_btGeneric6DofConstraint___destroy___0 = function () { return b.asm._emscripten_bind_btGeneric6DofConstraint___destroy___0.apply(null, arguments) }, Zs = b._emscripten_bind_btSoftRigidDynamicsWorld_addRigidBody_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addRigidBody_1.apply(null, arguments) }, $s = b._emscripten_bind_btSoftRigidDynamicsWorld_addRigidBody_3 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addRigidBody_3.apply(null, arguments) },
        at = b._emscripten_bind_Config_set_kDP_1 = function () { return b.asm._emscripten_bind_Config_set_kDP_1.apply(null, arguments) }, bt = b._emscripten_bind_btVehicleTuning_get_m_maxSuspensionTravelCm_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_maxSuspensionTravelCm_0.apply(null, arguments) }, ct = b._emscripten_bind_btConvexHullShape_addPoint_1 = function () { return b.asm._emscripten_bind_btConvexHullShape_addPoint_1.apply(null, arguments) }, dt = b._emscripten_bind_btQuaternion_length2_0 = function () {
            return b.asm._emscripten_bind_btQuaternion_length2_0.apply(null,
                arguments)
        }, et = b._emscripten_bind_btRaycastVehicle_resetSuspension_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_resetSuspension_0.apply(null, arguments) }, ft = b._emscripten_bind_btPoint2PointConstraint_getBreakingImpulseThreshold_0 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_getBreakingImpulseThreshold_0.apply(null, arguments) }, gt = b._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration_btSoftBodyRigidBodyCollisionConfiguration_1 = function () {
            return b.asm._emscripten_bind_btSoftBodyRigidBodyCollisionConfiguration_btSoftBodyRigidBodyCollisionConfiguration_1.apply(null,
                arguments)
        }, ht = b._emscripten_bind_btTransform_getOrigin_0 = function () { return b.asm._emscripten_bind_btTransform_getOrigin_0.apply(null, arguments) }, it = b._emscripten_bind_Config_get_kKHR_0 = function () { return b.asm._emscripten_bind_Config_get_kKHR_0.apply(null, arguments) }, jt = b._emscripten_bind_Material_get_m_kLST_0 = function () { return b.asm._emscripten_bind_Material_get_m_kLST_0.apply(null, arguments) }, kt = b._emscripten_bind_btHingeConstraint___destroy___0 = function () {
            return b.asm._emscripten_bind_btHingeConstraint___destroy___0.apply(null,
                arguments)
        }, lt = b._emscripten_bind_btPairCachingGhostObject_getUserPointer_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getUserPointer_0.apply(null, arguments) }, mt = b._emscripten_bind_btSoftBody_set_m_nodes_1 = function () { return b.asm._emscripten_bind_btSoftBody_set_m_nodes_1.apply(null, arguments) }, nt = b._emscripten_bind_btSoftBodyWorldInfo_set_air_density_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_air_density_1.apply(null, arguments) }, ot = b._emscripten_bind_btDbvtBroadphase___destroy___0 =
            function () { return b.asm._emscripten_bind_btDbvtBroadphase___destroy___0.apply(null, arguments) }, pt = b._emscripten_bind_Config_set_viterations_1 = function () { return b.asm._emscripten_bind_Config_set_viterations_1.apply(null, arguments) }, qt = b._emscripten_bind_btConvexShape_calculateLocalInertia_2 = function () { return b.asm._emscripten_bind_btConvexShape_calculateLocalInertia_2.apply(null, arguments) }, pb = b._memset = function () { return b.asm._memset.apply(null, arguments) }, rt = b._emscripten_bind_btGeneric6DofConstraint_setLinearLowerLimit_1 =
                function () { return b.asm._emscripten_bind_btGeneric6DofConstraint_setLinearLowerLimit_1.apply(null, arguments) }, st = b._emscripten_bind_ClosestRayResultCallback_get_m_hitNormalWorld_0 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_hitNormalWorld_0.apply(null, arguments) }, tt = b._emscripten_bind_btTriangleMesh_btTriangleMesh_0 = function () { return b.asm._emscripten_bind_btTriangleMesh_btTriangleMesh_0.apply(null, arguments) }, ut = b._emscripten_bind_btTriangleMesh_btTriangleMesh_1 = function () {
                    return b.asm._emscripten_bind_btTriangleMesh_btTriangleMesh_1.apply(null,
                        arguments)
                }, vt = b._emscripten_bind_btTriangleMesh_btTriangleMesh_2 = function () { return b.asm._emscripten_bind_btTriangleMesh_btTriangleMesh_2.apply(null, arguments) }, wt = b._emscripten_bind_btWheelInfo_set_m_frictionSlip_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_frictionSlip_1.apply(null, arguments) }, xt = b._emscripten_bind_btSoftBodyHelpers___destroy___0 = function () { return b.asm._emscripten_bind_btSoftBodyHelpers___destroy___0.apply(null, arguments) }, yt = b._emscripten_bind_btRigidBody_getCollisionShape_0 =
                    function () { return b.asm._emscripten_bind_btRigidBody_getCollisionShape_0.apply(null, arguments) }, zt = b._emscripten_bind_btManifoldPoint_set_m_positionWorldOnA_1 = function () { return b.asm._emscripten_bind_btManifoldPoint_set_m_positionWorldOnA_1.apply(null, arguments) }, At = b._emscripten_bind_btWheelInfo_get_m_wheelsDampingRelaxation_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_wheelsDampingRelaxation_0.apply(null, arguments) }, Bt = b._emscripten_bind_btManifoldPoint_get_m_localPointB_0 = function () {
                        return b.asm._emscripten_bind_btManifoldPoint_get_m_localPointB_0.apply(null,
                            arguments)
                    }, Ct = b._emscripten_bind_btQuaternion_inverse_0 = function () { return b.asm._emscripten_bind_btQuaternion_inverse_0.apply(null, arguments) }, Dt = b._emscripten_bind_btDiscreteDynamicsWorld_contactPairTest_3 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_contactPairTest_3.apply(null, arguments) }, Et = b._emscripten_bind_btSliderConstraint_setLowerLinLimit_1 = function () { return b.asm._emscripten_bind_btSliderConstraint_setLowerLinLimit_1.apply(null, arguments) }, Ft = b._emscripten_bind_btRigidBody_getAngularVelocity_0 =
                        function () { return b.asm._emscripten_bind_btRigidBody_getAngularVelocity_0.apply(null, arguments) }, Gt = b._emscripten_bind_btCollisionObject_setCcdSweptSphereRadius_1 = function () { return b.asm._emscripten_bind_btCollisionObject_setCcdSweptSphereRadius_1.apply(null, arguments) }, Ht = b._emscripten_bind_btWheelInfo_get_m_wheelsRadius_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_wheelsRadius_0.apply(null, arguments) }, It = b._emscripten_bind_btRigidBody_setLinearVelocity_1 = function () {
                            return b.asm._emscripten_bind_btRigidBody_setLinearVelocity_1.apply(null,
                                arguments)
                        }, Jt = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelDirectionCS_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelDirectionCS_0.apply(null, arguments) }, Kt = b._emscripten_bind_btVehicleTuning_btVehicleTuning_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_btVehicleTuning_0.apply(null, arguments) }, Lt = b._emscripten_bind_RayResultCallback_set_m_collisionObject_1 = function () {
                            return b.asm._emscripten_bind_RayResultCallback_set_m_collisionObject_1.apply(null,
                                arguments)
                        }, Mt = b._emscripten_bind_btDefaultSoftBodySolver___destroy___0 = function () { return b.asm._emscripten_bind_btDefaultSoftBodySolver___destroy___0.apply(null, arguments) }, Nt = b._emscripten_bind_ClosestRayResultCallback_set_m_rayToWorld_1 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_rayToWorld_1.apply(null, arguments) }, Ot = b._emscripten_bind_ClosestRayResultCallback_get_m_collisionFilterGroup_0 = function () {
                            return b.asm._emscripten_bind_ClosestRayResultCallback_get_m_collisionFilterGroup_0.apply(null,
                                arguments)
                        }, Pt = b._emscripten_bind_btWheelInfo_set_m_wheelsDampingRelaxation_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_wheelsDampingRelaxation_1.apply(null, arguments) }, Qt = b._emscripten_bind_btWheelInfo_get_m_clippedInvContactDotSuspension_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_clippedInvContactDotSuspension_0.apply(null, arguments) }, Rt = b._emscripten_bind_btDynamicsWorld_addAction_1 = function () {
                            return b.asm._emscripten_bind_btDynamicsWorld_addAction_1.apply(null,
                                arguments)
                        }, St = b._emscripten_bind_btSoftBody_appendMaterial_0 = function () { return b.asm._emscripten_bind_btSoftBody_appendMaterial_0.apply(null, arguments) }, Tt = b._emscripten_bind_btSoftBodyWorldInfo_set_m_maxDisplacement_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_m_maxDisplacement_1.apply(null, arguments) }, Ut = b._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_2 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_stepSimulation_2.apply(null, arguments) }, Vt =
            b._emscripten_bind_btPairCachingGhostObject_getCollisionFlags_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_getCollisionFlags_0.apply(null, arguments) }, Wt = b._emscripten_bind_btSoftBodyWorldInfo_get_air_density_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_air_density_0.apply(null, arguments) }, Xt = b._emscripten_bind_btSoftBody_setRestitution_1 = function () { return b.asm._emscripten_bind_btSoftBody_setRestitution_1.apply(null, arguments) }, Yt = b._emscripten_bind_Config_set_kLF_1 =
                function () { return b.asm._emscripten_bind_Config_set_kLF_1.apply(null, arguments) }, Zt = b._emscripten_bind_btWheelInfo_get_m_rotation_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_rotation_0.apply(null, arguments) }, $t = b._emscripten_enum_PHY_ScalarType_PHY_FLOAT = function () { return b.asm._emscripten_enum_PHY_ScalarType_PHY_FLOAT.apply(null, arguments) }, au = b._emscripten_bind_btWheelInfo_set_m_skidInfo_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_skidInfo_1.apply(null, arguments) },
        bu = b._emscripten_bind_Config_set_kSS_SPLT_CL_1 = function () { return b.asm._emscripten_bind_Config_set_kSS_SPLT_CL_1.apply(null, arguments) }, cu = b._emscripten_bind_btGhostObject_isActive_0 = function () { return b.asm._emscripten_bind_btGhostObject_isActive_0.apply(null, arguments) }, du = b._emscripten_bind_btWheelInfoConstructionInfo_set_m_suspensionRestLength_1 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_set_m_suspensionRestLength_1.apply(null, arguments) }, eu = b._emscripten_bind_btKinematicCharacterController_setFallSpeed_1 =
            function () { return b.asm._emscripten_bind_btKinematicCharacterController_setFallSpeed_1.apply(null, arguments) }, fu = b._emscripten_bind_btRigidBody_setActivationState_1 = function () { return b.asm._emscripten_bind_btRigidBody_setActivationState_1.apply(null, arguments) }, gu = b._emscripten_bind_btWheelInfo_get_m_wheelsDampingCompression_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_wheelsDampingCompression_0.apply(null, arguments) }, hu = b._emscripten_bind_ClosestConvexResultCallback_hasHit_0 = function () {
                return b.asm._emscripten_bind_ClosestConvexResultCallback_hasHit_0.apply(null,
                    arguments)
            }, iu = b._emscripten_bind_btCapsuleShapeZ___destroy___0 = function () { return b.asm._emscripten_bind_btCapsuleShapeZ___destroy___0.apply(null, arguments) }, ju = b._emscripten_bind_btRaycastVehicle_getRigidBody_0 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getRigidBody_0.apply(null, arguments) }, ku = b._emscripten_bind_btWheelInfo_get_m_maxSuspensionForce_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_maxSuspensionForce_0.apply(null, arguments) }, lu = b._emscripten_bind_btSoftBody_get_m_materials_0 =
                function () { return b.asm._emscripten_bind_btSoftBody_get_m_materials_0.apply(null, arguments) }, mu = b._emscripten_bind_btTriangleMesh_addTriangle_3 = function () { return b.asm._emscripten_bind_btTriangleMesh_addTriangle_3.apply(null, arguments) }, nu = b._emscripten_bind_btGhostObject_getOverlappingObject_1 = function () { return b.asm._emscripten_bind_btGhostObject_getOverlappingObject_1.apply(null, arguments) }, ou = b._emscripten_bind_btTriangleMesh_addTriangle_4 = function () {
                    return b.asm._emscripten_bind_btTriangleMesh_addTriangle_4.apply(null,
                        arguments)
                }, pu = b._emscripten_bind_btSoftRigidDynamicsWorld_getDispatchInfo_0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getDispatchInfo_0.apply(null, arguments) }, qu = b._emscripten_bind_btSoftBodyWorldInfo_set_water_normal_1 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_set_water_normal_1.apply(null, arguments) }, ru = b._emscripten_bind_btSoftRigidDynamicsWorld_addConstraint_2 = function () {
                    return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_addConstraint_2.apply(null,
                        arguments)
                }, su = b._emscripten_bind_Config_get_kDF_0 = function () { return b.asm._emscripten_bind_Config_get_kDF_0.apply(null, arguments) }, tu = b._emscripten_bind_btRigidBody_applyTorqueImpulse_1 = function () { return b.asm._emscripten_bind_btRigidBody_applyTorqueImpulse_1.apply(null, arguments) }, uu = b._emscripten_bind_btVector3_op_add_1 = function () { return b.asm._emscripten_bind_btVector3_op_add_1.apply(null, arguments) }, vu = b._emscripten_bind_btRigidBody_setCollisionFlags_1 = function () {
                    return b.asm._emscripten_bind_btRigidBody_setCollisionFlags_1.apply(null,
                        arguments)
                }, wu = b._emscripten_bind_btWheelInfo_get_m_steering_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_steering_0.apply(null, arguments) }, xu = b._emscripten_bind_btRigidBody___destroy___0 = function () { return b.asm._emscripten_bind_btRigidBody___destroy___0.apply(null, arguments) }, yu = b._emscripten_bind_btWheelInfo_set_m_suspensionRestLength1_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_suspensionRestLength1_1.apply(null, arguments) }, zu = b._emscripten_bind_Config_set_kCHR_1 =
                    function () { return b.asm._emscripten_bind_Config_set_kCHR_1.apply(null, arguments) }, Au = b._emscripten_bind_btRaycastVehicle_setUserConstraintType_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_setUserConstraintType_1.apply(null, arguments) }, Bu = b._emscripten_bind_btSoftRigidDynamicsWorld_contactTest_2 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_contactTest_2.apply(null, arguments) }, Cu = b._emscripten_bind_btCapsuleShapeZ_btCapsuleShapeZ_2 = function () {
                        return b.asm._emscripten_bind_btCapsuleShapeZ_btCapsuleShapeZ_2.apply(null,
                            arguments)
                    }, Du = b._emscripten_bind_btDispatcherInfo_get_m_enableSPU_0 = function () { return b.asm._emscripten_bind_btDispatcherInfo_get_m_enableSPU_0.apply(null, arguments) }, Eu = b._emscripten_bind_btSoftRigidDynamicsWorld_getWorldInfo_0 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_getWorldInfo_0.apply(null, arguments) }, Fu = b._emscripten_bind_btSliderConstraint_btSliderConstraint_3 = function () { return b.asm._emscripten_bind_btSliderConstraint_btSliderConstraint_3.apply(null, arguments) }, Gu =
            b._emscripten_bind_btTransform___destroy___0 = function () { return b.asm._emscripten_bind_btTransform___destroy___0.apply(null, arguments) }, Hu = b._emscripten_bind_btWheelInfo_get_m_wheelAxleCS_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_wheelAxleCS_0.apply(null, arguments) }, Iu = b._emscripten_bind_btDynamicsWorld_convexSweepTest_5 = function () { return b.asm._emscripten_bind_btDynamicsWorld_convexSweepTest_5.apply(null, arguments) }, Ju = b._emscripten_bind_btSliderConstraint___destroy___0 = function () {
                return b.asm._emscripten_bind_btSliderConstraint___destroy___0.apply(null,
                    arguments)
            }, Ku = b._emscripten_bind_btRigidBody_forceActivationState_1 = function () { return b.asm._emscripten_bind_btRigidBody_forceActivationState_1.apply(null, arguments) }, Lu = b._emscripten_bind_btPoint2PointConstraint_setPivotB_1 = function () { return b.asm._emscripten_bind_btPoint2PointConstraint_setPivotB_1.apply(null, arguments) }, Mu = b._emscripten_bind_btManifoldPoint_getDistance_0 = function () { return b.asm._emscripten_bind_btManifoldPoint_getDistance_0.apply(null, arguments) }, Nu = b._emscripten_bind_btWheelInfo_set_m_wheelAxleCS_1 =
                function () { return b.asm._emscripten_bind_btWheelInfo_set_m_wheelAxleCS_1.apply(null, arguments) }, Ou = b._emscripten_bind_btTransform_setFromOpenGLMatrix_1 = function () { return b.asm._emscripten_bind_btTransform_setFromOpenGLMatrix_1.apply(null, arguments) }, Pu = b._emscripten_bind_btKinematicCharacterController_getMaxSlope_0 = function () { return b.asm._emscripten_bind_btKinematicCharacterController_getMaxSlope_0.apply(null, arguments) }, Qu = b._emscripten_bind_btManifoldPoint_getPositionWorldOnA_0 = function () {
                    return b.asm._emscripten_bind_btManifoldPoint_getPositionWorldOnA_0.apply(null,
                        arguments)
                }, Ru = b._emscripten_bind_btRaycastVehicle_addWheel_7 = function () { return b.asm._emscripten_bind_btRaycastVehicle_addWheel_7.apply(null, arguments) }, Su = b._emscripten_bind_btQuaternion_op_add_1 = function () { return b.asm._emscripten_bind_btQuaternion_op_add_1.apply(null, arguments) }, Tu = b._emscripten_bind_ClosestRayResultCallback_set_m_hitNormalWorld_1 = function () { return b.asm._emscripten_bind_ClosestRayResultCallback_set_m_hitNormalWorld_1.apply(null, arguments) }, Uu = b._emscripten_bind_btRaycastVehicle_updateWheelTransformsWS_2 =
                    function () { return b.asm._emscripten_bind_btRaycastVehicle_updateWheelTransformsWS_2.apply(null, arguments) }, Vu = b._emscripten_bind_btStaticPlaneShape___destroy___0 = function () { return b.asm._emscripten_bind_btStaticPlaneShape___destroy___0.apply(null, arguments) }, Wu = b._emscripten_bind_btHingeConstraint_enableMotor_1 = function () { return b.asm._emscripten_bind_btHingeConstraint_enableMotor_1.apply(null, arguments) }, Xu = b._emscripten_bind_btCylinderShapeZ_setLocalScaling_1 = function () {
                        return b.asm._emscripten_bind_btCylinderShapeZ_setLocalScaling_1.apply(null,
                            arguments)
                    }, Yu = b._emscripten_bind_btBoxShape_setLocalScaling_1 = function () { return b.asm._emscripten_bind_btBoxShape_setLocalScaling_1.apply(null, arguments) }, Zu = b._emscripten_bind_btConeShapeZ___destroy___0 = function () { return b.asm._emscripten_bind_btConeShapeZ___destroy___0.apply(null, arguments) }, $u = b._emscripten_bind_btDynamicsWorld_getPairCache_0 = function () { return b.asm._emscripten_bind_btDynamicsWorld_getPairCache_0.apply(null, arguments) }, av = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelAxleCS_0 =
                        function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelAxleCS_0.apply(null, arguments) }, bv = b._emscripten_bind_btDiscreteDynamicsWorld_convexSweepTest_5 = function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_convexSweepTest_5.apply(null, arguments) }, cv = b._emscripten_bind_btSoftRigidDynamicsWorld_removeRigidBody_1 = function () { return b.asm._emscripten_bind_btSoftRigidDynamicsWorld_removeRigidBody_1.apply(null, arguments) }, dv = b._emscripten_bind_btRigidBody_setRestitution_1 =
                            function () { return b.asm._emscripten_bind_btRigidBody_setRestitution_1.apply(null, arguments) }, ev = b._emscripten_bind_btVector4_btVector4_0 = function () { return b.asm._emscripten_bind_btVector4_btVector4_0.apply(null, arguments) }, fv = b._emscripten_bind_btVector4_x_0 = function () { return b.asm._emscripten_bind_btVector4_x_0.apply(null, arguments) }, gv = b._emscripten_bind_btVector4_btVector4_4 = function () { return b.asm._emscripten_bind_btVector4_btVector4_4.apply(null, arguments) }, hv = b._emscripten_bind_btKinematicCharacterController___destroy___0 =
                                function () { return b.asm._emscripten_bind_btKinematicCharacterController___destroy___0.apply(null, arguments) }, iv = b._emscripten_bind_btGeneric6DofSpringConstraint_setLinearLowerLimit_1 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setLinearLowerLimit_1.apply(null, arguments) }, jv = b._emscripten_bind_tMaterialArray_at_1 = function () { return b.asm._emscripten_bind_tMaterialArray_at_1.apply(null, arguments) }, kv = b._emscripten_bind_LocalConvexResult_set_m_hitCollisionObject_1 = function () {
                                    return b.asm._emscripten_bind_LocalConvexResult_set_m_hitCollisionObject_1.apply(null,
                                        arguments)
                                }, lv = b._emscripten_bind_btVector4_op_sub_1 = function () { return b.asm._emscripten_bind_btVector4_op_sub_1.apply(null, arguments) }, mv = b._emscripten_bind_btGeneric6DofSpringConstraint_setAngularLowerLimit_1 = function () { return b.asm._emscripten_bind_btGeneric6DofSpringConstraint_setAngularLowerLimit_1.apply(null, arguments) }, nv = b._emscripten_bind_btSoftBodyWorldInfo_get_water_offset_0 = function () { return b.asm._emscripten_bind_btSoftBodyWorldInfo_get_water_offset_0.apply(null, arguments) }, ov = b._emscripten_bind_btDiscreteDynamicsWorld_rayTest_3 =
                                    function () { return b.asm._emscripten_bind_btDiscreteDynamicsWorld_rayTest_3.apply(null, arguments) }, pv = b._emscripten_bind_btWheelInfo_get_m_raycastInfo_0 = function () { return b.asm._emscripten_bind_btWheelInfo_get_m_raycastInfo_0.apply(null, arguments) }, qv = b._emscripten_bind_btContactSolverInfo_get_m_splitImpulse_0 = function () { return b.asm._emscripten_bind_btContactSolverInfo_get_m_splitImpulse_0.apply(null, arguments) }, rv = b._emscripten_bind_btConvexShape_getMargin_0 = function () {
                                        return b.asm._emscripten_bind_btConvexShape_getMargin_0.apply(null,
                                            arguments)
                                    }, sv = b._emscripten_bind_btRaycastVehicle_getSteeringValue_1 = function () { return b.asm._emscripten_bind_btRaycastVehicle_getSteeringValue_1.apply(null, arguments) }, tv = b._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelRadius_0 = function () { return b.asm._emscripten_bind_btWheelInfoConstructionInfo_get_m_wheelRadius_0.apply(null, arguments) }, uv = b._emscripten_bind_btKinematicCharacterController_setMaxJumpHeight_1 = function () {
                                        return b.asm._emscripten_bind_btKinematicCharacterController_setMaxJumpHeight_1.apply(null,
                                            arguments)
                                    }, vv = b._emscripten_bind_btPairCachingGhostObject_isActive_0 = function () { return b.asm._emscripten_bind_btPairCachingGhostObject_isActive_0.apply(null, arguments) }, wv = b._emscripten_bind_btWheelInfo_set_m_wheelDirectionCS_1 = function () { return b.asm._emscripten_bind_btWheelInfo_set_m_wheelDirectionCS_1.apply(null, arguments) }, xv = b._emscripten_bind_btVehicleTuning_get_m_frictionSlip_0 = function () { return b.asm._emscripten_bind_btVehicleTuning_get_m_frictionSlip_0.apply(null, arguments) };
    b.dynCall_viiiii = function () { return b.asm.dynCall_viiiii.apply(null, arguments) }; b.dynCall_viiiifffffifi = function () { return b.asm.dynCall_viiiifffffifi.apply(null, arguments) }; b.dynCall_vif = function () { return b.asm.dynCall_vif.apply(null, arguments) }; b.dynCall_viifii = function () { return b.asm.dynCall_viifii.apply(null, arguments) }; b.dynCall_vi = function () { return b.asm.dynCall_vi.apply(null, arguments) }; b.dynCall_vii = function () { return b.asm.dynCall_vii.apply(null, arguments) };
    b.dynCall_iiiiiiiiiii = function () { return b.asm.dynCall_iiiiiiiiiii.apply(null, arguments) }; b.dynCall_viiiif = function () { return b.asm.dynCall_viiiif.apply(null, arguments) }; b.dynCall_ii = function () { return b.asm.dynCall_ii.apply(null, arguments) }; b.dynCall_viifi = function () { return b.asm.dynCall_viifi.apply(null, arguments) }; b.dynCall_viiiiiiiii = function () { return b.asm.dynCall_viiiiiiiii.apply(null, arguments) }; b.dynCall_viiif = function () { return b.asm.dynCall_viiif.apply(null, arguments) };
    b.dynCall_viffiii = function () { return b.asm.dynCall_viffiii.apply(null, arguments) }; b.dynCall_iiiii = function () { return b.asm.dynCall_iiiii.apply(null, arguments) }; b.dynCall_viiifii = function () { return b.asm.dynCall_viiifii.apply(null, arguments) }; b.dynCall_fiifii = function () { return b.asm.dynCall_fiifii.apply(null, arguments) }; b.dynCall_fiiiiiiiii = function () { return b.asm.dynCall_fiiiiiiiii.apply(null, arguments) }; b.dynCall_iiii = function () { return b.asm.dynCall_iiii.apply(null, arguments) };
    b.dynCall_fif = function () { return b.asm.dynCall_fif.apply(null, arguments) }; b.dynCall_viff = function () { return b.asm.dynCall_viff.apply(null, arguments) }; b.dynCall_vifi = function () { return b.asm.dynCall_vifi.apply(null, arguments) }; b.dynCall_viiiiif = function () { return b.asm.dynCall_viiiiif.apply(null, arguments) }; b.dynCall_viiiiii = function () { return b.asm.dynCall_viiiiii.apply(null, arguments) }; b.dynCall_iiif = function () { return b.asm.dynCall_iiif.apply(null, arguments) };
    b.dynCall_fiii = function () { return b.asm.dynCall_fiii.apply(null, arguments) }; b.dynCall_iiiiiii = function () { return b.asm.dynCall_iiiiiii.apply(null, arguments) }; b.dynCall_fiiiiiiiiii = function () { return b.asm.dynCall_fiiiiiiiiii.apply(null, arguments) }; b.dynCall_fiiiii = function () { return b.asm.dynCall_fiiiii.apply(null, arguments) }; b.dynCall_viiiiiii = function () { return b.asm.dynCall_viiiiiii.apply(null, arguments) }; b.dynCall_vifii = function () { return b.asm.dynCall_vifii.apply(null, arguments) };
    b.dynCall_fi = function () { return b.asm.dynCall_fi.apply(null, arguments) }; b.dynCall_viiiiiiiiii = function () { return b.asm.dynCall_viiiiiiiiii.apply(null, arguments) }; b.dynCall_iii = function () { return b.asm.dynCall_iii.apply(null, arguments) }; b.dynCall_fiiiiiiii = function () { return b.asm.dynCall_fiiiiiiii.apply(null, arguments) }; b.dynCall_iifif = function () { return b.asm.dynCall_iifif.apply(null, arguments) }; b.dynCall_viiiiffffiif = function () { return b.asm.dynCall_viiiiffffiif.apply(null, arguments) };
    b.dynCall_fiiii = function () { return b.asm.dynCall_fiiii.apply(null, arguments) }; b.dynCall_iiiiiiiiii = function () { return b.asm.dynCall_iiiiiiiiii.apply(null, arguments) }; b.dynCall_viii = function () { return b.asm.dynCall_viii.apply(null, arguments) }; b.dynCall_v = function () { return b.asm.dynCall_v.apply(null, arguments) }; b.dynCall_viif = function () { return b.asm.dynCall_viif.apply(null, arguments) }; b.dynCall_fiiifii = function () { return b.asm.dynCall_fiiifii.apply(null, arguments) };
    b.dynCall_viiii = function () { return b.asm.dynCall_viiii.apply(null, arguments) }; k.t = b.stackAlloc; k.N = b.stackSave; k.M = b.stackRestore; k.S = b.establishStackSpace; k.e = b.setTempRet0; k.J = b.getTempRet0; b.asm = Jb;
    if (lb) if ("function" === typeof b.locateFile ? lb = b.locateFile(lb) : b.memoryInitializerPrefixURL && (lb = b.memoryInitializerPrefixURL + lb), ea || fa) { var yv = b.readBinary(lb); Aa.set(yv, k.i) } else {
        var Av = function () { b.readAsync(lb, zv, function () { throw "could not load memory initializer " + lb; }) }; jb(); var zv = function (a) { a.byteLength && (a = new Uint8Array(a)); Aa.set(a, k.i); b.memoryInitializerRequest && delete b.memoryInitializerRequest.response; kb() }; if (b.memoryInitializerRequest) {
            var Bv = function () {
                var a = b.memoryInitializerRequest;
                200 !== a.status && 0 !== a.status ? (console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: " + a.status + ", retrying " + lb), Av()) : zv(a.response)
            }; b.memoryInitializerRequest.response ? setTimeout(Bv, 0) : b.memoryInitializerRequest.addEventListener("load", Bv)
        } else Av()
    } b.then = function (a) { if (b.calledRun) a(b); else { var c = b.onRuntimeInitialized; b.onRuntimeInitialized = function () { c && c(); a(b) } } return b };
    function ka(a) { this.name = "ExitStatus"; this.message = "Program terminated with exit(" + a + ")"; this.status = a } ka.prototype = Error(); ka.prototype.constructor = ka; var Cv = null, ib = function Dv() { b.calledRun || Ev(); b.calledRun || (ib = Dv) };
    b.callMain = b.Q = function (a) { function c() { for (var a = 0; 3 > a; a++)e.push(0) } a = a || []; ab || (ab = !0, Va(Xa)); var d = a.length + 1, e = [ya(cb(b.thisProgram), "i8", 0)]; c(); for (var f = 0; f < d - 1; f += 1)e.push(ya(cb(a[f]), "i8", 0)), c(); e.push(0); e = ya(e, "i32", 0); try { var g = b._main(d, e, 0); Fv(g, !0) } catch (h) { h instanceof ka || ("SimulateInfiniteLoop" == h ? b.noExitRuntime = !0 : ((a = h) && "object" === typeof h && h.stack && (a = [h, h.stack]), b.h("exception thrown: " + a), b.quit(1, h))) } finally { } };
    function Ev(a) {
        function c() { if (!b.calledRun && (b.calledRun = !0, !ra)) { ab || (ab = !0, Va(Xa)); Va(Ya); if (b.onRuntimeInitialized) b.onRuntimeInitialized(); b._main && Gv && b.callMain(a); if (b.postRun) for ("function" == typeof b.postRun && (b.postRun = [b.postRun]); b.postRun.length;) { var c = b.postRun.shift(); $a.unshift(c) } Va($a) } } a = a || b.arguments; null === Cv && (Cv = Date.now()); if (!(0 < gb)) {
            if (b.preRun) for ("function" == typeof b.preRun && (b.preRun = [b.preRun]); b.preRun.length;)bb(); Va(Wa); 0 < gb || b.calledRun || (b.setStatus ? (b.setStatus("Running..."),
                setTimeout(function () { setTimeout(function () { b.setStatus("") }, 1); c() }, 1)) : c())
        }
    } b.run = b.run = Ev; function Fv(a, c) { if (!c || !b.noExitRuntime) { if (!b.noExitRuntime && (ra = !0, la = void 0, Va(Za), b.onExit)) b.onExit(a); ea && process.exit(a); b.quit(a, new ka(a)) } } b.exit = b.exit = Fv; var Hv = [];
    function sa(a) { void 0 !== a ? (b.print(a), b.h(a), a = JSON.stringify(a)) : a = ""; ra = !0; var c = "abort(" + a + ") at " + La() + "\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information."; Hv && Hv.forEach(function (d) { c = d(c, a) }); throw c; } b.abort = b.abort = sa; if (b.preInit) for ("function" == typeof b.preInit && (b.preInit = [b.preInit]); 0 < b.preInit.length;)b.preInit.pop()(); var Gv = !0; b.noInitialRun && (Gv = !1); b.noExitRuntime = !0; Ev(); function p() { } p.prototype = Object.create(p.prototype);
    p.prototype.constructor = p; p.prototype.b = p; p.c = {}; b.WrapperObject = p; function q(a) { return (a || p).c } b.getCache = q; function r(a, c) { var d = q(c), e = d[a]; if (e) return e; e = Object.create((c || p).prototype); e.a = a; return d[a] = e } b.wrapPointer = r; b.castObject = function (a, c) { return r(a.a, c) }; b.NULL = r(0); b.destroy = function (a) { if (!a.__destroy__) throw "Error: Cannot destroy object. (Did you create it yourself?)"; a.__destroy__(); delete q(a.b)[a.a] }; b.compare = function (a, c) { return a.a === c.a }; b.getPointer = function (a) { return a.a };
    b.getClass = function (a) { return a.b }; var Iv = 0, Jv = 0, Kv = 0, Lv = [], Mv = 0; function Nv() { if (Mv) { for (var a = 0; a < Lv.length; a++)b._free(Lv[a]); Lv.length = 0; b._free(Iv); Iv = 0; Jv += Mv; Mv = 0 } Iv || (Jv += 128, Iv = b._malloc(Jv), assert(Iv)); Kv = 0 } function Ov(a, c) { assert(Iv); var d = a.length * c.BYTES_PER_ELEMENT, d = d + 7 & -8, e; Kv + d >= Jv ? (assert(0 < d), Mv += d, e = b._malloc(d), Lv.push(e)) : (e = Iv + Kv, Kv += d); return e }
    function Pv(a, c, d) { switch (c.BYTES_PER_ELEMENT) { case 2: d >>= 1; break; case 4: d >>= 2; break; case 8: d >>= 3 }for (var e = 0; e < a.length; e++)c[d + e] = a[e] } function Qv(a) { if ("object" === typeof a) { var c = Ov(a, wa); Pv(a, wa, c); return c } return a } function Rv() { throw "cannot construct a btCollisionWorld, no constructor in IDL"; } Rv.prototype = Object.create(p.prototype); Rv.prototype.constructor = Rv; Rv.prototype.b = Rv; Rv.c = {}; b.btCollisionWorld = Rv; Rv.prototype.getDispatcher = function () { return r(jj(this.a), Sv) };
    Rv.prototype.rayTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Vf(e, a, c, d) }; Rv.prototype.getPairCache = function () { return r(Mn(this.a), Tv) }; Rv.prototype.getDispatchInfo = function () { return r(vi(this.a), t) }; Rv.prototype.addCollisionObject = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? yl(e, a) : void 0 === d ? zl(e, a, c) : Xi(e, a, c, d) };
    Rv.prototype.getBroadphase = function () { return r(bi(this.a), Uv) }; Rv.prototype.convexSweepTest = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); yf(g, a, c, d, e, f) }; Rv.prototype.contactPairTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Pr(e, a, c, d) };
    Rv.prototype.contactTest = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); lg(d, a, c) }; Rv.prototype.__destroy__ = function () { Od(this.a) }; function u() { throw "cannot construct a btCollisionShape, no constructor in IDL"; } u.prototype = Object.create(p.prototype); u.prototype.constructor = u; u.prototype.b = u; u.c = {}; b.btCollisionShape = u; u.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zi(c, a) };
    u.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); vf(d, a, c) }; u.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qe(c, a) }; u.prototype.getMargin = function () { return Uo(this.a) }; u.prototype.__destroy__ = function () { no(this.a) }; function w() { throw "cannot construct a btCollisionObject, no constructor in IDL"; } w.prototype = Object.create(p.prototype); w.prototype.constructor = w; w.prototype.b = w; w.c = {};
    b.btCollisionObject = w; w.prototype.setAnisotropicFriction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); tn(d, a, c) }; w.prototype.getCollisionShape = function () { return r(Lm(this.a), u) }; w.prototype.setContactProcessingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pi(c, a) }; w.prototype.setActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mf(c, a) };
    w.prototype.forceActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ir(c, a) }; w.prototype.activate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); void 0 === a ? cd(c) : bd(c, a) }; w.prototype.isActive = function () { return !!Tb(this.a) }; w.prototype.isKinematicObject = function () { return !!Oc(this.a) }; w.prototype.setRestitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Sk(c, a) }; w.prototype.setFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dm(c, a) };
    w.prototype.setRollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mj(c, a) }; w.prototype.getWorldTransform = function () { return r(Ek(this.a), y) }; w.prototype.getCollisionFlags = function () { return zi(this.a) }; w.prototype.setCollisionFlags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hq(c, a) }; w.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ur(c, a) };
    w.prototype.setCollisionShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mo(c, a) }; w.prototype.setCcdMotionThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jp(c, a) }; w.prototype.setCcdSweptSphereRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gt(c, a) }; w.prototype.getUserIndex = function () { return ic(this.a) }; w.prototype.setUserIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); In(c, a) };
    w.prototype.getUserPointer = function () { return r(jg(this.a), Vv) }; w.prototype.setUserPointer = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ml(c, a) }; w.prototype.__destroy__ = function () { Go(this.a) }; function z() { throw "cannot construct a btDynamicsWorld, no constructor in IDL"; } z.prototype = Object.create(Rv.prototype); z.prototype.constructor = z; z.prototype.b = z; z.c = {}; b.btDynamicsWorld = z; z.prototype.addAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Rt(c, a) };
    z.prototype.removeAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); gs(c, a) }; z.prototype.getSolverInfo = function () { return r(Cm(this.a), Wv) }; z.prototype.getDispatcher = function () { return r(sj(this.a), Sv) }; z.prototype.rayTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Kc(e, a, c, d) }; z.prototype.getPairCache = function () { return r($u(this.a), Tv) }; z.prototype.getDispatchInfo = function () { return r(fe(this.a), t) };
    z.prototype.addCollisionObject = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? $l(e, a) : void 0 === d ? bm(e, a, c) : pe(e, a, c, d) }; z.prototype.getBroadphase = function () { return r(Gm(this.a), Uv) };
    z.prototype.convexSweepTest = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); Iu(g, a, c, d, e, f) }; z.prototype.contactPairTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Ln(e, a, c, d) };
    z.prototype.contactTest = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Ll(d, a, c) }; z.prototype.__destroy__ = function () { bh(this.a) }; function Xv() { throw "cannot construct a btTypedConstraint, no constructor in IDL"; } Xv.prototype = Object.create(p.prototype); Xv.prototype.constructor = Xv; Xv.prototype.b = Xv; Xv.c = {}; b.btTypedConstraint = Xv; Xv.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); op(c, a) };
    Xv.prototype.getBreakingImpulseThreshold = function () { return rn(this.a) }; Xv.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dc(c, a) }; Xv.prototype.__destroy__ = function () { ml(this.a) }; function Yv() { throw "cannot construct a btConcaveShape, no constructor in IDL"; } Yv.prototype = Object.create(u.prototype); Yv.prototype.constructor = Yv; Yv.prototype.b = Yv; Yv.c = {}; b.btConcaveShape = Yv;
    Yv.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Mh(c, a) }; Yv.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Mj(d, a, c) }; Yv.prototype.__destroy__ = function () { Eh(this.a) }; function Zv(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = np(a, c); q(Zv)[this.a] = this } Zv.prototype = Object.create(u.prototype); Zv.prototype.constructor = Zv; Zv.prototype.b = Zv; Zv.c = {};
    b.btCapsuleShape = Zv; Zv.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jl(c, a) }; Zv.prototype.getMargin = function () { return Sp(this.a) }; Zv.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jg(c, a) }; Zv.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Qq(d, a, c) }; Zv.prototype.__destroy__ = function () { $c(this.a) };
    function $v(a) { a && "object" === typeof a && (a = a.a); this.a = void 0 === a ? Fi() : ad(a); q($v)[this.a] = this } $v.prototype = Object.create(p.prototype); $v.prototype.constructor = $v; $v.prototype.b = $v; $v.c = {}; b.btDefaultCollisionConfiguration = $v; $v.prototype.__destroy__ = function () { Wm(this.a) }; function aw() { throw "cannot construct a btTriangleMeshShape, no constructor in IDL"; } aw.prototype = Object.create(Yv.prototype); aw.prototype.constructor = aw; aw.prototype.b = aw; aw.c = {}; b.btTriangleMeshShape = aw;
    aw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jo(c, a) }; aw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Vj(d, a, c) }; aw.prototype.__destroy__ = function () { ds(this.a) }; function bw() { throw "cannot construct a RayResultCallback, no constructor in IDL"; } bw.prototype = Object.create(p.prototype); bw.prototype.constructor = bw; bw.prototype.b = bw; bw.c = {}; b.RayResultCallback = bw; bw.prototype.hasHit = function () { return !!ak(this.a) };
    bw.prototype.get_m_collisionFilterGroup = function () { return io(this.a) }; bw.prototype.set_m_collisionFilterGroup = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); oj(c, a) }; bw.prototype.get_m_collisionFilterMask = function () { return ij(this.a) }; bw.prototype.set_m_collisionFilterMask = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); le(c, a) }; bw.prototype.get_m_collisionObject = function () { return r(Sl(this.a), w) };
    bw.prototype.set_m_collisionObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lt(c, a) }; bw.prototype.__destroy__ = function () { ph(this.a) }; function cw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = Lg(a, c); q(cw)[this.a] = this } cw.prototype = Object.create(u.prototype); cw.prototype.constructor = cw; cw.prototype.b = cw; cw.c = {}; b.btConeShape = cw; cw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); lo(c, a) };
    cw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); ys(d, a, c) }; cw.prototype.__destroy__ = function () { zq(this.a) }; function dw() { throw "cannot construct a btActionInterface, no constructor in IDL"; } dw.prototype = Object.create(p.prototype); dw.prototype.constructor = dw; dw.prototype.b = dw; dw.c = {}; b.btActionInterface = dw;
    dw.prototype.updateAction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); nd(d, a, c) }; dw.prototype.__destroy__ = function () { Ol(this.a) }; function A(a, c, d) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); this.a = void 0 === a ? Po() : void 0 === c ? _emscripten_bind_btVector3_btVector3_1(a) : void 0 === d ? _emscripten_bind_btVector3_btVector3_2(a, c) : Oo(a, c, d); q(A)[this.a] = this } A.prototype = Object.create(p.prototype);
    A.prototype.constructor = A; A.prototype.b = A; A.c = {}; b.btVector3 = A; A.prototype.length = A.prototype.length = function () { return mr(this.a) }; A.prototype.x = function () { return Jm(this.a) }; A.prototype.y = function () { return Sq(this.a) }; A.prototype.z = function () { return So(this.a) }; A.prototype.setX = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jd(c, a) }; A.prototype.setY = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ch(c, a) };
    A.prototype.setZ = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); sd(c, a) }; A.prototype.setValue = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Ym(e, a, c, d) }; A.prototype.normalize = function () { pr(this.a) }; A.prototype.rotate = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); return r(fm(d, a, c), A) };
    A.prototype.dot = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return Qg(c, a) }; A.prototype.op_mul = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Dh(c, a), A) }; A.prototype.op_add = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(uu(c, a), A) }; A.prototype.op_sub = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(ef(c, a), A) }; A.prototype.__destroy__ = function () { um(this.a) };
    function ew() { throw "cannot construct a btVehicleRaycaster, no constructor in IDL"; } ew.prototype = Object.create(p.prototype); ew.prototype.constructor = ew; ew.prototype.b = ew; ew.c = {}; b.btVehicleRaycaster = ew; ew.prototype.castRay = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); pj(e, a, c, d) }; ew.prototype.__destroy__ = function () { xg(this.a) }; function fw() { throw "cannot construct a btQuadWord, no constructor in IDL"; } fw.prototype = Object.create(p.prototype);
    fw.prototype.constructor = fw; fw.prototype.b = fw; fw.c = {}; b.btQuadWord = fw; fw.prototype.x = function () { return Mc(this.a) }; fw.prototype.y = function () { return rg(this.a) }; fw.prototype.z = function () { return oc(this.a) }; fw.prototype.w = function () { return Bg(this.a) }; fw.prototype.setX = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fh(c, a) }; fw.prototype.setY = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Bd(c, a) }; fw.prototype.setZ = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vm(c, a) };
    fw.prototype.setW = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Be(c, a) }; fw.prototype.__destroy__ = function () { ap(this.a) }; function gw(a) { a && "object" === typeof a && (a = a.a); this.a = ci(a); q(gw)[this.a] = this } gw.prototype = Object.create(u.prototype); gw.prototype.constructor = gw; gw.prototype.b = gw; gw.c = {}; b.btCylinderShape = gw; gw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Md(c, a) }; gw.prototype.getMargin = function () { return Tp(this.a) };
    gw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); si(c, a) }; gw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Wl(d, a, c) }; gw.prototype.__destroy__ = function () { Kb(this.a) }; function B(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = Wb(a, c, d, e); q(B)[this.a] = this } B.prototype = Object.create(z.prototype);
    B.prototype.constructor = B; B.prototype.b = B; B.c = {}; b.btDiscreteDynamicsWorld = B; B.prototype.setGravity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fr(c, a) }; B.prototype.getGravity = function () { return r(Ro(this.a), A) }; B.prototype.addRigidBody = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? ql(e, a) : void 0 === d ? _emscripten_bind_btDiscreteDynamicsWorld_addRigidBody_2(e, a, c) : pl(e, a, c, d) };
    B.prototype.removeRigidBody = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dg(c, a) }; B.prototype.addConstraint = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); void 0 === c ? Te(d, a) : Se(d, a, c) }; B.prototype.removeConstraint = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Us(c, a) };
    B.prototype.stepSimulation = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); return void 0 === c ? Gc(e, a) : void 0 === d ? Ic(e, a, c) : Hc(e, a, c, d) }; B.prototype.getDispatcher = function () { return r(cm(this.a), Sv) }; B.prototype.rayTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); ov(e, a, c, d) }; B.prototype.getPairCache = function () { return r($p(this.a), Tv) };
    B.prototype.getDispatchInfo = function () { return r(Nh(this.a), t) }; B.prototype.addCollisionObject = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? jq(e, a) : void 0 === d ? kq(e, a, c) : lq(e, a, c, d) }; B.prototype.getBroadphase = function () { return r(fg(this.a), Uv) };
    B.prototype.convexSweepTest = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); bv(g, a, c, d, e, f) }; B.prototype.contactPairTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Dt(e, a, c, d) };
    B.prototype.contactTest = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Eg(d, a, c) }; B.prototype.addAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); gg(c, a) }; B.prototype.removeAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gs(c, a) }; B.prototype.getSolverInfo = function () { return r(Ld(this.a), Wv) }; B.prototype.__destroy__ = function () { Jk(this.a) }; function hw() { throw "cannot construct a btConvexShape, no constructor in IDL"; }
    hw.prototype = Object.create(u.prototype); hw.prototype.constructor = hw; hw.prototype.b = hw; hw.c = {}; b.btConvexShape = hw; hw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tj(c, a) }; hw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); qt(d, a, c) }; hw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Er(c, a) }; hw.prototype.getMargin = function () { return rv(this.a) };
    hw.prototype.__destroy__ = function () { Kk(this.a) }; function Sv() { throw "cannot construct a btDispatcher, no constructor in IDL"; } Sv.prototype = Object.create(p.prototype); Sv.prototype.constructor = Sv; Sv.prototype.b = Sv; Sv.c = {}; b.btDispatcher = Sv; Sv.prototype.getNumManifolds = function () { return Dr(this.a) }; Sv.prototype.getManifoldByIndexInternal = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(oq(c, a), iw) }; Sv.prototype.__destroy__ = function () { Hk(this.a) };
    function jw(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = void 0 === e ? Xd(a, c, d) : void 0 === f ? _emscripten_bind_btGeneric6DofConstraint_btGeneric6DofConstraint_4(a, c, d, e) : Wd(a, c, d, e, f); q(jw)[this.a] = this } jw.prototype = Object.create(Xv.prototype); jw.prototype.constructor = jw; jw.prototype.b = jw; jw.c = {}; b.btGeneric6DofConstraint = jw;
    jw.prototype.setLinearLowerLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); rt(c, a) }; jw.prototype.setLinearUpperLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yj(c, a) }; jw.prototype.setAngularLowerLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ss(c, a) }; jw.prototype.setAngularUpperLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Re(c, a) }; jw.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lb(c, a) };
    jw.prototype.getBreakingImpulseThreshold = function () { return lr(this.a) }; jw.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Rn(c, a) }; jw.prototype.__destroy__ = function () { Ys(this.a) }; function kw() { throw "cannot construct a btStridingMeshInterface, no constructor in IDL"; } kw.prototype = Object.create(p.prototype); kw.prototype.constructor = kw; kw.prototype.b = kw; kw.c = {}; b.btStridingMeshInterface = kw; kw.prototype.__destroy__ = function () { yr(this.a) };
    function lw() { throw "cannot construct a btMotionState, no constructor in IDL"; } lw.prototype = Object.create(p.prototype); lw.prototype.constructor = lw; lw.prototype.b = lw; lw.c = {}; b.btMotionState = lw; lw.prototype.getWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Bm(c, a) }; lw.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xd(c, a) }; lw.prototype.__destroy__ = function () { gq(this.a) };
    function mw() { throw "cannot construct a ConvexResultCallback, no constructor in IDL"; } mw.prototype = Object.create(p.prototype); mw.prototype.constructor = mw; mw.prototype.b = mw; mw.c = {}; b.ConvexResultCallback = mw; mw.prototype.hasHit = function () { return !!uf(this.a) }; mw.prototype.get_m_collisionFilterGroup = function () { return Yk(this.a) }; mw.prototype.set_m_collisionFilterGroup = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); yn(c, a) }; mw.prototype.get_m_collisionFilterMask = function () { return Bp(this.a) };
    mw.prototype.set_m_collisionFilterMask = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); As(c, a) }; mw.prototype.get_m_closestHitFraction = function () { return po(this.a) }; mw.prototype.set_m_closestHitFraction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); yp(c, a) }; mw.prototype.__destroy__ = function () { Ji(this.a) }; function nw() { throw "cannot construct a ContactResultCallback, no constructor in IDL"; } nw.prototype = Object.create(p.prototype); nw.prototype.constructor = nw; nw.prototype.b = nw;
    nw.c = {}; b.ContactResultCallback = nw; nw.prototype.addSingleResult = function (a, c, d, e, f, g, h) { var l = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); return Dd(l, a, c, d, e, f, g, h) }; nw.prototype.__destroy__ = function () { Xr(this.a) }; function ow() { throw "cannot construct a btSoftBodySolver, no constructor in IDL"; } ow.prototype = Object.create(p.prototype);
    ow.prototype.constructor = ow; ow.prototype.b = ow; ow.c = {}; b.btSoftBodySolver = ow; ow.prototype.__destroy__ = function () { sq(this.a) }; function C() { this.a = Kg(); q(C)[this.a] = this } C.prototype = Object.create(w.prototype); C.prototype.constructor = C; C.prototype.b = C; C.c = {}; b.btGhostObject = C; C.prototype.getNumOverlappingObjects = function () { return Jq(this.a) }; C.prototype.getOverlappingObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(nu(c, a), w) };
    C.prototype.setAnisotropicFriction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); kp(d, a, c) }; C.prototype.getCollisionShape = function () { return r(sn(this.a), u) }; C.prototype.setContactProcessingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $h(c, a) }; C.prototype.setActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nr(c, a) };
    C.prototype.forceActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Un(c, a) }; C.prototype.activate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); void 0 === a ? qp(c) : An(c, a) }; C.prototype.isActive = function () { return !!cu(this.a) }; C.prototype.isKinematicObject = function () { return !!Xc(this.a) }; C.prototype.setRestitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tm(c, a) }; C.prototype.setFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Uf(c, a) };
    C.prototype.setRollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pk(c, a) }; C.prototype.getWorldTransform = function () { return r(Zp(this.a), y) }; C.prototype.getCollisionFlags = function () { return an(this.a) }; C.prototype.setCollisionFlags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); tj(c, a) }; C.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $f(c, a) };
    C.prototype.setCollisionShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); br(c, a) }; C.prototype.setCcdMotionThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qn(c, a) }; C.prototype.setCcdSweptSphereRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Aq(c, a) }; C.prototype.getUserIndex = function () { return rm(this.a) }; C.prototype.setUserIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qm(c, a) };
    C.prototype.getUserPointer = function () { return r(Rg(this.a), Vv) }; C.prototype.setUserPointer = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ms(c, a) }; C.prototype.__destroy__ = function () { Ob(this.a) }; function pw() { throw "cannot construct a btMatrix3x3, no constructor in IDL"; } pw.prototype = Object.create(p.prototype); pw.prototype.constructor = pw; pw.prototype.b = pw; pw.c = {}; b.btMatrix3x3 = pw;
    pw.prototype.setEulerZYX = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); bj(e, a, c, d) }; pw.prototype.getRotation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uj(c, a) }; pw.prototype.getRow = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(oo(c, a), A) }; pw.prototype.__destroy__ = function () { rf(this.a) }; function t() { throw "cannot construct a btDispatcherInfo, no constructor in IDL"; } t.prototype = Object.create(p.prototype);
    t.prototype.constructor = t; t.prototype.b = t; t.c = {}; b.btDispatcherInfo = t; t.prototype.get_m_timeStep = function () { return cq(this.a) }; t.prototype.set_m_timeStep = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); No(c, a) }; t.prototype.get_m_stepCount = function () { return ze(this.a) }; t.prototype.set_m_stepCount = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); di(c, a) }; t.prototype.get_m_dispatchFunc = function () { return ro(this.a) };
    t.prototype.set_m_dispatchFunc = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vd(c, a) }; t.prototype.get_m_timeOfImpact = function () { return gc(this.a) }; t.prototype.set_m_timeOfImpact = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hd(c, a) }; t.prototype.get_m_useContinuous = function () { return !!Qr(this.a) }; t.prototype.set_m_useContinuous = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Sb(c, a) }; t.prototype.get_m_enableSatConvex = function () { return !!Os(this.a) };
    t.prototype.set_m_enableSatConvex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ai(c, a) }; t.prototype.get_m_enableSPU = function () { return !!Du(this.a) }; t.prototype.set_m_enableSPU = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hf(c, a) }; t.prototype.get_m_useEpa = function () { return !!Ze(this.a) }; t.prototype.set_m_useEpa = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tq(c, a) }; t.prototype.get_m_allowedCcdPenetration = function () { return jc(this.a) };
    t.prototype.set_m_allowedCcdPenetration = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); cr(c, a) }; t.prototype.get_m_useConvexConservativeDistanceUtil = function () { return !!Yr(this.a) }; t.prototype.set_m_useConvexConservativeDistanceUtil = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); He(c, a) }; t.prototype.get_m_convexConservativeDistanceThreshold = function () { return dj(this.a) };
    t.prototype.set_m_convexConservativeDistanceThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wk(c, a) }; t.prototype.__destroy__ = function () { Rp(this.a) }; function qw() { throw "cannot construct a Material, no constructor in IDL"; } qw.prototype = Object.create(p.prototype); qw.prototype.constructor = qw; qw.prototype.b = qw; qw.c = {}; b.Material = qw; qw.prototype.get_m_kLST = function () { return jt(this.a) }; qw.prototype.set_m_kLST = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Or(c, a) };
    qw.prototype.get_m_kAST = function () { return mh(this.a) }; qw.prototype.set_m_kAST = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ok(c, a) }; qw.prototype.get_m_kVST = function () { return om(this.a) }; qw.prototype.set_m_kVST = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Mr(c, a) }; qw.prototype.get_m_flags = function () { return en(this.a) }; qw.prototype.set_m_flags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hi(c, a) }; qw.prototype.__destroy__ = function () { qc(this.a) };
    function D() { throw "cannot construct a btWheelInfoConstructionInfo, no constructor in IDL"; } D.prototype = Object.create(p.prototype); D.prototype.constructor = D; D.prototype.b = D; D.c = {}; b.btWheelInfoConstructionInfo = D; D.prototype.get_m_chassisConnectionCS = function () { return r(ll(this.a), A) }; D.prototype.set_m_chassisConnectionCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Dl(c, a) }; D.prototype.get_m_wheelDirectionCS = function () { return r(Jt(this.a), A) };
    D.prototype.set_m_wheelDirectionCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mm(c, a) }; D.prototype.get_m_wheelAxleCS = function () { return r(av(this.a), A) }; D.prototype.set_m_wheelAxleCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nm(c, a) }; D.prototype.get_m_suspensionRestLength = function () { return Kd(this.a) }; D.prototype.set_m_suspensionRestLength = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); du(c, a) }; D.prototype.get_m_maxSuspensionTravelCm = function () { return Ss(this.a) };
    D.prototype.set_m_maxSuspensionTravelCm = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jl(c, a) }; D.prototype.get_m_wheelRadius = function () { return tv(this.a) }; D.prototype.set_m_wheelRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xl(c, a) }; D.prototype.get_m_suspensionStiffness = function () { return We(this.a) }; D.prototype.set_m_suspensionStiffness = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); se(c, a) }; D.prototype.get_m_wheelsDampingCompression = function () { return jf(this.a) };
    D.prototype.set_m_wheelsDampingCompression = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dn(c, a) }; D.prototype.get_m_wheelsDampingRelaxation = function () { return Sr(this.a) }; D.prototype.set_m_wheelsDampingRelaxation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ub(c, a) }; D.prototype.get_m_frictionSlip = function () { return pp(this.a) }; D.prototype.set_m_frictionSlip = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ar(c, a) }; D.prototype.get_m_maxSuspensionForce = function () { return gd(this.a) };
    D.prototype.set_m_maxSuspensionForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Kr(c, a) }; D.prototype.get_m_bIsFrontWheel = function () { return !!Cj(this.a) }; D.prototype.set_m_bIsFrontWheel = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xp(c, a) }; D.prototype.__destroy__ = function () { Zc(this.a) }; function rw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = void 0 === c ? je(a) : ie(a, c); q(rw)[this.a] = this } rw.prototype = Object.create(hw.prototype);
    rw.prototype.constructor = rw; rw.prototype.b = rw; rw.c = {}; b.btConvexTriangleMeshShape = rw; rw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ng(c, a) }; rw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Wp(d, a, c) }; rw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Io(c, a) }; rw.prototype.getMargin = function () { return rq(this.a) }; rw.prototype.__destroy__ = function () { Km(this.a) };
    function Uv() { throw "cannot construct a btBroadphaseInterface, no constructor in IDL"; } Uv.prototype = Object.create(p.prototype); Uv.prototype.constructor = Uv; Uv.prototype.b = Uv; Uv.c = {}; b.btBroadphaseInterface = Uv; Uv.prototype.__destroy__ = function () { Wo(this.a) }; function E(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = void 0 === e ? xp(a, c, d) : Mm(a, c, d, e); q(E)[this.a] = this } E.prototype = Object.create(p.prototype);
    E.prototype.constructor = E; E.prototype.b = E; E.c = {}; b.btRigidBodyConstructionInfo = E; E.prototype.get_m_linearDamping = function () { return Br(this.a) }; E.prototype.set_m_linearDamping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ak(c, a) }; E.prototype.get_m_angularDamping = function () { return Bc(this.a) }; E.prototype.set_m_angularDamping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ae(c, a) }; E.prototype.get_m_friction = function () { return Ce(this.a) };
    E.prototype.set_m_friction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qo(c, a) }; E.prototype.get_m_rollingFriction = function () { return so(this.a) }; E.prototype.set_m_rollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Om(c, a) }; E.prototype.get_m_restitution = function () { return mc(this.a) }; E.prototype.set_m_restitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ed(c, a) }; E.prototype.get_m_linearSleepingThreshold = function () { return Hr(this.a) };
    E.prototype.set_m_linearSleepingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vr(c, a) }; E.prototype.get_m_angularSleepingThreshold = function () { return Np(this.a) }; E.prototype.set_m_angularSleepingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ue(c, a) }; E.prototype.get_m_additionalDamping = function () { return !!Bj(this.a) }; E.prototype.set_m_additionalDamping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); En(c, a) };
    E.prototype.get_m_additionalDampingFactor = function () { return tp(this.a) }; E.prototype.set_m_additionalDampingFactor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ni(c, a) }; E.prototype.get_m_additionalLinearDampingThresholdSqr = function () { return ih(this.a) }; E.prototype.set_m_additionalLinearDampingThresholdSqr = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hh(c, a) }; E.prototype.get_m_additionalAngularDampingThresholdSqr = function () { return Ip(this.a) };
    E.prototype.set_m_additionalAngularDampingThresholdSqr = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); gl(c, a) }; E.prototype.get_m_additionalAngularDampingFactor = function () { return Si(this.a) }; E.prototype.set_m_additionalAngularDampingFactor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Mg(c, a) }; E.prototype.__destroy__ = function () { Kq(this.a) }; function sw() { throw "cannot construct a btCollisionConfiguration, no constructor in IDL"; } sw.prototype = Object.create(p.prototype);
    sw.prototype.constructor = sw; sw.prototype.b = sw; sw.c = {}; b.btCollisionConfiguration = sw; sw.prototype.__destroy__ = function () { kd(this.a) }; function iw() { this.a = Ap(); q(iw)[this.a] = this } iw.prototype = Object.create(p.prototype); iw.prototype.constructor = iw; iw.prototype.b = iw; iw.c = {}; b.btPersistentManifold = iw; iw.prototype.getBody0 = function () { return r(sh(this.a), w) }; iw.prototype.getBody1 = function () { return r(Sd(this.a), w) }; iw.prototype.getNumContacts = function () { return xj(this.a) };
    iw.prototype.getContactPoint = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(wn(c, a), G) }; iw.prototype.__destroy__ = function () { of(this.a) }; function tw(a) { a && "object" === typeof a && (a = a.a); this.a = void 0 === a ? sk() : tk(a); q(tw)[this.a] = this } tw.prototype = Object.create(u.prototype); tw.prototype.constructor = tw; tw.prototype.b = tw; tw.c = {}; b.btCompoundShape = tw; tw.prototype.addChildShape = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Gk(d, a, c) };
    tw.prototype.removeChildShapeByIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ge(c, a) }; tw.prototype.getNumChildShapes = function () { return Hg(this.a) }; tw.prototype.getChildShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Ge(c, a), u) }; tw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gg(c, a) }; tw.prototype.getMargin = function () { return Zb(this.a) };
    tw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Al(c, a) }; tw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); ui(d, a, c) }; tw.prototype.__destroy__ = function () { Qi(this.a) }; function H(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = co(a, c); q(H)[this.a] = this } H.prototype = Object.create(mw.prototype); H.prototype.constructor = H; H.prototype.b = H; H.c = {};
    b.ClosestConvexResultCallback = H; H.prototype.hasHit = function () { return !!hu(this.a) }; H.prototype.get_m_convexFromWorld = function () { return r(zg(this.a), A) }; H.prototype.set_m_convexFromWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); On(c, a) }; H.prototype.get_m_convexToWorld = function () { return r(Yp(this.a), A) }; H.prototype.set_m_convexToWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Oq(c, a) }; H.prototype.get_m_hitNormalWorld = function () { return r(kl(this.a), A) };
    H.prototype.set_m_hitNormalWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tc(c, a) }; H.prototype.get_m_hitPointWorld = function () { return r(To(this.a), A) }; H.prototype.set_m_hitPointWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ke(c, a) }; H.prototype.get_m_collisionFilterGroup = function () { return Il(this.a) }; H.prototype.set_m_collisionFilterGroup = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Bl(c, a) }; H.prototype.get_m_collisionFilterMask = function () { return am(this.a) };
    H.prototype.set_m_collisionFilterMask = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nr(c, a) }; H.prototype.get_m_closestHitFraction = function () { return $i(this.a) }; H.prototype.set_m_closestHitFraction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ts(c, a) }; H.prototype.__destroy__ = function () { Yl(this.a) }; function uw() { throw "cannot construct a tMaterialArray, no constructor in IDL"; } uw.prototype = Object.create(p.prototype); uw.prototype.constructor = uw; uw.prototype.b = uw; uw.c = {};
    b.tMaterialArray = uw; uw.prototype.size = function () { return ag(this.a) }; uw.prototype.at = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(jv(c, a), qw) }; uw.prototype.__destroy__ = function () { gh(this.a) }; function vw(a) { a && "object" === typeof a && (a = a.a); this.a = Kl(a); q(vw)[this.a] = this } vw.prototype = Object.create(ew.prototype); vw.prototype.constructor = vw; vw.prototype.b = vw; vw.c = {}; b.btDefaultVehicleRaycaster = vw;
    vw.prototype.castRay = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Ps(e, a, c, d) }; vw.prototype.__destroy__ = function () { Bk(this.a) }; function ww() { this.a = rp(); q(ww)[this.a] = this } ww.prototype = Object.create(p.prototype); ww.prototype.constructor = ww; ww.prototype.b = ww; ww.c = {}; b.btConstraintSetting = ww; ww.prototype.get_m_tau = function () { return ko(this.a) };
    ww.prototype.set_m_tau = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jf(c, a) }; ww.prototype.get_m_damping = function () { return Th(this.a) }; ww.prototype.set_m_damping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Df(c, a) }; ww.prototype.get_m_impulseClamp = function () { return pf(this.a) }; ww.prototype.set_m_impulseClamp = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fq(c, a) }; ww.prototype.__destroy__ = function () { nh(this.a) };
    function xw() { throw "cannot construct a LocalShapeInfo, no constructor in IDL"; } xw.prototype = Object.create(p.prototype); xw.prototype.constructor = xw; xw.prototype.b = xw; xw.c = {}; b.LocalShapeInfo = xw; xw.prototype.get_m_shapePart = function () { return Yd(this.a) }; xw.prototype.set_m_shapePart = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ee(c, a) }; xw.prototype.get_m_triangleIndex = function () { return Xq(this.a) };
    xw.prototype.set_m_triangleIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Am(c, a) }; xw.prototype.__destroy__ = function () { yd(this.a) }; function I(a) { a && "object" === typeof a && (a = a.a); this.a = cs(a); q(I)[this.a] = this } I.prototype = Object.create(w.prototype); I.prototype.constructor = I; I.prototype.b = I; I.c = {}; b.btRigidBody = I; I.prototype.getCenterOfMassTransform = function () { return r(Vc(this.a), y) }; I.prototype.setCenterOfMassTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); gp(c, a) };
    I.prototype.setSleepingThresholds = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); sr(d, a, c) }; I.prototype.setDamping = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); rj(d, a, c) }; I.prototype.setMassProps = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Xm(d, a, c) }; I.prototype.setLinearFactor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Fe(c, a) };
    I.prototype.applyTorque = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hs(c, a) }; I.prototype.applyLocalTorque = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); bk(c, a) }; I.prototype.applyForce = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Hf(d, a, c) }; I.prototype.applyCentralForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tk(c, a) };
    I.prototype.applyCentralLocalForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Fs(c, a) }; I.prototype.applyTorqueImpulse = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); tu(c, a) }; I.prototype.applyImpulse = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Lj(d, a, c) }; I.prototype.applyCentralImpulse = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Dc(c, a) }; I.prototype.updateInertiaTensor = function () { Wr(this.a) };
    I.prototype.getLinearVelocity = function () { return r(Kj(this.a), A) }; I.prototype.getAngularVelocity = function () { return r(Ft(this.a), A) }; I.prototype.setLinearVelocity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); It(c, a) }; I.prototype.setAngularVelocity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qj(c, a) }; I.prototype.getMotionState = function () { return r(as(this.a), lw) }; I.prototype.setMotionState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hj(c, a) };
    I.prototype.setAngularFactor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xm(c, a) }; I.prototype.upcast = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(og(c, a), I) }; I.prototype.setAnisotropicFriction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); up(d, a, c) }; I.prototype.getCollisionShape = function () { return r(yt(this.a), u) }; I.prototype.setContactProcessingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jj(c, a) };
    I.prototype.setActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fu(c, a) }; I.prototype.forceActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ku(c, a) }; I.prototype.activate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); void 0 === a ? ae(c) : Ir(c, a) }; I.prototype.isActive = function () { return !!oi(this.a) }; I.prototype.isKinematicObject = function () { return !!Cd(this.a) };
    I.prototype.setRestitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dv(c, a) }; I.prototype.setFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yf(c, a) }; I.prototype.setRollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dr(c, a) }; I.prototype.getWorldTransform = function () { return r(Mq(this.a), y) }; I.prototype.getCollisionFlags = function () { return Yi(this.a) }; I.prototype.setCollisionFlags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vu(c, a) };
    I.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Oj(c, a) }; I.prototype.setCollisionShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wi(c, a) }; I.prototype.setCcdMotionThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ff(c, a) }; I.prototype.setCcdSweptSphereRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Wf(c, a) }; I.prototype.getUserIndex = function () { return Ar(this.a) };
    I.prototype.setUserIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); lh(c, a) }; I.prototype.getUserPointer = function () { return r(ej(this.a), Vv) }; I.prototype.setUserPointer = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); bc(c, a) }; I.prototype.__destroy__ = function () { xu(this.a) }; function yw() { this.a = zj(); q(yw)[this.a] = this } yw.prototype = Object.create(p.prototype); yw.prototype.constructor = yw; yw.prototype.b = yw; yw.c = {}; b.btDbvtBroadphase = yw; yw.prototype.__destroy__ = function () { ot(this.a) };
    function zw() { this.a = Fn(); q(zw)[this.a] = this } zw.prototype = Object.create(ow.prototype); zw.prototype.constructor = zw; zw.prototype.b = zw; zw.c = {}; b.btDefaultSoftBodySolver = zw; zw.prototype.__destroy__ = function () { Mt(this.a) }; function Aw(a) { a && "object" === typeof a && (a = a.a); this.a = id(a); q(Aw)[this.a] = this } Aw.prototype = Object.create(Sv.prototype); Aw.prototype.constructor = Aw; Aw.prototype.b = Aw; Aw.c = {}; b.btCollisionDispatcher = Aw; Aw.prototype.getNumManifolds = function () { return wg(this.a) };
    Aw.prototype.getManifoldByIndexInternal = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Rf(c, a), iw) }; Aw.prototype.__destroy__ = function () { Cc(this.a) }; function Bw(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = void 0 === d ? $g(a, c) : void 0 === e ? ah(a, c, d) : void 0 === f ? dh(a, c, d, e) : eh(a, c, d, e, f); q(Bw)[this.a] = this } Bw.prototype = Object.create(p.prototype);
    Bw.prototype.constructor = Bw; Bw.prototype.b = Bw; Bw.c = {}; b.btAxisSweep3 = Bw; Bw.prototype.__destroy__ = function () { Dg(this.a) }; function Vv() { throw "cannot construct a VoidPtr, no constructor in IDL"; } Vv.prototype = Object.create(p.prototype); Vv.prototype.constructor = Vv; Vv.prototype.b = Vv; Vv.c = {}; b.VoidPtr = Vv; Vv.prototype.__destroy__ = function () { Yn(this.a) }; function J() { this.a = ln(); q(J)[this.a] = this } J.prototype = Object.create(p.prototype); J.prototype.constructor = J; J.prototype.b = J; J.c = {}; b.btSoftBodyWorldInfo = J;
    J.prototype.get_air_density = function () { return Wt(this.a) }; J.prototype.set_air_density = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nt(c, a) }; J.prototype.get_water_density = function () { return rh(this.a) }; J.prototype.set_water_density = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); lc(c, a) }; J.prototype.get_water_offset = function () { return nv(this.a) }; J.prototype.set_water_offset = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vo(c, a) }; J.prototype.get_m_maxDisplacement = function () { return el(this.a) };
    J.prototype.set_m_maxDisplacement = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tt(c, a) }; J.prototype.get_water_normal = function () { return r(or(this.a), A) }; J.prototype.set_water_normal = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); qu(c, a) }; J.prototype.get_m_broadphase = function () { return r(Pd(this.a), Uv) }; J.prototype.set_m_broadphase = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ig(c, a) }; J.prototype.get_m_dispatcher = function () { return r(kk(this.a), Sv) };
    J.prototype.set_m_dispatcher = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wr(c, a) }; J.prototype.get_m_gravity = function () { return r(Lr(this.a), A) }; J.prototype.set_m_gravity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Uk(c, a) }; J.prototype.__destroy__ = function () { qq(this.a) };
    function K(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = void 0 === d ? Ch(a, c) : void 0 === e ? _emscripten_bind_btConeTwistConstraint_btConeTwistConstraint_3(a, c, d) : Gh(a, c, d, e); q(K)[this.a] = this } K.prototype = Object.create(Xv.prototype); K.prototype.constructor = K; K.prototype.b = K; K.c = {}; b.btConeTwistConstraint = K;
    K.prototype.setLimit = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); qr(d, a, c) }; K.prototype.setAngularOnly = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Um(c, a) }; K.prototype.setDamping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Id(c, a) }; K.prototype.enableMotor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Bi(c, a) }; K.prototype.setMaxMotorImpulse = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wj(c, a) };
    K.prototype.setMaxMotorImpulseNormalized = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mg(c, a) }; K.prototype.setMotorTarget = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nn(c, a) }; K.prototype.setMotorTargetInConstraintSpace = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lo(c, a) }; K.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vh(c, a) }; K.prototype.getBreakingImpulseThreshold = function () { return rs(this.a) };
    K.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Cl(c, a) }; K.prototype.__destroy__ = function () { $o(this.a) };
    function Cw(a, c, d, e, f, g, h) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); this.a = void 0 === d ? rc(a, c) : void 0 === e ? fl(a, c, d) : void 0 === f ? Vg(a, c, d, e) : void 0 === g ? dl(a, c, d, e, f) : void 0 === h ? al(a, c, d, e, f, g) : bl(a, c, d, e, f, g, h); q(Cw)[this.a] = this } Cw.prototype = Object.create(Xv.prototype); Cw.prototype.constructor = Cw; Cw.prototype.b = Cw;
    Cw.c = {}; b.btHingeConstraint = Cw; Cw.prototype.setLimit = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); void 0 === f ? jn(g, a, c, d, e) : kn(g, a, c, d, e, f) }; Cw.prototype.enableAngularMotor = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Ij(e, a, c, d) };
    Cw.prototype.setAngularOnly = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dq(c, a) }; Cw.prototype.enableMotor = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Wu(c, a) }; Cw.prototype.setMaxMotorImpulse = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Rr(c, a) }; Cw.prototype.setMotorTarget = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Ri(d, a, c) };
    Cw.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); md(c, a) }; Cw.prototype.getBreakingImpulseThreshold = function () { return xq(this.a) }; Cw.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Wk(c, a) }; Cw.prototype.__destroy__ = function () { kt(this.a) }; function Dw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = zm(a, c); q(Dw)[this.a] = this } Dw.prototype = Object.create(cw.prototype);
    Dw.prototype.constructor = Dw; Dw.prototype.b = Dw; Dw.c = {}; b.btConeShapeZ = Dw; Dw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jk(c, a) }; Dw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); If(d, a, c) }; Dw.prototype.__destroy__ = function () { Zu(this.a) }; function Ew(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = th(a, c); q(Ew)[this.a] = this } Ew.prototype = Object.create(cw.prototype);
    Ew.prototype.constructor = Ew; Ew.prototype.b = Ew; Ew.c = {}; b.btConeShapeX = Ew; Ew.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Oh(c, a) }; Ew.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Gq(d, a, c) }; Ew.prototype.__destroy__ = function () { yi(this.a) }; function Fw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = void 0 === a ? tt() : void 0 === c ? ut(a) : vt(a, c); q(Fw)[this.a] = this }
    Fw.prototype = Object.create(kw.prototype); Fw.prototype.constructor = Fw; Fw.prototype.b = Fw; Fw.c = {}; b.btTriangleMesh = Fw; Fw.prototype.addTriangle = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); void 0 === e ? mu(f, a, c, d) : ou(f, a, c, d, e) }; Fw.prototype.__destroy__ = function () { yo(this.a) }; function Gw() { this.a = Rh(); q(Gw)[this.a] = this } Gw.prototype = Object.create(u.prototype); Gw.prototype.constructor = Gw;
    Gw.prototype.b = Gw; Gw.c = {}; b.btConvexHullShape = Gw; Gw.prototype.addPoint = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); void 0 === c ? ct(d, a) : ns(d, a, c) }; Gw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gf(c, a) }; Gw.prototype.getMargin = function () { return Ec(this.a) }; Gw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xr(c, a) };
    Gw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Kf(d, a, c) }; Gw.prototype.__destroy__ = function () { eg(this.a) }; function L() { this.a = Kt(); q(L)[this.a] = this } L.prototype = Object.create(p.prototype); L.prototype.constructor = L; L.prototype.b = L; L.c = {}; b.btVehicleTuning = L; L.prototype.get_m_suspensionStiffness = function () { return ks(this.a) };
    L.prototype.set_m_suspensionStiffness = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); gr(c, a) }; L.prototype.get_m_suspensionCompression = function () { return Ik(this.a) }; L.prototype.set_m_suspensionCompression = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); eq(c, a) }; L.prototype.get_m_suspensionDamping = function () { return ek(this.a) }; L.prototype.set_m_suspensionDamping = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ho(c, a) }; L.prototype.get_m_maxSuspensionTravelCm = function () { return bt(this.a) };
    L.prototype.set_m_maxSuspensionTravelCm = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nk(c, a) }; L.prototype.get_m_frictionSlip = function () { return xv(this.a) }; L.prototype.set_m_frictionSlip = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vb(c, a) }; L.prototype.get_m_maxSuspensionForce = function () { return Uq(this.a) }; L.prototype.set_m_maxSuspensionForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dk(c, a) };
    function Hw() { throw "cannot construct a btCollisionObjectWrapper, no constructor in IDL"; } Hw.prototype = Object.create(p.prototype); Hw.prototype.constructor = Hw; Hw.prototype.b = Hw; Hw.c = {}; b.btCollisionObjectWrapper = Hw; function Iw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = void 0 === a ? ii() : void 0 === c ? ji(a) : gi(a, c); q(Iw)[this.a] = this } Iw.prototype = Object.create(lw.prototype); Iw.prototype.constructor = Iw; Iw.prototype.b = Iw; Iw.c = {}; b.btDefaultMotionState = Iw;
    Iw.prototype.getWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Fc(c, a) }; Iw.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ql(c, a) }; Iw.prototype.get_m_graphicsWorldTrans = function () { return r(Gd(this.a), y) }; Iw.prototype.set_m_graphicsWorldTrans = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Sm(c, a) }; Iw.prototype.__destroy__ = function () { ye(this.a) }; function M(a) { a && "object" === typeof a && (a = a.a); this.a = oh(a); q(M)[this.a] = this }
    M.prototype = Object.create(p.prototype); M.prototype.constructor = M; M.prototype.b = M; M.c = {}; b.btWheelInfo = M; M.prototype.getSuspensionRestLength = function () { return Rm(this.a) }; M.prototype.updateWheel = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Xo(d, a, c) }; M.prototype.get_m_suspensionStiffness = function () { return uc(this.a) }; M.prototype.set_m_suspensionStiffness = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mq(c, a) }; M.prototype.get_m_frictionSlip = function () { return ik(this.a) };
    M.prototype.set_m_frictionSlip = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wt(c, a) }; M.prototype.get_m_engineForce = function () { return Le(this.a) }; M.prototype.set_m_engineForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vj(c, a) }; M.prototype.get_m_rollInfluence = function () { return $d(this.a) }; M.prototype.set_m_rollInfluence = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pn(c, a) }; M.prototype.get_m_suspensionRestLength1 = function () { return tc(this.a) };
    M.prototype.set_m_suspensionRestLength1 = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); yu(c, a) }; M.prototype.get_m_wheelsRadius = function () { return Ht(this.a) }; M.prototype.set_m_wheelsRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ff(c, a) }; M.prototype.get_m_wheelsDampingCompression = function () { return gu(this.a) }; M.prototype.set_m_wheelsDampingCompression = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ei(c, a) }; M.prototype.get_m_wheelsDampingRelaxation = function () { return At(this.a) };
    M.prototype.set_m_wheelsDampingRelaxation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pt(c, a) }; M.prototype.get_m_steering = function () { return wu(this.a) }; M.prototype.set_m_steering = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uq(c, a) }; M.prototype.get_m_maxSuspensionForce = function () { return ku(this.a) }; M.prototype.set_m_maxSuspensionForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Sh(c, a) }; M.prototype.get_m_maxSuspensionTravelCm = function () { return hm(this.a) };
    M.prototype.set_m_maxSuspensionTravelCm = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nn(c, a) }; M.prototype.get_m_wheelsSuspensionForce = function () { return wl(this.a) }; M.prototype.set_m_wheelsSuspensionForce = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); on(c, a) }; M.prototype.get_m_bIsFrontWheel = function () { return !!vn(this.a) }; M.prototype.set_m_bIsFrontWheel = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ef(c, a) };
    M.prototype.get_m_raycastInfo = function () { return r(pv(this.a), N) }; M.prototype.set_m_raycastInfo = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); kg(c, a) }; M.prototype.get_m_chassisConnectionPointCS = function () { return r(ip(this.a), A) }; M.prototype.set_m_chassisConnectionPointCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ci(c, a) }; M.prototype.get_m_worldTransform = function () { return r(Is(this.a), y) };
    M.prototype.set_m_worldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nk(c, a) }; M.prototype.get_m_wheelDirectionCS = function () { return r(cn(this.a), A) }; M.prototype.set_m_wheelDirectionCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wv(c, a) }; M.prototype.get_m_wheelAxleCS = function () { return r(Hu(this.a), A) }; M.prototype.set_m_wheelAxleCS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nu(c, a) }; M.prototype.get_m_rotation = function () { return Zt(this.a) };
    M.prototype.set_m_rotation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ul(c, a) }; M.prototype.get_m_deltaRotation = function () { return Op(this.a) }; M.prototype.set_m_deltaRotation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); tm(c, a) }; M.prototype.get_m_brake = function () { return Mp(this.a) }; M.prototype.set_m_brake = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Mk(c, a) }; M.prototype.get_m_clippedInvContactDotSuspension = function () { return Qt(this.a) };
    M.prototype.set_m_clippedInvContactDotSuspension = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); sp(c, a) }; M.prototype.get_m_suspensionRelativeVelocity = function () { return Ej(this.a) }; M.prototype.set_m_suspensionRelativeVelocity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ds(c, a) }; M.prototype.get_m_skidInfo = function () { return Gl(this.a) }; M.prototype.set_m_skidInfo = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); au(c, a) }; M.prototype.__destroy__ = function () { Tg(this.a) };
    function O(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = void 0 === a ? ev() : void 0 === c ? _emscripten_bind_btVector4_btVector4_1(a) : void 0 === d ? _emscripten_bind_btVector4_btVector4_2(a, c) : void 0 === e ? _emscripten_bind_btVector4_btVector4_3(a, c, d) : gv(a, c, d, e); q(O)[this.a] = this } O.prototype = Object.create(A.prototype); O.prototype.constructor = O; O.prototype.b = O; O.c = {}; b.btVector4 = O; O.prototype.w = function () { return Hs(this.a) };
    O.prototype.setValue = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); be(f, a, c, d, e) }; O.prototype.length = O.prototype.length = function () { return Pm(this.a) }; O.prototype.x = function () { return fv(this.a) }; O.prototype.y = function () { return Xn(this.a) }; O.prototype.z = function () { return hr(this.a) }; O.prototype.setX = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hg(c, a) };
    O.prototype.setY = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vc(c, a) }; O.prototype.setZ = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); qg(c, a) }; O.prototype.normalize = function () { vs(this.a) }; O.prototype.rotate = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); return r(Fd(d, a, c), A) }; O.prototype.dot = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return kj(c, a) };
    O.prototype.op_mul = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Uh(c, a), A) }; O.prototype.op_add = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Sg(c, a), A) }; O.prototype.op_sub = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(lv(c, a), A) }; O.prototype.__destroy__ = function () { Dp(this.a) }; function Jw() { this.a = js(); q(Jw)[this.a] = this } Jw.prototype = Object.create(p.prototype); Jw.prototype.constructor = Jw; Jw.prototype.b = Jw; Jw.c = {};
    b.btDefaultCollisionConstructionInfo = Jw; Jw.prototype.__destroy__ = function () { yq(this.a) }; function Kw() { throw "cannot construct a btVehicleRaycasterResult, no constructor in IDL"; } Kw.prototype = Object.create(p.prototype); Kw.prototype.constructor = Kw; Kw.prototype.b = Kw; Kw.c = {}; b.btVehicleRaycasterResult = Kw; Kw.prototype.get_m_hitPointInWorld = function () { return r(Di(this.a), A) }; Kw.prototype.set_m_hitPointInWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jm(c, a) };
    Kw.prototype.get_m_hitNormalInWorld = function () { return r(ac(this.a), A) }; Kw.prototype.set_m_hitNormalInWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Dm(c, a) }; Kw.prototype.get_m_distFraction = function () { return ts(this.a) }; Kw.prototype.set_m_distFraction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jh(c, a) }; Kw.prototype.__destroy__ = function () { Bf(this.a) }; function Lw() { throw "cannot construct a btConstraintSolver, no constructor in IDL"; } Lw.prototype = Object.create(p.prototype);
    Lw.prototype.constructor = Lw; Lw.prototype.b = Lw; Lw.c = {}; b.btConstraintSolver = Lw; Lw.prototype.__destroy__ = function () { lp(this.a) }; function P(a, c, d) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); this.a = pi(a, c, d); q(P)[this.a] = this } P.prototype = Object.create(dw.prototype); P.prototype.constructor = P; P.prototype.b = P; P.c = {}; b.btRaycastVehicle = P;
    P.prototype.applyEngineForce = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Ao(d, a, c) }; P.prototype.setSteeringValue = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Ne(d, a, c) }; P.prototype.getWheelTransformWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(fk(c, a), y) };
    P.prototype.updateWheelTransform = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Jr(d, a, c) }; P.prototype.addWheel = function (a, c, d, e, f, g, h) { var l = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); return r(Ru(l, a, c, d, e, f, g, h), M) }; P.prototype.getNumWheels = function () { return mk(this.a) };
    P.prototype.getRigidBody = function () { return r(ju(this.a), I) }; P.prototype.getWheelInfo = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(kr(c, a), M) }; P.prototype.setBrake = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Co(d, a, c) }; P.prototype.setCoordinateSystem = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); kf(e, a, c, d) }; P.prototype.getCurrentSpeedKmHour = function () { return Ke(this.a) };
    P.prototype.getChassisWorldTransform = function () { return r(Rk(this.a), y) }; P.prototype.rayCast = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return Nd(c, a) }; P.prototype.updateVehicle = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Fo(c, a) }; P.prototype.resetSuspension = function () { et(this.a) }; P.prototype.getSteeringValue = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return sv(c, a) };
    P.prototype.updateWheelTransformsWS = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); void 0 === c ? Lk(d, a) : Uu(d, a, c) }; P.prototype.setPitchControl = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wh(c, a) }; P.prototype.updateSuspension = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); qd(c, a) }; P.prototype.updateFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Cq(c, a) }; P.prototype.getRightAxis = function () { return Xe(this.a) };
    P.prototype.getUpAxis = function () { return Je(this.a) }; P.prototype.getForwardAxis = function () { return Bn(this.a) }; P.prototype.getForwardVector = function () { return r(Bs(this.a), A) }; P.prototype.getUserConstraintType = function () { return Xs(this.a) }; P.prototype.setUserConstraintType = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Au(c, a) }; P.prototype.setUserConstraintId = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Wj(c, a) }; P.prototype.getUserConstraintId = function () { return gn(this.a) };
    P.prototype.updateAction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); hn(d, a, c) }; P.prototype.__destroy__ = function () { Wi(this.a) }; function Mw(a) { a && "object" === typeof a && (a = a.a); this.a = nl(a); q(Mw)[this.a] = this } Mw.prototype = Object.create(gw.prototype); Mw.prototype.constructor = Mw; Mw.prototype.b = Mw; Mw.c = {}; b.btCylinderShapeX = Mw; Mw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ag(c, a) }; Mw.prototype.getMargin = function () { return qj(this.a) };
    Mw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); yj(c, a) }; Mw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); zs(d, a, c) }; Mw.prototype.__destroy__ = function () { Ur(this.a) }; function Nw(a) { a && "object" === typeof a && (a = a.a); this.a = qk(a); q(Nw)[this.a] = this } Nw.prototype = Object.create(gw.prototype); Nw.prototype.constructor = Nw; Nw.prototype.b = Nw; Nw.c = {}; b.btCylinderShapeZ = Nw;
    Nw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xf(c, a) }; Nw.prototype.getMargin = function () { return fc(this.a) }; Nw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xu(c, a) }; Nw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); ym(d, a, c) }; Nw.prototype.__destroy__ = function () { qf(this.a) }; function Ow() { this.a = Nl(); q(Ow)[this.a] = this } Ow.prototype = Object.create(p.prototype);
    Ow.prototype.constructor = Ow; Ow.prototype.b = Ow; Ow.c = {}; b.btSequentialImpulseConstraintSolver = Ow; Ow.prototype.__destroy__ = function () { Ui(this.a) }; function N() { throw "cannot construct a RaycastInfo, no constructor in IDL"; } N.prototype = Object.create(p.prototype); N.prototype.constructor = N; N.prototype.b = N; N.c = {}; b.RaycastInfo = N; N.prototype.get_m_contactNormalWS = function () { return r(qh(this.a), A) }; N.prototype.set_m_contactNormalWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zn(c, a) };
    N.prototype.get_m_contactPointWS = function () { return r(Mf(this.a), A) }; N.prototype.set_m_contactPointWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); bo(c, a) }; N.prototype.get_m_suspensionLength = function () { return $m(this.a) }; N.prototype.set_m_suspensionLength = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nq(c, a) }; N.prototype.get_m_hardPointWS = function () { return r(Zo(this.a), A) }; N.prototype.set_m_hardPointWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); bg(c, a) };
    N.prototype.get_m_wheelDirectionWS = function () { return r(fp(this.a), A) }; N.prototype.set_m_wheelDirectionWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $b(c, a) }; N.prototype.get_m_wheelAxleWS = function () { return r(Ac(this.a), A) }; N.prototype.set_m_wheelAxleWS = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Eo(c, a) }; N.prototype.get_m_isInContact = function () { return !!Fl(this.a) }; N.prototype.set_m_isInContact = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ud(c, a) };
    N.prototype.get_m_groundObject = function () { return Nj(this.a) }; N.prototype.set_m_groundObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zn(c, a) }; N.prototype.__destroy__ = function () { wm(this.a) }; function Pw() { throw "cannot construct a tNodeArray, no constructor in IDL"; } Pw.prototype = Object.create(p.prototype); Pw.prototype.constructor = Pw; Pw.prototype.b = Pw; Pw.c = {}; b.tNodeArray = Pw; Pw.prototype.size = function () { return de(this.a) };
    Pw.prototype.at = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Gp(c, a), Node) }; Pw.prototype.__destroy__ = function () { Gj(this.a) }; function Q(a, c, d, e) { Nv(); a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); "object" == typeof e && (e = Qv(e)); this.a = vp(a, c, d, e); q(Q)[this.a] = this } Q.prototype = Object.create(w.prototype); Q.prototype.constructor = Q; Q.prototype.b = Q; Q.c = {}; b.btSoftBody = Q;
    Q.prototype.checkLink = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); return !!xk(d, a, c) }; Q.prototype.checkFace = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); return !!Ms(e, a, c, d) }; Q.prototype.appendMaterial = function () { return r(St(this.a), qw) }; Q.prototype.appendNode = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); lf(d, a, c) };
    Q.prototype.appendLink = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); Ph(f, a, c, d, e) }; Q.prototype.appendFace = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); he(f, a, c, d, e) };
    Q.prototype.appendTetra = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); xi(g, a, c, d, e, f) }; Q.prototype.appendAnchor = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); zd(f, a, c, d, e) }; Q.prototype.getTotalMass = function () { return cj(this.a) };
    Q.prototype.setTotalMass = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Sf(d, a, c) }; Q.prototype.setMass = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); ve(d, a, c) }; Q.prototype.transform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); qi(c, a) }; Q.prototype.translate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ah(c, a) };
    Q.prototype.rotate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); sc(c, a) }; Q.prototype.scale = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pn(c, a) }; Q.prototype.generateClusters = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); return void 0 === c ? Rl(d, a) : Pl(d, a, c) }; Q.prototype.upcast = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(tr(c, a), Q) };
    Q.prototype.setAnisotropicFriction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Zr(d, a, c) }; Q.prototype.getCollisionShape = function () { return r(yk(this.a), u) }; Q.prototype.setContactProcessingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fo(c, a) }; Q.prototype.setActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zr(c, a) };
    Q.prototype.forceActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); lj(c, a) }; Q.prototype.activate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); void 0 === a ? wp(c) : $q(c, a) }; Q.prototype.isActive = function () { return !!mp(this.a) }; Q.prototype.isKinematicObject = function () { return !!Uc(this.a) }; Q.prototype.setRestitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xt(c, a) }; Q.prototype.setFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); rr(c, a) };
    Q.prototype.setRollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ug(c, a) }; Q.prototype.getWorldTransform = function () { return r(xo(this.a), y) }; Q.prototype.getCollisionFlags = function () { return Qk(this.a) }; Q.prototype.setCollisionFlags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nf(c, a) }; Q.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hl(c, a) };
    Q.prototype.setCollisionShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jp(c, a) }; Q.prototype.setCcdMotionThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pc(c, a) }; Q.prototype.setCcdSweptSphereRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uh(c, a) }; Q.prototype.getUserIndex = function () { return to(this.a) }; Q.prototype.setUserIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hp(c, a) };
    Q.prototype.getUserPointer = function () { return r(Pe(this.a), Vv) }; Q.prototype.setUserPointer = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ue(c, a) }; Q.prototype.get_m_cfg = function () { return r(Hn(this.a), R) }; Q.prototype.set_m_cfg = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lp(c, a) }; Q.prototype.get_m_nodes = function () { return r(Fr(this.a), Pw) }; Q.prototype.set_m_nodes = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mt(c, a) };
    Q.prototype.get_m_materials = function () { return r(lu(this.a), uw) }; Q.prototype.set_m_materials = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zf(c, a) }; Q.prototype.__destroy__ = function () { Gr(this.a) };
    function Qw(a, c, d, e, f, g, h, l, n) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); l && "object" === typeof l && (l = l.a); n && "object" === typeof n && (n = n.a); this.a = Zq(a, c, d, e, f, g, h, l, n); q(Qw)[this.a] = this } Qw.prototype = Object.create(Yv.prototype); Qw.prototype.constructor = Qw; Qw.prototype.b = Qw; Qw.c = {}; b.btHeightfieldTerrainShape = Qw;
    Qw.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Hl(c, a) }; Qw.prototype.getMargin = function () { return Ii(this.a) }; Qw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); od(c, a) }; Qw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); gj(d, a, c) }; Qw.prototype.__destroy__ = function () { gm(this.a) }; function R() { throw "cannot construct a Config, no constructor in IDL"; }
    R.prototype = Object.create(p.prototype); R.prototype.constructor = R; R.prototype.b = R; R.c = {}; b.Config = R; R.prototype.get_kVCF = function () { return Ve(this.a) }; R.prototype.set_kVCF = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pm(c, a) }; R.prototype.get_kDP = function () { return Sj(this.a) }; R.prototype.set_kDP = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); at(c, a) }; R.prototype.get_kDG = function () { return zk(this.a) };
    R.prototype.set_kDG = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mn(c, a) }; R.prototype.get_kLF = function () { return Wh(this.a) }; R.prototype.set_kLF = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yt(c, a) }; R.prototype.get_kPR = function () { return iq(this.a) }; R.prototype.set_kPR = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xf(c, a) }; R.prototype.get_kVC = function () { return im(this.a) }; R.prototype.set_kVC = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); cf(c, a) };
    R.prototype.get_kDF = function () { return su(this.a) }; R.prototype.set_kDF = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yq(c, a) }; R.prototype.get_kMT = function () { return Fm(this.a) }; R.prototype.set_kMT = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pg(c, a) }; R.prototype.get_kCHR = function () { return we(this.a) }; R.prototype.set_kCHR = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zu(c, a) }; R.prototype.get_kKHR = function () { return it(this.a) };
    R.prototype.set_kKHR = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); tf(c, a) }; R.prototype.get_kSHR = function () { return fj(this.a) }; R.prototype.set_kSHR = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xg(c, a) }; R.prototype.get_kAHR = function () { return Ul(this.a) }; R.prototype.set_kAHR = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Hi(c, a) }; R.prototype.get_kSRHR_CL = function () { return Pb(this.a) };
    R.prototype.set_kSRHR_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vg(c, a) }; R.prototype.get_kSKHR_CL = function () { return Ns(this.a) }; R.prototype.set_kSKHR_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $k(c, a) }; R.prototype.get_kSSHR_CL = function () { return Ko(this.a) }; R.prototype.set_kSSHR_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); fd(c, a) }; R.prototype.get_kSR_SPLT_CL = function () { return Me(this.a) };
    R.prototype.set_kSR_SPLT_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lc(c, a) }; R.prototype.get_kSK_SPLT_CL = function () { return Fh(this.a) }; R.prototype.set_kSK_SPLT_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nj(c, a) }; R.prototype.get_kSS_SPLT_CL = function () { return Tr(this.a) }; R.prototype.set_kSS_SPLT_CL = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); bu(c, a) }; R.prototype.get_maxvolume = function () { return Pq(this.a) };
    R.prototype.set_maxvolume = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pf(c, a) }; R.prototype.get_timescale = function () { return Im(this.a) }; R.prototype.set_timescale = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Kh(c, a) }; R.prototype.get_viterations = function () { return ki(this.a) }; R.prototype.set_viterations = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pt(c, a) }; R.prototype.get_piterations = function () { return zh(this.a) };
    R.prototype.set_piterations = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); jh(c, a) }; R.prototype.get_diterations = function () { return Nc(this.a) }; R.prototype.set_diterations = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ws(c, a) }; R.prototype.get_citerations = function () { return qn(this.a) }; R.prototype.set_citerations = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ls(c, a) }; R.prototype.get_collisions = function () { return Uj(this.a) };
    R.prototype.set_collisions = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); rl(c, a) }; R.prototype.__destroy__ = function () { ck(this.a) }; function Node() { throw "cannot construct a Node, no constructor in IDL"; } Node.prototype = Object.create(p.prototype); Node.prototype.constructor = Node; Node.prototype.b = Node; Node.c = {}; b.Node = Node; Node.prototype.get_m_x = function () { return r(Dk(this.a), A) }; Node.prototype.set_m_x = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vq(c, a) };
    Node.prototype.get_m_n = function () { return r(tq(this.a), A) }; Node.prototype.set_m_n = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yb(c, a) }; Node.prototype.__destroy__ = function () { Cg(this.a) }; function Rw() { this.a = Vn(); q(Rw)[this.a] = this } Rw.prototype = Object.create(p.prototype); Rw.prototype.constructor = Rw; Rw.prototype.b = Rw; Rw.c = {}; b.btGhostPairCallback = Rw; Rw.prototype.__destroy__ = function () { Lq(this.a) }; function Sw() { throw "cannot construct a btOverlappingPairCallback, no constructor in IDL"; }
    Sw.prototype = Object.create(p.prototype); Sw.prototype.constructor = Sw; Sw.prototype.b = Sw; Sw.c = {}; b.btOverlappingPairCallback = Sw; Sw.prototype.__destroy__ = function () { Dj(this.a) }; function S(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = void 0 === e ? Oi(a, c, d) : Kp(a, c, d, e); q(S)[this.a] = this } S.prototype = Object.create(dw.prototype); S.prototype.constructor = S; S.prototype.b = S; S.c = {};
    b.btKinematicCharacterController = S; S.prototype.setUpAxis = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dd(c, a) }; S.prototype.setWalkDirection = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); es(c, a) }; S.prototype.setVelocityForTimeInterval = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Cs(d, a, c) }; S.prototype.warp = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); vo(c, a) };
    S.prototype.preStep = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zo(c, a) }; S.prototype.playerStep = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Qp(d, a, c) }; S.prototype.setFallSpeed = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); eu(c, a) }; S.prototype.setJumpSpeed = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Eq(c, a) }; S.prototype.setMaxJumpHeight = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uv(c, a) };
    S.prototype.canJump = function () { return !!li(this.a) }; S.prototype.jump = function () { ig(this.a) }; S.prototype.setGravity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vd(c, a) }; S.prototype.getGravity = function () { return ps(this.a) }; S.prototype.setMaxSlope = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); nc(c, a) }; S.prototype.getMaxSlope = function () { return Pu(this.a) }; S.prototype.getGhostObject = function () { return r(bs(this.a), T) };
    S.prototype.setUseGhostSweepTest = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); td(c, a) }; S.prototype.onGround = function () { return !!jr(this.a) }; S.prototype.updateAction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); fi(d, a, c) }; S.prototype.__destroy__ = function () { hv(this.a) }; function Tw() { throw "cannot construct a btSoftBodyArray, no constructor in IDL"; } Tw.prototype = Object.create(p.prototype); Tw.prototype.constructor = Tw; Tw.prototype.b = Tw; Tw.c = {};
    b.btSoftBodyArray = Tw; Tw.prototype.size = function () { return Up(this.a) }; Tw.prototype.at = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(mi(c, a), Q) }; Tw.prototype.__destroy__ = function () { rk(this.a) }; function Uw(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = vk(a, c); q(Uw)[this.a] = this } Uw.prototype = Object.create(Yv.prototype); Uw.prototype.constructor = Uw; Uw.prototype.b = Uw; Uw.c = {}; b.btStaticPlaneShape = Uw;
    Uw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vp(c, a) }; Uw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Hp(d, a, c) }; Uw.prototype.__destroy__ = function () { Vu(this.a) }; function Tv() { throw "cannot construct a btOverlappingPairCache, no constructor in IDL"; } Tv.prototype = Object.create(p.prototype); Tv.prototype.constructor = Tv; Tv.prototype.b = Tv; Tv.c = {}; b.btOverlappingPairCache = Tv;
    Tv.prototype.setInternalGhostPairCallback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uk(c, a) }; Tv.prototype.__destroy__ = function () { kh(this.a) }; function U(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = Ih(a, c, d, e, f); q(U)[this.a] = this } U.prototype = Object.create(B.prototype); U.prototype.constructor = U; U.prototype.b = U; U.c = {}; b.btSoftRigidDynamicsWorld = U;
    U.prototype.addSoftBody = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); df(e, a, c, d) }; U.prototype.removeSoftBody = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pc(c, a) }; U.prototype.removeCollisionObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Vh(c, a) }; U.prototype.getWorldInfo = function () { return r(Eu(this.a), J) }; U.prototype.getSoftBodyArray = function () { return r(Ug(this.a), Tw) };
    U.prototype.getDispatcher = function () { return r(ri(this.a), Sv) }; U.prototype.rayTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); Ki(e, a, c, d) }; U.prototype.getPairCache = function () { return r(qo(this.a), Tv) }; U.prototype.getDispatchInfo = function () { return r(pu(this.a), t) };
    U.prototype.addCollisionObject = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? Zh(e, a) : void 0 === d ? Yh(e, a, c) : Xh(e, a, c, d) }; U.prototype.getBroadphase = function () { return r(tl(this.a), Uv) };
    U.prototype.convexSweepTest = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); Em(g, a, c, d, e, f) }; U.prototype.contactPairTest = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); bp(e, a, c, d) };
    U.prototype.contactTest = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Bu(d, a, c) }; U.prototype.setGravity = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ie(c, a) }; U.prototype.getGravity = function () { return r(Rq(this.a), A) };
    U.prototype.addRigidBody = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); void 0 === c ? Zs(e, a) : void 0 === d ? _emscripten_bind_btSoftRigidDynamicsWorld_addRigidBody_2(e, a, c) : $s(e, a, c, d) }; U.prototype.removeRigidBody = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); cv(c, a) }; U.prototype.addConstraint = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); void 0 === c ? ed(d, a) : ru(d, a, c) };
    U.prototype.removeConstraint = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zc(c, a) }; U.prototype.stepSimulation = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); return void 0 === c ? sm(e, a) : void 0 === d ? Ut(e, a, c) : qm(e, a, c, d) }; U.prototype.addAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jc(c, a) }; U.prototype.removeAction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zd(c, a) };
    U.prototype.getSolverInfo = function () { return r(Qf(this.a), Wv) }; U.prototype.__destroy__ = function () { yc(this.a) }; function y(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = void 0 === a ? af() : void 0 === c ? _emscripten_bind_btTransform_btTransform_1(a) : $e(a, c); q(y)[this.a] = this } y.prototype = Object.create(p.prototype); y.prototype.constructor = y; y.prototype.b = y; y.c = {}; b.btTransform = y; y.prototype.setIdentity = function () { Wc(this.a) };
    y.prototype.setOrigin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pg(c, a) }; y.prototype.setRotation = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Wg(c, a) }; y.prototype.getOrigin = function () { return r(ht(this.a), A) }; y.prototype.getRotation = function () { return r(Zk(this.a), W) }; y.prototype.getBasis = function () { return r(sg(this.a), pw) }; y.prototype.setFromOpenGLMatrix = function (a) { var c = this.a; Nv(); "object" == typeof a && (a = Qv(a)); Ou(c, a) }; y.prototype.__destroy__ = function () { Gu(this.a) };
    function X(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = Cp(a, c); q(X)[this.a] = this } X.prototype = Object.create(bw.prototype); X.prototype.constructor = X; X.prototype.b = X; X.c = {}; b.ClosestRayResultCallback = X; X.prototype.hasHit = function () { return !!Fk(this.a) }; X.prototype.get_m_rayFromWorld = function () { return r(eo(this.a), A) }; X.prototype.set_m_rayFromWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); dp(c, a) };
    X.prototype.get_m_rayToWorld = function () { return r(Tf(this.a), A) }; X.prototype.set_m_rayToWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nt(c, a) }; X.prototype.get_m_hitNormalWorld = function () { return r(st(this.a), A) }; X.prototype.set_m_hitNormalWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Tu(c, a) }; X.prototype.get_m_hitPointWorld = function () { return r(cc(this.a), A) }; X.prototype.set_m_hitPointWorld = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Af(c, a) };
    X.prototype.get_m_collisionFilterGroup = function () { return Ot(this.a) }; X.prototype.set_m_collisionFilterGroup = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zg(c, a) }; X.prototype.get_m_collisionFilterMask = function () { return Zj(this.a) }; X.prototype.set_m_collisionFilterMask = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Fp(c, a) }; X.prototype.get_m_collisionObject = function () { return r(ao(this.a), w) };
    X.prototype.set_m_collisionObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Td(c, a) }; X.prototype.__destroy__ = function () { yg(this.a) }; function Vw(a) { a && "object" === typeof a && (a = a.a); this.a = void 0 === a ? xh() : gt(a); q(Vw)[this.a] = this } Vw.prototype = Object.create($v.prototype); Vw.prototype.constructor = Vw; Vw.prototype.b = Vw; Vw.c = {}; b.btSoftBodyRigidBodyCollisionConfiguration = Vw; Vw.prototype.__destroy__ = function () { Fq(this.a) }; function Ww() { this.a = Vs(); q(Ww)[this.a] = this } Ww.prototype = Object.create(nw.prototype);
    Ww.prototype.constructor = Ww; Ww.prototype.b = Ww; Ww.c = {}; b.ConcreteContactResultCallback = Ww; Ww.prototype.addSingleResult = function (a, c, d, e, f, g, h) { var l = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); return Yo(l, a, c, d, e, f, g, h) }; Ww.prototype.__destroy__ = function () { Do(this.a) };
    function Xw(a, c, d) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); this.a = void 0 === d ? Vq(a, c) : Wq(a, c, d); q(Xw)[this.a] = this } Xw.prototype = Object.create(aw.prototype); Xw.prototype.constructor = Xw; Xw.prototype.b = Xw; Xw.c = {}; b.btBvhTriangleMeshShape = Xw; Xw.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ce(c, a) };
    Xw.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Fg(d, a, c) }; Xw.prototype.__destroy__ = function () { Ks(this.a) };
    function Yw(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = void 0 === e ? Fu(a, c, d) : void 0 === f ? _emscripten_bind_btSliderConstraint_btSliderConstraint_4(a, c, d, e) : Hd(a, c, d, e, f); q(Yw)[this.a] = this } Yw.prototype = Object.create(Xv.prototype); Yw.prototype.constructor = Yw; Yw.prototype.b = Yw; Yw.c = {}; b.btSliderConstraint = Yw;
    Yw.prototype.setLowerLinLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Et(c, a) }; Yw.prototype.setUpperLinLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Bh(c, a) }; Yw.prototype.setLowerAngLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $n(c, a) }; Yw.prototype.setUpperAngLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Kn(c, a) }; Yw.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ho(c, a) };
    Yw.prototype.getBreakingImpulseThreshold = function () { return Mi(this.a) }; Yw.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pq(c, a) }; Yw.prototype.__destroy__ = function () { Ju(this.a) }; function T() { this.a = Jd(); q(T)[this.a] = this } T.prototype = Object.create(C.prototype); T.prototype.constructor = T; T.prototype.b = T; T.c = {}; b.btPairCachingGhostObject = T;
    T.prototype.setAnisotropicFriction = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Ck(d, a, c) }; T.prototype.getCollisionShape = function () { return r(Pk(this.a), u) }; T.prototype.setContactProcessingThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Xj(c, a) }; T.prototype.setActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); lm(c, a) };
    T.prototype.forceActivationState = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xe(c, a) }; T.prototype.activate = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); void 0 === a ? qe(c) : re(c, a) }; T.prototype.isActive = function () { return !!vv(this.a) }; T.prototype.isKinematicObject = function () { return !!Ep(this.a) }; T.prototype.setRestitution = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gi(c, a) }; T.prototype.setFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); tg(c, a) };
    T.prototype.setRollingFriction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); er(c, a) }; T.prototype.getWorldTransform = function () { return r(ti(this.a), y) }; T.prototype.getCollisionFlags = function () { return Vt(this.a) }; T.prototype.setCollisionFlags = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); $r(c, a) }; T.prototype.setWorldTransform = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wq(c, a) };
    T.prototype.setCollisionShape = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); uo(c, a) }; T.prototype.setCcdMotionThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zm(c, a) }; T.prototype.setCcdSweptSphereRadius = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Hj(c, a) }; T.prototype.getUserIndex = function () { return bf(this.a) }; T.prototype.setUserIndex = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ni(c, a) };
    T.prototype.getUserPointer = function () { return r(lt(this.a), Vv) }; T.prototype.setUserPointer = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); oe(c, a) }; T.prototype.getNumOverlappingObjects = function () { return go(this.a) }; T.prototype.getOverlappingObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Iq(c, a), w) }; T.prototype.__destroy__ = function () { ne(this.a) }; function G() { throw "cannot construct a btManifoldPoint, no constructor in IDL"; } G.prototype = Object.create(p.prototype);
    G.prototype.constructor = G; G.prototype.b = G; G.c = {}; b.btManifoldPoint = G; G.prototype.getPositionWorldOnA = function () { return r(Qu(this.a), A) }; G.prototype.getPositionWorldOnB = function () { return r(Cn(this.a), A) }; G.prototype.getAppliedImpulse = function () { return cg(this.a) }; G.prototype.getDistance = function () { return Mu(this.a) }; G.prototype.get_m_localPointA = function () { return r(is(this.a), A) }; G.prototype.set_m_localPointA = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ng(c, a) };
    G.prototype.get_m_localPointB = function () { return r(Bt(this.a), A) }; G.prototype.set_m_localPointB = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); rd(c, a) }; G.prototype.get_m_positionWorldOnB = function () { return r(Fj(this.a), A) }; G.prototype.set_m_positionWorldOnB = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); pd(c, a) }; G.prototype.get_m_positionWorldOnA = function () { return r(Dn(this.a), A) }; G.prototype.set_m_positionWorldOnA = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); zt(c, a) };
    G.prototype.get_m_normalWorldOnB = function () { return r(Js(this.a), A) }; G.prototype.set_m_normalWorldOnB = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ls(c, a) }; G.prototype.__destroy__ = function () { Nm(this.a) };
    function Zw(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = void 0 === d ? Dq(a, c) : void 0 === e ? _emscripten_bind_btPoint2PointConstraint_btPoint2PointConstraint_3(a, c, d) : Bq(a, c, d, e); q(Zw)[this.a] = this } Zw.prototype = Object.create(Xv.prototype); Zw.prototype.constructor = Zw; Zw.prototype.b = Zw; Zw.c = {}; b.btPoint2PointConstraint = Zw; Zw.prototype.setPivotA = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Nq(c, a) };
    Zw.prototype.setPivotB = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lu(c, a) }; Zw.prototype.getPivotInA = function () { return r(Tl(this.a), A) }; Zw.prototype.getPivotInB = function () { return r(Pp(this.a), A) }; Zw.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yg(c, a) }; Zw.prototype.getBreakingImpulseThreshold = function () { return ft(this.a) }; Zw.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ee(c, a) };
    Zw.prototype.get_m_setting = function () { return r(Ad(this.a), ww) }; Zw.prototype.set_m_setting = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Qb(c, a) }; Zw.prototype.__destroy__ = function () { Oe(this.a) }; function $w() { this.a = Aj(); q($w)[this.a] = this } $w.prototype = Object.create(p.prototype); $w.prototype.constructor = $w; $w.prototype.b = $w; $w.c = {}; b.btSoftBodyHelpers = $w;
    $w.prototype.CreateRope = function (a, c, d, e, f) { var g = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); return r(Vi(g, a, c, d, e, f), Q) };
    $w.prototype.CreatePatch = function (a, c, d, e, f, g, h, l, n) { var v = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); l && "object" === typeof l && (l = l.a); n && "object" === typeof n && (n = n.a); return r(Cr(v, a, c, d, e, f, g, h, l, n), Q) };
    $w.prototype.CreatePatchUV = function (a, c, d, e, f, g, h, l, n, v) { var x = this.a; Nv(); a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); g && "object" === typeof g && (g = g.a); h && "object" === typeof h && (h = h.a); l && "object" === typeof l && (l = l.a); n && "object" === typeof n && (n = n.a); "object" == typeof v && (v = Qv(v)); return r(Tn(x, a, c, d, e, f, g, h, l, n, v), Q) };
    $w.prototype.CreateEllipsoid = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); return r(El(f, a, c, d, e), Q) };
    $w.prototype.CreateFromTriMesh = function (a, c, d, e, f) { var g = this.a; Nv(); a && "object" === typeof a && (a = a.a); "object" == typeof c && (c = Qv(c)); if ("object" == typeof d && "object" === typeof d) { var h = Ov(d, m); Pv(d, m, h); d = h } e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); return r(Wn(g, a, c, d, e, f), Q) };
    $w.prototype.CreateFromConvexHull = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); return r(ai(f, a, c, d, e), Q) }; $w.prototype.__destroy__ = function () { xt(this.a) }; function ax(a) { a && "object" === typeof a && (a = a.a); this.a = Rd(a); q(ax)[this.a] = this } ax.prototype = Object.create(u.prototype); ax.prototype.constructor = ax; ax.prototype.b = ax; ax.c = {}; b.btBoxShape = ax;
    ax.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); il(c, a) }; ax.prototype.getMargin = function () { return me(this.a) }; ax.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Yu(c, a) }; ax.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Bo(d, a, c) }; ax.prototype.__destroy__ = function () { un(this.a) };
    function bx(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = De(a, c); q(bx)[this.a] = this } bx.prototype = Object.create(Zv.prototype); bx.prototype.constructor = bx; bx.prototype.b = bx; bx.c = {}; b.btCapsuleShapeX = bx; bx.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Og(c, a) }; bx.prototype.getMargin = function () { return Xb(this.a) }; bx.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ok(c, a) };
    bx.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Cf(d, a, c) }; bx.prototype.__destroy__ = function () { bn(this.a) }; function W(a, c, d, e) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); this.a = sl(a, c, d, e); q(W)[this.a] = this } W.prototype = Object.create(fw.prototype); W.prototype.constructor = W; W.prototype.b = W; W.c = {}; b.btQuaternion = W;
    W.prototype.setValue = function (a, c, d, e) { var f = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); ud(f, a, c, d, e) }; W.prototype.setEulerZYX = function (a, c, d) { var e = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); cp(e, a, c, d) }; W.prototype.setRotation = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); wd(d, a, c) };
    W.prototype.normalize = function () { gk(this.a) }; W.prototype.length2 = function () { return dt(this.a) }; W.prototype.length = W.prototype.length = function () { return gf(this.a) }; W.prototype.dot = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return Rb(c, a) }; W.prototype.normalized = function () { return r(Zl(this.a), W) }; W.prototype.getAxis = function () { return r(fn(this.a), A) }; W.prototype.inverse = function () { return r(Ct(this.a), W) }; W.prototype.getAngle = function () { return Li(this.a) };
    W.prototype.getAngleShortestPath = function () { return Sc(this.a) }; W.prototype.angle = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return nf(c, a) }; W.prototype.angleShortestPath = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return sf(c, a) }; W.prototype.op_add = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Su(c, a), W) }; W.prototype.op_sub = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(us(c, a), W) };
    W.prototype.op_mul = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Mo(c, a), W) }; W.prototype.op_mulq = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(km(c, a), W) }; W.prototype.op_div = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); return r(Lf(c, a), W) }; W.prototype.x = function () { return Hh(this.a) }; W.prototype.y = function () { return xl(this.a) }; W.prototype.z = function () { return Qh(this.a) }; W.prototype.w = function () { return Xk(this.a) };
    W.prototype.setX = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ec(c, a) }; W.prototype.setY = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xs(c, a) }; W.prototype.setZ = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); hc(c, a) }; W.prototype.setW = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); xc(c, a) }; W.prototype.__destroy__ = function () { hk(this.a) }; function cx(a, c) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); this.a = Cu(a, c); q(cx)[this.a] = this }
    cx.prototype = Object.create(Zv.prototype); cx.prototype.constructor = cx; cx.prototype.b = cx; cx.c = {}; b.btCapsuleShapeZ = cx; cx.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ld(c, a) }; cx.prototype.getMargin = function () { return cl(this.a) }; cx.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); yh(c, a) }; cx.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); bq(d, a, c) };
    cx.prototype.__destroy__ = function () { iu(this.a) }; function Wv() { throw "cannot construct a btContactSolverInfo, no constructor in IDL"; } Wv.prototype = Object.create(p.prototype); Wv.prototype.constructor = Wv; Wv.prototype.b = Wv; Wv.c = {}; b.btContactSolverInfo = Wv; Wv.prototype.get_m_splitImpulse = function () { return !!qv(this.a) }; Wv.prototype.set_m_splitImpulse = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ei(c, a) }; Wv.prototype.get_m_splitImpulsePenetrationThreshold = function () { return te(this.a) };
    Wv.prototype.set_m_splitImpulsePenetrationThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Jn(c, a) }; Wv.prototype.get_m_numIterations = function () { return Rs(this.a) }; Wv.prototype.set_m_numIterations = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Ye(c, a) }; Wv.prototype.__destroy__ = function () { wo(this.a) };
    function Y(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = void 0 === e ? Rj(a, c, d) : void 0 === f ? _emscripten_bind_btGeneric6DofSpringConstraint_btGeneric6DofSpringConstraint_4(a, c, d, e) : Yc(a, c, d, e, f); q(Y)[this.a] = this } Y.prototype = Object.create(jw.prototype); Y.prototype.constructor = Y; Y.prototype.b = Y; Y.c = {}; b.btGeneric6DofSpringConstraint = Y;
    Y.prototype.enableSpring = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); zp(d, a, c) }; Y.prototype.setStiffness = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Vl(d, a, c) }; Y.prototype.setDamping = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); ep(d, a, c) }; Y.prototype.setLinearLowerLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); iv(c, a) };
    Y.prototype.setLinearUpperLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Rc(c, a) }; Y.prototype.setAngularLowerLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); mv(c, a) }; Y.prototype.setAngularUpperLimit = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); ol(c, a) }; Y.prototype.enableFeedback = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); em(c, a) }; Y.prototype.getBreakingImpulseThreshold = function () { return xn(this.a) };
    Y.prototype.setBreakingImpulseThreshold = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); wf(c, a) }; Y.prototype.__destroy__ = function () { lk(this.a) }; function dx(a) { a && "object" === typeof a && (a = a.a); this.a = vl(a); q(dx)[this.a] = this } dx.prototype = Object.create(u.prototype); dx.prototype.constructor = dx; dx.prototype.b = dx; dx.c = {}; b.btSphereShape = dx; dx.prototype.setMargin = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Gn(c, a) }; dx.prototype.getMargin = function () { return Hm(this.a) };
    dx.prototype.setLocalScaling = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Es(c, a) }; dx.prototype.calculateLocalInertia = function (a, c) { var d = this.a; a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); Of(d, a, c) }; dx.prototype.__destroy__ = function () { Qc(this.a) };
    function Z(a, c, d, e, f) { a && "object" === typeof a && (a = a.a); c && "object" === typeof c && (c = c.a); d && "object" === typeof d && (d = d.a); e && "object" === typeof e && (e = e.a); f && "object" === typeof f && (f = f.a); this.a = Qs(a, c, d, e, f); q(Z)[this.a] = this } Z.prototype = Object.create(p.prototype); Z.prototype.constructor = Z; Z.prototype.b = Z; Z.c = {}; b.LocalConvexResult = Z; Z.prototype.get_m_hitCollisionObject = function () { return r(aj(this.a), w) };
    Z.prototype.set_m_hitCollisionObject = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); kv(c, a) }; Z.prototype.get_m_localShapeInfo = function () { return r(vr(this.a), xw) }; Z.prototype.set_m_localShapeInfo = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Pj(c, a) }; Z.prototype.get_m_hitNormalLocal = function () { return r(kc(this.a), A) }; Z.prototype.set_m_hitNormalLocal = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Lh(c, a) };
    Z.prototype.get_m_hitPointLocal = function () { return r(Qd(this.a), A) }; Z.prototype.set_m_hitPointLocal = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); Zf(c, a) }; Z.prototype.get_m_hitFraction = function () { return Vk(this.a) }; Z.prototype.set_m_hitFraction = function (a) { var c = this.a; a && "object" === typeof a && (a = a.a); aq(c, a) }; Z.prototype.__destroy__ = function () { Ti(this.a) };
    (function () { function a() { b.PHY_FLOAT = $t(); b.PHY_DOUBLE = jo(); b.PHY_INTEGER = Sn(); b.PHY_SHORT = qs(); b.PHY_FIXEDPOINT88 = Hq(); b.PHY_UCHAR = wc() } b.calledRun ? a() : Ya.unshift(a) })();

    return Ammo;
};
export default Ammo;